name: playtime-master-orchestration.yml
on:
  pull_request:
    branches:
      - 'master'
      - 'feature/*'
      - 'production/*'
  push:
    branches:
      - 'master'

jobs:
  checkLicense:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Check public licenses
        run: ./gradlew checkLicense
  validateOpenapi:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Validate openapi2-appengine.yaml
        uses: mpetrunic/swagger-cli-action@v1.0.0
        with:
          command: "validate **/src/main/resources/openapi2-appengine.yaml"
  tests:
    strategy:
      matrix:
        module:
          - accounting
          - applovin
          - base
          - base-proxy
          - commands
          - game_progress
          - liquibase-runner
          - notifications
          - orchestrator
          - payment
          - playtime_app
          - proxy_service
          - rewarding-service
          - router
    uses: './.github/workflows/playtime-master.yaml'
    name: 'Run tests for ${{ matrix.module }}'
    with:
      module: ${{ matrix.module }}
    secrets: inherit
