name: Playtime Server Pull Request & Master CI
#
on:
  workflow_call:
    inputs:
      module:
        type: string
        description: Module to run tests on

jobs:
  test:
    name: Run Unit Tests
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2
      - name: set up JDK 21
        uses: actions/setup-java@v1
        with:
          java-version: 21
      - name: Cache Gradle packages
        uses: actions/cache@v4
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*') }}
          restore-keys: ${{ runner.os }}-gradle
      - name: Setup node
        uses: actions/setup-node@v1
        with:
          cache: 'npm'
      - name: Install junit-merger
        run: npm install -g junit-report-merger@6.0.3
      - name: Run Tests with Gradle
        run: |
          echo $GOOGLE_APPLICATION_CREDENTIALS_SECRET > $HOME/key.json  
          GOOGLE_APPLICATION_CREDENTIALS=$(echo $HOME)/key.json ./gradlew ${{ inputs.module }}:test
        env:
          GOOGLE_APPLICATION_CREDENTIALS_SECRET: ${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}
      - name: Publish Test Report
        if: always() # needed to also upload on failed tests
        uses: actions/upload-artifact@v4
        with:
          name: 'test-report-${{ inputs.module }}'
          path: '**/build/reports/tests/test/*'
      - name: Merge report
        if: always()
        run: jrm ./combinedReport.xml "**/test-results/test/TEST-*.xml"
      - name: Test Report
        uses: dorny/test-reporter@v1
        if: always()
        with:
          name: ${{ inputs.module }} JUnit Tests            # Name of the check run which will be created
          path: './combinedReport.xml'    # Path to test results
          reporter: java-junit        # Format of test results
          list-suites: 'failed'
          list-tests: 'failed'
          fail-on-error: 'false'
