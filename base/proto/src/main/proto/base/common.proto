syntax = "proto3";

package com.justplayapps.base;

enum AppPlatformProto {
  UNDEFINED = 0;
  ANDROID = 1;
  IOS = 2;
  IOS_WEB = 3;
}

enum UserBonusBalanceTypeProto {
  USER_BONUS_BALANCE_UNSPECIFIED = 0;
  WELCOME_COINS = 1;
  AB_COIN_REWARD_AFTER_X_MINUTES = 2;
  NO_EARNINGS_COINS = 3;
  SHOW_AD_AFTER_CASHOUT_COINS = 4;
  COIN_GOAL = 5;
  COINS_AFTER_CASHOUT = 6;
  CASH_STREAK = 7;
  ITERABLE_EVENT = 8;
  COINS_MILESTONE = 9;
  BONUS_BANK_REWARD = 10;
}

message Experiment {
  string key = 1;
}
message Variation {
  string key = 1;
}

message DecimalValue {
  reserved 1;
  int32 scale = 2;
  bytes value = 3;
  int32 precision = 4;
}

message UserCurrencyEarningsProto {
  com.justplayapps.base.DecimalValue amount_usd = 1;
  string user_currency_code = 2;
  com.justplayapps.base.DecimalValue user_currency_amount = 3;
  com.justplayapps.base.DecimalValue non_boosted_amount_usd = 4;
  com.justplayapps.base.DecimalValue non_boosted_user_currency_amount = 5;
}