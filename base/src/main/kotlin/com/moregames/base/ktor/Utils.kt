package com.moregames.base.ktor

import com.moregames.base.app.BuildVariant
import com.moregames.base.messaging.dto.PushMessageDto
import com.moregames.base.util.BaseLogEnhancer.Companion.PARENT_TRACE_ID
import com.moregames.base.util.BaseLogEnhancer.Companion.TRACE_ID
import com.moregames.base.util.Constants.IOS_WEB_APP_VERSION_HEADER
import com.moregames.base.util.Constants.MARKET_HEADER
import com.moregames.base.util.Constants.VERIFICATION_SESSION_HEADER
import com.moregames.base.util.Constants.WEB_PLATFORM_HEADER
import com.moregames.base.util.Constants.X_DEVICE_KEY_HEADER
import com.moregames.base.util.Constants.X_USER_AGENT_HEADER
import com.moregames.base.util.base64Decoded
import io.ktor.client.*
import io.ktor.client.features.*
import io.ktor.client.request.*
import io.ktor.features.*
import io.ktor.http.*
import io.ktor.routing.*
import io.ktor.util.*
import io.ktor.utils.io.concurrent.*
import io.ktor.utils.io.core.internal.*
import io.opentelemetry.api.GlobalOpenTelemetry
import io.opentelemetry.api.trace.SpanKind
import io.opentelemetry.extension.kotlin.getOpenTelemetryContext
import kotlinx.serialization.json.Json
import kotlinx.serialization.serializer
import org.slf4j.MDC
import kotlin.reflect.KClass
import kotlin.reflect.full.starProjectedType

const val TASK_QUEUE_AUTHORIZATION_HEADER = "X-AppEngine-QueueName"
const val GAE_INSTANCE_HEADER = "X-AppEngine-Default-Version-Hostname"

// TODO After full migration use X-CloudScheduler header or normal auth
fun Route.cronInterceptor(buildVariant: BuildVariant) {
//  if (buildVariant == BuildVariant.PRODUCTION) {
//    intercept(ApplicationCallPipeline.Call) {
//      val isCalledByAppengineCronjob = call.request.headers["X-Appengine-Cron"]?.toBoolean() == true
//      if (!isCalledByAppengineCronjob) {
//        call.respondText("Unauthorized request", ContentType.Text.Plain, HttpStatusCode.Forbidden)
//        return@intercept finish()
//      }
//    }
//  }
}

// TODO Add normal authorization
fun Route.taskCallInterceptor(buildVariant: BuildVariant, queueName: String) {
//  if (buildVariant == BuildVariant.PRODUCTION) {
//    intercept(ApplicationCallPipeline.Call) {
//      //delay-queue-cashout-period-ended-production
//      val headerValue = call.request.headers[TASK_QUEUE_AUTHORIZATION_HEADER] ?: ""
//      if (!headerValue.startsWith(queueName) || !headerValue.endsWith(buildVariant.key)) {
//        call.respondText("Unauthorized request", ContentType.Text.Plain, HttpStatusCode.Forbidden)
//        finish()
//      }
//    }
//  }
}

fun <T : Any> getData(message: PushMessageDto, expectedClass: KClass<T>, json: Json): T {
  message.message.attributes?.get(PARENT_TRACE_ID)?.let {
    MDC.put(TRACE_ID, it)
    MDC.put(PARENT_TRACE_ID, it)
  }
  return expectedClass.javaObjectType.cast(
    json.decodeFromString(
      serializer(expectedClass.starProjectedType),
      message.message.data.base64Decoded().decodeToString()
    )
  )
}

fun cors(buildVariant: BuildVariant? = BuildVariant.PRODUCTION): CORS.Configuration.() -> Unit = {
  header(HttpHeaders.AccessControlAllowOrigin)
  header(HttpHeaders.Authorization)
  method(HttpMethod.Options)
  header(WEB_PLATFORM_HEADER) // header to track client-defined platform
  header(IOS_WEB_APP_VERSION_HEADER) // we need app version, maybe it will be always 1 but still
  header(MARKET_HEADER)
  header(VERIFICATION_SESSION_HEADER)
  header(X_DEVICE_KEY_HEADER)
  header(X_USER_AGENT_HEADER)
  allowCredentials = true
  allowNonSimpleContentTypes = true
  if (buildVariant == BuildVariant.PRODUCTION) {
    host(host = "*.justplayapi.com", schemes = listOf("https"))
    host(host = "justplay.io", schemes = listOf("https"))
  } else {
    anyHost()
  }
  hosts.add("null") // browsers populate Origin header with null when it's redirect.........
}


class HttpClientTracing(private val spanName: String?) {

  class HttpClientConfiguration {
    @OptIn(DangerousInternalIoApi::class)
    private var _spanName: String? by shared("")

    constructor(
      spanName: String? = null,
    ) {
      this.spanName = spanName
    }

    var spanName: String? by ::_spanName

    internal fun build(): HttpClientTracing = HttpClientTracing(spanName)
    override fun equals(other: Any?): Boolean {
      if (this === other) return true
      if (javaClass != other?.javaClass) return false

      other as HttpClientConfiguration

      return spanName == other.spanName
    }

    override fun hashCode(): Int {
      return spanName?.hashCode() ?: 0
    }
  }

  companion object : HttpClientFeature<HttpClientConfiguration, HttpClientTracing> {
    override val key: AttributeKey<HttpClientTracing> = AttributeKey("HttpClientTracing")

    override fun prepare(block: HttpClientConfiguration.() -> Unit): HttpClientTracing {
      return HttpClientConfiguration().also(block).build()
    }

    override fun install(feature: HttpClientTracing, scope: HttpClient) {
      scope.sendPipeline.intercept(HttpSendPipeline.Monitoring) {
        val span = GlobalOpenTelemetry.get()
          .getTracer("ktor-http-client")
          .spanBuilder("http-client-call${if (feature.spanName != null) ": ${feature.spanName}" else ""}")
          .setSpanKind(SpanKind.PRODUCER)
          .setParent(coroutineContext.getOpenTelemetryContext())
          .startSpan()
        val spanScope = span.makeCurrent()

        context.executionContext.invokeOnCompletion { cause ->
          if (cause != null) {
            span.recordException(cause)
          }
          span.end()
          spanScope.close()
        }
      }
    }
  }
}