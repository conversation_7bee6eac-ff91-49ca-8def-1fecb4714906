package com.moregames.base.user.dto

import com.moregames.base.app.OfferWallType
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable
import java.math.BigDecimal

@Serializable
data class UserDefinitionRules(
  val countryCode: String? = null,
  val ip: String? = null,
  val simCountry: String? = null,
  val networkCountry: String? = null,
  val deviceLocale: String? = null,

  val deviceAttestationPassed: Boolean? = null,
  val strongAttestationPassed: Boolean? = null,
  // will create stub with successful facetec verification, so that cashout will
  //     be allowed for this sessionId
  val facetecVerificationSessionId: String? = null,
  val frozenFraudScore: Int? = null,
  val coinsToAdd: Int? = null,
  @Contextual val revenueToAdd: BigDecimal? = null,
  val experiments: Set<ExperimentVariationKey>? = null,
  val skipOnboarding: Boolean? = null,
  val speedUpCashoutPeriodEnd: Boolean? = null,
  val restrictBan: Boolean? = null,
  val isReviewer: Boolean? = null,
  val offerWallType: OfferWallType? = null,
  val useUsualCashoutPeriod: Boolean? = null,
)

@Serializable
data class ExperimentVariationKey(
  val experimentKey: String,
  val variationKey: String
)
