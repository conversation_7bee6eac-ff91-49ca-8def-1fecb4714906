package com.moregames.base.util

const val GIMICA_PREFIX = "com.gimica."
const val FOREVERGREEN_PREFIX = "com.forevergreen."

object ApplicationId {
  // maybe collect all existing in project application_id constants here some day.

  const val TREASURE_MASTER_APP_ID = "com.gimica.treasuremaster"
  const val TREASURE_MASTER_WEBGL_APP_ID = "com.gimica.treasuremaster.webgl"
  const val TREASURE_MASTER_WEBGL_DEMO_APP_ID = "com.gimica.treasuremaster.webgl.demo"
  const val SOLITAIRE_VERSE_APP_ID = "com.gimica.solitaireverse"
  const val SOLITAIRE_VERSE_WEBGL_APP_ID = "com.gimica.solitaireverse.webgl"
  const val SOLITAIRE_VERSE_WEBGL_DEMO_APP_ID = "com.gimica.solitaireverse.webgl.demo"
  const val SOLITAIRE_CLASSIC_APP_ID = "com.gimica.solitaire"
  const val SOLITAIRE_CLASSIC_FOREVERGREEN_APP_ID = "com.forevergreen.solitaire"
  const val SPIDER_SOLITAIRE_APP_ID = "com.gimica.spidersolitaire"
  const val MERGE_BLAST_APP_ID = "com.gimica.mergeblast"
  const val MAD_SMASH_APP_ID = "com.gimica.madsmash"
  const val MAD_SMASH_WEBGL_APP_ID = "com.gimica.madsmash.webgl"
  const val BALL_BOUNCE_APP_ID = "com.gimica.ballbounce"
  const val BALL_BOUNCE_WEBGL_APP_ID = "com.gimica.ballbounce.webgl"
  const val BALL_BOUNCE_ATLANTIS_APP_ID = "com.gimica.atlantis"
  const val SUGAR_RUSH_APP_ID = "com.gimica.sugarmatch"
  const val SUGAR_RUSH_WEBGL_APP_ID = "com.gimica.sugarmatch.webgl"
  const val WOODEN_PUZZLE_APP_ID = "com.gimica.zentiles"
  const val WOODEN_PUZZLE_WEBGL_APP_ID = "com.gimica.zentiles.webgl"
  const val PUZZLE_POP_BLASTER_APP_ID = "com.gimica.puzzlepopblaster"
  const val BUBBLE_POP_APP_ID = "com.gimica.bubblepop"
  const val BUBBLE_POP_WEBGL_APP_ID = "com.gimica.bubblepop.webgl"
  const val FAIRY_TALE_MANSION_APP_ID = "com.gimica.fairytalematch"
  const val EMOJICLICKERS_APP_ID = "com.gimica.emojiclickers"
  const val MIX_BLOX_APP_ID = "com.gimica.mixblox"
  const val HEXA_PUZZLE_FUN_APP_ID = "com.gimica.hexapuzzlefun"
  const val WORD_SEEKER_APP_ID = "com.gimica.wordseeker"
  const val TRIVIA_MADNESS_APP_ID = "com.gimica.triviamadness"
  const val HEX_MATCH_APP_ID = "com.gimica.hexmatch"
  const val CARS_MERGE_APP_ID = "com.gimica.carsmerge"
  const val BLOCK_HOLE_CLASH_APP_ID = "com.gimica.blockholeclash"
  const val IDLE_MERGE_FUN_APP_ID = "com.gimica.idlemergefun"
  const val BLOCK_SLIDER_APP_ID = "com.gimica.blockslider"
  const val MARBLE_MADNESS_APP_ID = "com.gimica.marblemadness"
  const val BRICK_DOKU_APP_ID = "com.gimica.brickdoku"
  const val SUDOKU_APP_ID = "com.gimica.sudoku"
  const val WORD_KITCHEN_APP_ID = "com.gimica.wordkitchen"
  const val WATER_SORTER_APP_ID = "com.gimica.watersorter"
  const val COLOR_LOGIC_APP_ID = "com.gimica.colorlogic"
  const val DICE_LOGIC_APP_ID = "com.gimica.maketen"
  const val CRYSTAL_CRUSH_APP_ID = "com.gimica.crystalcrush"
  const val SPIRAL_DROP_APP_ID = "com.gimica.helixdash"
  const val TILE_MATCH_PRO_APP_ID = "com.gimica.tilematchpro"
  const val TILE_MATCH_PRO_WEBGL_APP_ID = "com.gimica.tilematchpro.webgl"
  const val SPACE_CONNECT_APP_ID = "com.gimica.spaceconnect"
  const val BLOCKBUSTER_APP_ID = "com.gimica.blockbuster"
  const val TANGRAM_APP_ID = "com.gimica.tangram"
  const val PIN_MASTER_APP_ID = "com.pinmaster.screwpuzzle"
  const val BUBBLE_CHIEF_APP_ID = "com.bubblechef.bubbleshooter"
  const val ATLANTIS_BOUNCE_APP_ID = "com.forevergreen.atlantis"
  const val THREADFORM_APP_ID = "com.gimica.threadform"
  const val HEXA_DROP_APP_ID = "com.gimica.hexadrop"
  const val DONT_POP_APP_ID = "com.gimica.dontpop"
  const val ONE_LINE_PATTERN_PUZZLE_APP_ID = "com.gimica.oneline"
  const val AERO_ESCAPE_APP_ID = "com.gimica.aeroescape"
  const val SLAM_DUNK_APP_ID = "com.gimica.slamdunk"
  const val SLICE_PUZZLE_APP_ID = "com.gimica.slicepuzzle"
  const val SIDE_AND_ROLL_APP_ID = "com.gimica.slideandroll"
  const val FLOOD_BLASTIC_APP_ID = "com.gimica.floodblastic"
  const val FOOD_BLAST_APP_ID = "com.forevergreen.foodblast"

}