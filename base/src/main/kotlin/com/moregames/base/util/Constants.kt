package com.moregames.base.util

object Constants {
  const val JUSTPLAY_APPLICATION_ID = "com.justplay.app"
  const val JUSTPLAY_IOS_APPLICATION_ID = "com.gimica.justplay"
  const val IOS_FIREBASE_ACCOUNT = "<EMAIL>"
  const val ANDROID_FIREBASE_ACCOUNT = "<EMAIL>"

  @Suppress("unused") // for possible later use
  const val WEBHOOK_ENTRY_POINT = "webhook.justplayapi.com"

  const val GKE_INTERNAL_URL = "justplay-internal.com"

  const val JP_USER_ID = "JP_USER_ID"

  const val JP_PLATFORM = "JP_PLATFORM"

  const val JP_USER_FOUND_BY = "JP_USER_FOUND_BY"

  val GOOGLE_CLOUD_PROJECT_ID: String = System.getenv("GOOGLE_CLOUD_PROJECT")  // Provided by Google Cloud
    ?: if (System.getenv("NODE_ENV") == null) "justplay-test-local" else throw IllegalStateException("Can't determine google project name")

  val GCLOUD_REGION: String = System.getenv("GCLOUD_REGION")
    ?: if (System.getenv("NODE_ENV") == null) "test-local" else throw IllegalStateException("Can't determine cloud region")

  val FORBIDDEN_COUNTRIES_FOR_TIER3: Set<String> by lazy {
    (System.getenv("TIER3_FORBIDDEN_COUNTRIES") ?: "")
      .uppercase()
      .split(",")
      .filterNot { it.isBlank() }
      .toSet()
  }

  const val AUTHENTICATION_TOKEN_PARAM_NAME = "authenticationToken"

  const val DONATIONS_FILTER_VALUE = "donations"
  const val WITHDRAWALS_FILTER_VALUE = "withdrawals"

  const val SIGNATURE_REQUEST_HEADER = "X-Playtime-Signature"
  const val MARKET_HEADER = "X-Playtime-Market"
  const val WEB_PLATFORM_HEADER = "X-Web-Platform"

  const val FORWARDED_FOR_HEADER = "X-Forwarded-For"
  const val CLIENT_GEO_LOCATION_HEADER = "X-Client-Geo-Location"
  const val COUNTRY_HEADER = "X-AppEngine-Country"
  const val REGION_HEADER = "X-Appengine-Region"
  const val CITY_HEADER = "X-Appengine-City"
  const val IP_HEADER = "X-AppEngine-User-IP"

  const val BACKEND_COUNTRY_HEADER = "X-Backend-Country"
  const val BACKEND_REGION_HEADER = "X-Backend-Region"
  const val BACKEND_CITY_HEADER = "X-Backend-City"
  const val BACKEND_IP_HEADER = "X-Backend-IP"

  const val ANDROID_APP_VERSION_HEADER = "X-Playtime-AppVersion"
  const val IOS_APP_VERSION_HEADER = "X-iOS-AppVersion"
  const val IOS_WEB_APP_VERSION_HEADER = "X-iOS-WEB-AppVersion"
  const val VERIFICATION_SESSION_HEADER = "X-VSession"
  const val X_DEVICE_KEY_HEADER = "X-Device-Key"
  const val X_USER_AGENT_HEADER = "X-User-Agent"

  val UTC_OFFSET_MINUTES: Long by lazy {
    System.getenv("UTC_OFFSET_MINUTES")?.toLong()
      ?: if (System.getenv("NODE_ENV") == null)
        0
      else {
        logger().alert("Can't determine market utc offset minutes")
        0
      }
  }

  const val MOCK_AD_UNIT_ID_VALUE = "1cb72efd7a22989d"
}
