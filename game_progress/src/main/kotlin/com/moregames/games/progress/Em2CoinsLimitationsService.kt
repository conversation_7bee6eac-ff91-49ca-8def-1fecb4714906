package com.moregames.games.progress

import com.google.inject.Inject
import com.moregames.base.util.ApplicationId.ATLANTIS_BOUNCE_APP_ID
import com.moregames.base.util.ApplicationId.BALL_BOUNCE_APP_ID
import com.moregames.base.util.ApplicationId.BALL_BOUNCE_ATLANTIS_APP_ID
import com.moregames.base.util.ApplicationId.BLOCKBUSTER_APP_ID
import com.moregames.base.util.ApplicationId.BLOCK_HOLE_CLASH_APP_ID
import com.moregames.base.util.ApplicationId.BLOCK_SLIDER_APP_ID
import com.moregames.base.util.ApplicationId.BUBBLE_POP_APP_ID
import com.moregames.base.util.ApplicationId.CARS_MERGE_APP_ID
import com.moregames.base.util.ApplicationId.EMOJICLICKERS_APP_ID
import com.moregames.base.util.ApplicationId.FOOD_BLAST_APP_ID
import com.moregames.base.util.ApplicationId.HEXA_PUZZLE_FUN_APP_ID
import com.moregames.base.util.ApplicationId.HEX_MATCH_APP_ID
import com.moregames.base.util.ApplicationId.IDLE_MERGE_FUN_APP_ID
import com.moregames.base.util.ApplicationId.MAD_SMASH_APP_ID
import com.moregames.base.util.ApplicationId.MERGE_BLAST_APP_ID
import com.moregames.base.util.ApplicationId.MIX_BLOX_APP_ID
import com.moregames.base.util.ApplicationId.PUZZLE_POP_BLASTER_APP_ID
import com.moregames.base.util.ApplicationId.SOLITAIRE_CLASSIC_APP_ID
import com.moregames.base.util.ApplicationId.SOLITAIRE_CLASSIC_FOREVERGREEN_APP_ID
import com.moregames.base.util.ApplicationId.SOLITAIRE_VERSE_APP_ID
import com.moregames.base.util.ApplicationId.SPIDER_SOLITAIRE_APP_ID
import com.moregames.base.util.ApplicationId.SUGAR_RUSH_APP_ID
import com.moregames.base.util.ApplicationId.TREASURE_MASTER_APP_ID
import com.moregames.base.util.ApplicationId.TRIVIA_MADNESS_APP_ID
import com.moregames.base.util.ApplicationId.WOODEN_PUZZLE_APP_ID
import com.moregames.base.util.ApplicationId.WORD_SEEKER_APP_ID
import com.moregames.games.progress.UserGameBalancePersistenceService.UserGameCoinsDataEm2
import java.lang.Integer.max

class Em2CoinsLimitationsService @Inject constructor(
  private val gamesApplicationIdService: GamesApplicationIdService
) {

  companion object {
    private val thresholds = mapOf(
      BALL_BOUNCE_APP_ID to FiveMinThreshold(maxCoins = 245, maxCoinsTransactions = 16),
      BLOCK_HOLE_CLASH_APP_ID to FiveMinThreshold(maxCoins = 600, maxCoinsTransactions = 10),
      BLOCK_SLIDER_APP_ID to FiveMinThreshold(maxCoins = 46, maxCoinsTransactions = 14),
      BUBBLE_POP_APP_ID to FiveMinThreshold(maxCoins = 95, maxCoinsTransactions = 10),
      CARS_MERGE_APP_ID to FiveMinThreshold(maxCoins = 25, maxCoinsTransactions = 10),
      EMOJICLICKERS_APP_ID to FiveMinThreshold(maxCoins = 113317, maxCoinsTransactions = 354), //* turned off for em2 participant
      HEXA_PUZZLE_FUN_APP_ID to FiveMinThreshold(maxCoins = 129, maxCoinsTransactions = 12),
      HEX_MATCH_APP_ID to FiveMinThreshold(maxCoins = 285, maxCoinsTransactions = 10),
      IDLE_MERGE_FUN_APP_ID to FiveMinThreshold(maxCoins = 55, maxCoinsTransactions = 24),
      MAD_SMASH_APP_ID to FiveMinThreshold(maxCoins = 482, maxCoinsTransactions = 10),
      MERGE_BLAST_APP_ID to FiveMinThreshold(maxCoins = 2581, maxCoinsTransactions = 43),
      MIX_BLOX_APP_ID to FiveMinThreshold(maxCoins = 67, maxCoinsTransactions = 13),
      PUZZLE_POP_BLASTER_APP_ID to FiveMinThreshold(maxCoins = 2980, maxCoinsTransactions = 10),
      FOOD_BLAST_APP_ID to FiveMinThreshold(maxCoins = 2980, maxCoinsTransactions = 10),
      SOLITAIRE_VERSE_APP_ID to FiveMinThreshold(maxCoins = 658, maxCoinsTransactions = 66),
      SOLITAIRE_CLASSIC_APP_ID to FiveMinThreshold(maxCoins = 658, maxCoinsTransactions = 66),
      SPIDER_SOLITAIRE_APP_ID to FiveMinThreshold(maxCoins = 658, maxCoinsTransactions = 66),
      SOLITAIRE_CLASSIC_FOREVERGREEN_APP_ID to FiveMinThreshold(maxCoins = 658, maxCoinsTransactions = 66),
      SUGAR_RUSH_APP_ID to FiveMinThreshold(maxCoins = 139, maxCoinsTransactions = 10),
      TREASURE_MASTER_APP_ID to FiveMinThreshold(maxCoins = 1242, maxCoinsTransactions = 46),
      TRIVIA_MADNESS_APP_ID to FiveMinThreshold(maxCoins = 662, maxCoinsTransactions = 10),
      WORD_SEEKER_APP_ID to FiveMinThreshold(maxCoins = 2035, maxCoinsTransactions = 11),
      WOODEN_PUZZLE_APP_ID to FiveMinThreshold(maxCoins = 554, maxCoinsTransactions = 45),
      BLOCKBUSTER_APP_ID to FiveMinThreshold(maxCoins = 554, maxCoinsTransactions = 45),
      BALL_BOUNCE_ATLANTIS_APP_ID to FiveMinThreshold(maxCoins = 245, maxCoinsTransactions = 16),
      ATLANTIS_BOUNCE_APP_ID to FiveMinThreshold(maxCoins = 245, maxCoinsTransactions = 16),
    )
  }

  suspend fun applyGameCoinsSpeedLimitations(gameId: Int, calculatedCoinsEarned: Int, existingCoinsData: UserGameCoinsDataEm2): CapCoinsResult {
    val gameThresholds = thresholds[gamesApplicationIdService.getApplicationId(gameId)]
    return if (gameThresholds != null) {
      //We can have a case with games with replace logic, when after re-installing JP app we can receive whole previous score for a game as initial game coins
      //for the very first in game round, it such case we will provide only 1 coin for this round
      val maxCoins =
        if (existingCoinsData.calculatedCoins == 0 && calculatedCoinsEarned > gameThresholds.maxCoins) 1
        else max(gameThresholds.maxCoins - existingCoinsData.fiveMinIntervalCoins, 0)
      val maxTransactionsCount = max(gameThresholds.maxCoinsTransactions - existingCoinsData.fiveMinIntervalCoinsTransactionsCount, 0)
      when {
        maxTransactionsCount <= 0 -> CapCoinsResult(validCoins = 0, coinsBlocked = calculatedCoinsEarned)
        calculatedCoinsEarned >= maxCoins -> CapCoinsResult(validCoins = maxCoins, coinsBlocked = calculatedCoinsEarned - maxCoins)
        else -> CapCoinsResult(validCoins = calculatedCoinsEarned, coinsBlocked = 0)
      }
    } else CapCoinsResult(validCoins = calculatedCoinsEarned, coinsBlocked = 0)
  }

  private data class FiveMinThreshold(
    val maxCoins: Int,
    val maxCoinsTransactions: Int
  )

  data class CapCoinsResult(
    val validCoins: Int,
    val coinsBlocked: Int
  )
}