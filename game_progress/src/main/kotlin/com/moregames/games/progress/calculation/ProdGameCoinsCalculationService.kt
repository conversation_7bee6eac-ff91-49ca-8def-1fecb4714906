package com.moregames.games.progress.calculation

import com.moregames.base.util.ApplicationId.AERO_ESCAPE_APP_ID
import com.moregames.base.util.ApplicationId.ATLANTIS_BOUNCE_APP_ID
import com.moregames.base.util.ApplicationId.BALL_BOUNCE_APP_ID
import com.moregames.base.util.ApplicationId.BALL_BOUNCE_ATLANTIS_APP_ID
import com.moregames.base.util.ApplicationId.BALL_BOUNCE_WEBGL_APP_ID
import com.moregames.base.util.ApplicationId.BLOCKBUSTER_APP_ID
import com.moregames.base.util.ApplicationId.BLOCK_HOLE_CLASH_APP_ID
import com.moregames.base.util.ApplicationId.BLOCK_SLIDER_APP_ID
import com.moregames.base.util.ApplicationId.BRICK_DOKU_APP_ID
import com.moregames.base.util.ApplicationId.BUBBLE_CHIEF_APP_ID
import com.moregames.base.util.ApplicationId.BUBBLE_POP_APP_ID
import com.moregames.base.util.ApplicationId.BUBBLE_POP_WEBGL_APP_ID
import com.moregames.base.util.ApplicationId.CARS_MERGE_APP_ID
import com.moregames.base.util.ApplicationId.COLOR_LOGIC_APP_ID
import com.moregames.base.util.ApplicationId.CRYSTAL_CRUSH_APP_ID
import com.moregames.base.util.ApplicationId.DICE_LOGIC_APP_ID
import com.moregames.base.util.ApplicationId.DONT_POP_APP_ID
import com.moregames.base.util.ApplicationId.EMOJICLICKERS_APP_ID
import com.moregames.base.util.ApplicationId.FAIRY_TALE_MANSION_APP_ID
import com.moregames.base.util.ApplicationId.FOOD_BLAST_APP_ID
import com.moregames.base.util.ApplicationId.HEXA_DROP_APP_ID
import com.moregames.base.util.ApplicationId.HEXA_PUZZLE_FUN_APP_ID
import com.moregames.base.util.ApplicationId.HEX_MATCH_APP_ID
import com.moregames.base.util.ApplicationId.IDLE_MERGE_FUN_APP_ID
import com.moregames.base.util.ApplicationId.MAD_SMASH_APP_ID
import com.moregames.base.util.ApplicationId.MAD_SMASH_WEBGL_APP_ID
import com.moregames.base.util.ApplicationId.MARBLE_MADNESS_APP_ID
import com.moregames.base.util.ApplicationId.MERGE_BLAST_APP_ID
import com.moregames.base.util.ApplicationId.MIX_BLOX_APP_ID
import com.moregames.base.util.ApplicationId.ONE_LINE_PATTERN_PUZZLE_APP_ID
import com.moregames.base.util.ApplicationId.PIN_MASTER_APP_ID
import com.moregames.base.util.ApplicationId.PUZZLE_POP_BLASTER_APP_ID
import com.moregames.base.util.ApplicationId.SIDE_AND_ROLL_APP_ID
import com.moregames.base.util.ApplicationId.SLAM_DUNK_APP_ID
import com.moregames.base.util.ApplicationId.SLICE_PUZZLE_APP_ID
import com.moregames.base.util.ApplicationId.SOLITAIRE_CLASSIC_APP_ID
import com.moregames.base.util.ApplicationId.SOLITAIRE_CLASSIC_FOREVERGREEN_APP_ID
import com.moregames.base.util.ApplicationId.SOLITAIRE_VERSE_APP_ID
import com.moregames.base.util.ApplicationId.SOLITAIRE_VERSE_WEBGL_APP_ID
import com.moregames.base.util.ApplicationId.SOLITAIRE_VERSE_WEBGL_DEMO_APP_ID
import com.moregames.base.util.ApplicationId.SPACE_CONNECT_APP_ID
import com.moregames.base.util.ApplicationId.SPIDER_SOLITAIRE_APP_ID
import com.moregames.base.util.ApplicationId.SPIRAL_DROP_APP_ID
import com.moregames.base.util.ApplicationId.SUDOKU_APP_ID
import com.moregames.base.util.ApplicationId.SUGAR_RUSH_APP_ID
import com.moregames.base.util.ApplicationId.SUGAR_RUSH_WEBGL_APP_ID
import com.moregames.base.util.ApplicationId.TANGRAM_APP_ID
import com.moregames.base.util.ApplicationId.THREADFORM_APP_ID
import com.moregames.base.util.ApplicationId.TILE_MATCH_PRO_APP_ID
import com.moregames.base.util.ApplicationId.TILE_MATCH_PRO_WEBGL_APP_ID
import com.moregames.base.util.ApplicationId.TREASURE_MASTER_APP_ID
import com.moregames.base.util.ApplicationId.TREASURE_MASTER_WEBGL_APP_ID
import com.moregames.base.util.ApplicationId.TREASURE_MASTER_WEBGL_DEMO_APP_ID
import com.moregames.base.util.ApplicationId.TRIVIA_MADNESS_APP_ID
import com.moregames.base.util.ApplicationId.WATER_SORTER_APP_ID
import com.moregames.base.util.ApplicationId.WOODEN_PUZZLE_APP_ID
import com.moregames.base.util.ApplicationId.WOODEN_PUZZLE_WEBGL_APP_ID
import com.moregames.base.util.ApplicationId.WORD_KITCHEN_APP_ID
import com.moregames.base.util.ApplicationId.WORD_SEEKER_APP_ID
import com.moregames.games.progress.CoinCalculationMissingException
import com.moregames.games.progress.GameParameterRequiredException
import com.moregames.games.progress.Method.ADD
import com.moregames.games.progress.Method.REPLACE
import com.moregames.games.progress.UserBalanceUpdate
import com.moregames.games.progress.UserGameProgressEvent
import kotlin.math.max
import kotlin.math.roundToInt

class ProdGameCoinsCalculationService : GameCoinsCalculationService {

  override suspend fun calculateCoins(userGameProgressEvent: UserGameProgressEvent): UserBalanceUpdate {
    return when (userGameProgressEvent.applicationId) {

      // simple add
      HEXA_PUZZLE_FUN_APP_ID -> {
        when (userGameProgressEvent.amount) {
          1 -> UserBalanceUpdate(10, ADD)
          else -> UserBalanceUpdate.noBalanceUpdate
        }
      }

      "com.relaxingbraintraining.wordcup", "com.cocomagic.merger", IDLE_MERGE_FUN_APP_ID, WORD_KITCHEN_APP_ID ->
        UserBalanceUpdate(2, ADD)

      "com.cocomagic.solitaire",
      "com.cocomagic.mergecarsdefense", "com.cocomagic.snake3d", "com.cocomagic.slideblock", CARS_MERGE_APP_ID, BLOCK_SLIDER_APP_ID ->
        UserBalanceUpdate(3, ADD)

      "com.cocomagic.mergeblock", MIX_BLOX_APP_ID ->
        UserBalanceUpdate(5, ADD)

      "com.cocomagic.idlerestaurant" ->
        UserBalanceUpdate(10, ADD)

      "com.relaxingbraintraining.brickmania", BALL_BOUNCE_APP_ID, BALL_BOUNCE_WEBGL_APP_ID, BALL_BOUNCE_ATLANTIS_APP_ID, ATLANTIS_BOUNCE_APP_ID ->
        UserBalanceUpdate(15, ADD)

      // score-based with add
      "com.relaxingbraintraining.ballrush" ->
        UserBalanceUpdate((scoreOrThrow(userGameProgressEvent) / 2.0).roundToInt(), ADD)

      "com.relaxingbraintraining.snakez" ->
        UserBalanceUpdate((scoreOrThrow(userGameProgressEvent) / 10.0).roundToInt(), ADD)

      // score-based with replace
      "com.cocomagic.crossword" ->
        UserBalanceUpdate(scoreOrThrow(userGameProgressEvent) + 1, REPLACE)

      "com.relaxingbraintraining.helix", SPIRAL_DROP_APP_ID ->
        UserBalanceUpdate(scoreOrThrow(userGameProgressEvent) * 3, REPLACE)

      "com.relaxingbraintraining.unblockbar", "com.relaxingbraintraining.pipeout",
      "com.relaxingbraintraining.zombiechallenge",
      "com.relaxingbraintraining.onelineadvanced", "com.relaxingbraintraining.rollthatball",
      "com.relaxingbraintraining.oneline", THREADFORM_APP_ID, ONE_LINE_PATTERN_PUZZLE_APP_ID, SIDE_AND_ROLL_APP_ID ->
        UserBalanceUpdate(scoreOrThrow(userGameProgressEvent) * 5, REPLACE)

      "com.cocomagic.burningman2" ->
        UserBalanceUpdate((scoreOrThrow(userGameProgressEvent) + 1) * 7, REPLACE)

      "com.cocomagic.colorturn" ->
        UserBalanceUpdate(scoreOrThrow(userGameProgressEvent) * 8, REPLACE)

      "com.relaxingbraintraining.mergecandy",
      "com.relaxingbraintraining.cookiejellymatch", "com.cocomagic.sweetjam", SUGAR_RUSH_APP_ID, SUGAR_RUSH_WEBGL_APP_ID,
      "com.relaxingbraintraining.hexapuzzle", HEX_MATCH_APP_ID ->
        UserBalanceUpdate(scoreOrThrow(userGameProgressEvent) * 20, REPLACE)

      "com.relaxingbraintraining.blockshooter" ->
        UserBalanceUpdate(scoreOrThrow(userGameProgressEvent) * 50, REPLACE)

      "com.relaxingbraintraining.pixelpaint", "com.relaxingbraintraining.pixelcolor" ->
        UserBalanceUpdate(scoreOrThrow(userGameProgressEvent) * 50, REPLACE)

      "com.relaxingbraintraining.juicesplash",
      "com.relaxingbraintraining.grindmygears", "com.relaxingbraintraining.raccoonbubbles", CRYSTAL_CRUSH_APP_ID,
      "com.cocomagic.cannonshooter", "com.cocomagic.hole", "com.cocomagic.smashball", MAD_SMASH_APP_ID, MAD_SMASH_WEBGL_APP_ID,
      BUBBLE_POP_APP_ID, BUBBLE_POP_WEBGL_APP_ID ->
        UserBalanceUpdate(scoreOrThrow(userGameProgressEvent) * 10, REPLACE)

      // amount based with replace
      "com.relaxingbraintraining.emojibounce", "com.relaxingbraintraining.idleemojis", EMOJICLICKERS_APP_ID ->
        UserBalanceUpdate(amountOrThrow(userGameProgressEvent) * 45, REPLACE)


      // score combined with amount
      "com.relaxingbraintraining.colorjump" ->
        UserBalanceUpdate(scoreOrThrow(userGameProgressEvent) + amountOrThrow(userGameProgressEvent), ADD)

      "com.relaxingbraintraining.numbermerge",
      MERGE_BLAST_APP_ID ->
        UserBalanceUpdate((scoreOrThrow(userGameProgressEvent) + amountOrThrow(userGameProgressEvent)) * 5, REPLACE)

      // complex logic
      "com.cocomagic.daggers", "com.relaxingbraintraining.knives",
      "com.adp.treasurequest", TREASURE_MASTER_APP_ID, TREASURE_MASTER_WEBGL_APP_ID ->
        tmLikeCoinsCalculation(userGameProgressEvent)

      TREASURE_MASTER_WEBGL_DEMO_APP_ID -> tmLikeCoinsCalculation(userGameProgressEvent, isDemoGame = true)

      "com.relaxingbraintraining.popslice", SLICE_PUZZLE_APP_ID ->
        if (userGameProgressEvent.score.isNotZero())
          UserBalanceUpdate(scoreOrThrow(userGameProgressEvent) * 10, REPLACE)
        else
          UserBalanceUpdate.noBalanceUpdate

      "com.relaxingbraintraining.snakeclash" ->
        if (userGameProgressEvent.amount.isNotZero())
          UserBalanceUpdate(8, ADD)
        else
          UserBalanceUpdate(max(scoreOrThrow(userGameProgressEvent) / 5.0, 1.0).roundToInt(), ADD)

      "com.relaxingbraintraining.logicblocks", "com.relaxingbraintraining.colorpuzzle",
      "com.addictingpuzzlegames.zenpuzzle", "com.cocomagic.blockpuzzle" ->
        getScoreMultiply1point1UpdateBalance(userGameProgressEvent)

      DICE_LOGIC_APP_ID -> makeTenBalanceCalculation(userGameProgressEvent)
      COLOR_LOGIC_APP_ID -> colorLogicCalculation(userGameProgressEvent)

      "com.relaxingbraintraining.zenpuzzle", WOODEN_PUZZLE_APP_ID, WOODEN_PUZZLE_WEBGL_APP_ID, BLOCKBUSTER_APP_ID -> {
        when (userGameProgressEvent.amount) {
          null, 0 -> getScoreMultiply1point1UpdateBalance(userGameProgressEvent)

          1 -> UserBalanceUpdate(10, ADD)
          2 -> UserBalanceUpdate(20, ADD)
          3 -> UserBalanceUpdate(50, ADD)
          4 -> UserBalanceUpdate(100, ADD)
          else -> UserBalanceUpdate.noBalanceUpdate
        }
      }

      BLOCK_HOLE_CLASH_APP_ID -> {
        if (userGameProgressEvent.amount.isNotZero() && userGameProgressEvent.amount == 101) {
          UserBalanceUpdate(1, ADD)
        } else {
          UserBalanceUpdate(scoreOrThrow(userGameProgressEvent) * 10, REPLACE)
        }
      }

      "com.relaxingbraintraining.planes", AERO_ESCAPE_APP_ID ->
        if (userGameProgressEvent.amount.isNotZero())
          UserBalanceUpdate(2, ADD)
        else if (userGameProgressEvent.score.isNotZero())
          UserBalanceUpdate((scoreOrThrow(userGameProgressEvent) / 2.0).roundToInt() + 1, ADD)
        else
          UserBalanceUpdate.noBalanceUpdate

      "com.relaxingbraintraining.mousekeeper", DONT_POP_APP_ID ->
        if (userGameProgressEvent.amount.isNotZero())
          UserBalanceUpdate(2, ADD)
        else if (userGameProgressEvent.score.isNotZero())
          UserBalanceUpdate(3, ADD)
        else
          UserBalanceUpdate.noBalanceUpdate

      "com.relaxingbraintraining.six", "com.relaxingbraintraining.dunk", HEXA_DROP_APP_ID, SLAM_DUNK_APP_ID ->
        if (userGameProgressEvent.amount.isNotZero())
          UserBalanceUpdate(1, ADD)
        else if (userGameProgressEvent.score.isNotZero())
          UserBalanceUpdate((scoreOrThrow(userGameProgressEvent) / 5.0).roundToInt() + 1, ADD)
        else
          UserBalanceUpdate.noBalanceUpdate

      "com.relaxingbraintraining.blocks" ->
        if (userGameProgressEvent.amount.isNotZero() && amountOrThrow(userGameProgressEvent).rem(10) == 0)
          UserBalanceUpdate(1, ADD)
        else if (userGameProgressEvent.score.isNotZero())
          UserBalanceUpdate((scoreOrThrow(userGameProgressEvent) / 5.0).roundToInt(), ADD)
        else
          UserBalanceUpdate.noBalanceUpdate

      "com.relaxingbraintraining.triviamillion",
      TRIVIA_MADNESS_APP_ID ->
        if (userGameProgressEvent.amount.isNotZero()) {
          if (userGameProgressEvent.amount == 500)
            UserBalanceUpdate(5, ADD)
          else
            UserBalanceUpdate((userGameProgressEvent.amount!! / 2000.0).roundToInt() + 10, ADD)
        } else
          UserBalanceUpdate.noBalanceUpdate

      SOLITAIRE_VERSE_APP_ID,
      SOLITAIRE_VERSE_WEBGL_APP_ID,
      SOLITAIRE_CLASSIC_APP_ID,
      SOLITAIRE_CLASSIC_FOREVERGREEN_APP_ID,
      SPIDER_SOLITAIRE_APP_ID ->
        amount52to208(userGameProgressEvent.amount)

      SOLITAIRE_VERSE_WEBGL_DEMO_APP_ID -> amount52to208(userGameProgressEvent.amount, isDemoGame = true)
      "com.relaxingbraintraining.solitairekingdom" ->
        when (userGameProgressEvent.amount) {
          null -> UserBalanceUpdate(3, ADD)
          else -> amount52to208(userGameProgressEvent.amount)
        }

      WORD_SEEKER_APP_ID -> {
        when (userGameProgressEvent.amount) {
          null -> UserBalanceUpdate.noBalanceUpdate
          4 -> UserBalanceUpdate(200, ADD)
          801, 803, 804, 806, 810 -> UserBalanceUpdate(50, ADD)
          802, 805, 807, 809 -> UserBalanceUpdate(100, ADD)
          808 -> UserBalanceUpdate(70, ADD)
          else -> UserBalanceUpdate.noBalanceUpdate
        }
      }

      PUZZLE_POP_BLASTER_APP_ID, FOOD_BLAST_APP_ID -> {
        when (userGameProgressEvent.amount) {
          101 -> UserBalanceUpdate(20, ADD)
          102 -> UserBalanceUpdate(40, ADD)
          103 -> UserBalanceUpdate(60, ADD)
          else -> UserBalanceUpdate.noBalanceUpdate
        }
      }

      MARBLE_MADNESS_APP_ID -> {
        when (userGameProgressEvent.amount) {
          101 -> UserBalanceUpdate(20, ADD)
          102 -> UserBalanceUpdate(50, ADD)
          103 -> UserBalanceUpdate(100, ADD)
          else -> UserBalanceUpdate.noBalanceUpdate
        }
      }

      WATER_SORTER_APP_ID -> {
        when (userGameProgressEvent.amount) {
          101 -> UserBalanceUpdate(50, ADD)
          102 -> UserBalanceUpdate(100, ADD)
          103 -> UserBalanceUpdate(150, ADD)
          104 -> UserBalanceUpdate(200, ADD)
          else -> UserBalanceUpdate.noBalanceUpdate
        }
      }

      "com.relaxingbraintraining.sudokumaster", SUDOKU_APP_ID -> {
        when (userGameProgressEvent.amount) {
          null -> UserBalanceUpdate(scoreOrThrow(userGameProgressEvent) * 20, REPLACE)
          1 -> UserBalanceUpdate(10, ADD)
          2 -> UserBalanceUpdate(25, ADD)
          3 -> UserBalanceUpdate(15, ADD)
          4 -> UserBalanceUpdate(100, ADD)
          5 -> UserBalanceUpdate(30, ADD)
          6 -> UserBalanceUpdate(50, ADD)
          else -> UserBalanceUpdate.noBalanceUpdate
        }
      }

      BRICK_DOKU_APP_ID -> {
        when (userGameProgressEvent.amount) {
          null -> UserBalanceUpdate(scoreOrThrow(userGameProgressEvent) * 20, REPLACE)
          1 -> UserBalanceUpdate(5, ADD)
          2 -> UserBalanceUpdate(12, ADD)
          3 -> UserBalanceUpdate(10, ADD)
          4 -> UserBalanceUpdate(50, ADD)
          5 -> UserBalanceUpdate(20, ADD)
          6 -> UserBalanceUpdate(25, ADD)
          else -> UserBalanceUpdate.noBalanceUpdate
        }
      }

      TILE_MATCH_PRO_APP_ID, TILE_MATCH_PRO_WEBGL_APP_ID -> {
        when (userGameProgressEvent.amount) {
          100 -> UserBalanceUpdate(20, ADD)
          101 -> UserBalanceUpdate(50, ADD)
          102 -> UserBalanceUpdate(125, ADD)
          103 -> UserBalanceUpdate(300, ADD)
          else -> UserBalanceUpdate.noBalanceUpdate
        }
      }

      FAIRY_TALE_MANSION_APP_ID -> {
        when (userGameProgressEvent.amount) {
          201, 202, 301 -> UserBalanceUpdate(20, ADD)
          else -> {
            scoreOrThrow(userGameProgressEvent)
            UserBalanceUpdate(20, ADD)
          }
        }
      }

      SPACE_CONNECT_APP_ID -> {
        when (userGameProgressEvent.amount) {
          101 -> UserBalanceUpdate(50, ADD)
          102 -> UserBalanceUpdate(60, ADD)
          103 -> UserBalanceUpdate(70, ADD)
          104 -> UserBalanceUpdate(100, ADD)
          105 -> UserBalanceUpdate(120, ADD)
          106 -> UserBalanceUpdate(150, ADD)
          107 -> UserBalanceUpdate(200, ADD)
          108 -> UserBalanceUpdate(150, ADD)
          109 -> UserBalanceUpdate(200, ADD)
          else -> UserBalanceUpdate.noBalanceUpdate
        }
      }

      TANGRAM_APP_ID -> {
        when (userGameProgressEvent.amount) {
          101 -> UserBalanceUpdate(50, ADD)
          102 -> UserBalanceUpdate(100, ADD)
          103 -> UserBalanceUpdate(150, ADD)
          104 -> UserBalanceUpdate(200, ADD)
          105 -> UserBalanceUpdate(200, ADD)
          801 -> UserBalanceUpdate(50, ADD)
          else -> UserBalanceUpdate.noBalanceUpdate
        }
      }

      PIN_MASTER_APP_ID -> {
        when (userGameProgressEvent.amount) {
          1 -> UserBalanceUpdate(100, ADD)
          2 -> UserBalanceUpdate(200, ADD)
          else -> UserBalanceUpdate.noBalanceUpdate
        }
      }

      BUBBLE_CHIEF_APP_ID -> {
        when (userGameProgressEvent.amount) {
          101 -> UserBalanceUpdate(50, ADD)
          102 -> UserBalanceUpdate(75, ADD)
          103 -> UserBalanceUpdate(100, ADD)
          201 -> UserBalanceUpdate(120, ADD)
          301 -> UserBalanceUpdate(100, ADD)
          302 -> UserBalanceUpdate(110, ADD)
          303 -> UserBalanceUpdate(120, ADD)
          401 -> UserBalanceUpdate(650, ADD)
          else -> UserBalanceUpdate.noBalanceUpdate
        }
      }

      else -> {
        throw CoinCalculationMissingException(userGameProgressEvent.applicationId)
      }
    }
  }

  private fun colorLogicCalculation(userGameProgressEvent: UserGameProgressEvent): UserBalanceUpdate {
    val userIsAlternatingScoringParticipant = userGameProgressEvent.amount != null // firebase-driven ab testing

    if (userIsAlternatingScoringParticipant) {
      return when (userGameProgressEvent.amount) {
        1 -> UserBalanceUpdate(10, ADD)
        4 -> UserBalanceUpdate(100, ADD)
        8 -> UserBalanceUpdate(10, ADD)
        else -> UserBalanceUpdate.noBalanceUpdate
      }
    }
    return getScoreMultiply1point1UpdateBalance(userGameProgressEvent)
  }

  private fun makeTenBalanceCalculation(userGameProgressEvent: UserGameProgressEvent): UserBalanceUpdate {
    val userIsAlternatingScoringParticipant = userGameProgressEvent.amount != null // firebase-driven ab testing

    if (userIsAlternatingScoringParticipant) {
      return when (userGameProgressEvent.amount) {
        1 -> UserBalanceUpdate(10, ADD)
        2 -> UserBalanceUpdate(30, ADD)
        4 -> UserBalanceUpdate(100, ADD)
        8 -> UserBalanceUpdate(10, ADD)
        else -> UserBalanceUpdate.noBalanceUpdate
      }
    }
    return getScoreMultiply1point1UpdateBalance(userGameProgressEvent)
  }

  private fun getScoreMultiply1point1UpdateBalance(userGameProgressEvent: UserGameProgressEvent) =
    if (userGameProgressEvent.parameters["is_new_record"]?.toBoolean() == true)
      UserBalanceUpdate((scoreOrThrow(userGameProgressEvent) * 1.1).roundToInt(), REPLACE)
    else
      UserBalanceUpdate.noBalanceUpdate

  private fun amount52to208(amount: Int?, isDemoGame: Boolean = false): UserBalanceUpdate =
    when {
      ((amount ?: 0) == 0) -> UserBalanceUpdate.noBalanceUpdate
      amount!! <= 52 -> UserBalanceUpdate(3, ADD, isDemoGame)
      amount == 208 -> UserBalanceUpdate(200, ADD, isDemoGame)
      else -> UserBalanceUpdate.noBalanceUpdate
    }

  private fun tmLikeCoinsCalculation(userGameProgressEvent: UserGameProgressEvent, isDemoGame: Boolean = false) =
    UserBalanceUpdate(
      coins = scoreOrThrow(userGameProgressEvent) * (if (userGameProgressEvent.parameters["is_boss"]?.toBoolean() == true) 6 else 4),
      method = ADD,
      isDemoGame = isDemoGame
    )

  private fun scoreOrThrow(userGameProgressEvent: UserGameProgressEvent) =
    userGameProgressEvent.score ?: throw GameParameterRequiredException(userGameProgressEvent.applicationId, "score")

  private fun amountOrThrow(userGameProgressEvent: UserGameProgressEvent) =
    userGameProgressEvent.amount ?: throw GameParameterRequiredException(userGameProgressEvent.applicationId, "amount")

  private fun Int?.isNotZero(): Boolean {
    return this != null && this != 0
  }
}
