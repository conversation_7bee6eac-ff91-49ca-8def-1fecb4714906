package com.justplayapps.service.orchestrator

import com.google.inject.Inject
import com.justplayapps.service.orchestrator.ApiManager.Companion.PERSONAL_DATA_BASIC_AUTH_CFG
import com.justplayapps.service.orchestrator.ApiManager.Companion.REDIRECT_BASIC_AUTH_CFG
import com.justplayapps.service.orchestrator.ApiManager.Companion.REDIRECT_BASIC_AUTH_USERNAME
import com.justplayapps.service.orchestrator.util.OrchestratorSecrets
import com.justplayapps.service.orchestrator.util.getDefaultJsonConverter
import com.moregames.base.app.BuildVariant
import com.moregames.base.exceptions.ParameterRequiredException
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.ktor.cors
import com.moregames.base.secret.SecretService
import com.moregames.base.util.installLogging
import com.moregames.base.util.logger
import io.ktor.application.*
import io.ktor.auth.*
import io.ktor.features.*
import io.ktor.http.*
import io.ktor.response.*
import io.ktor.routing.*
import io.ktor.serialization.*

class ApiManager @Inject constructor(
  private val application: Application,
  private val webhookController: WebhookController,
  private val redirectController: RedirectController,
  private val liquibaseMigrationController: OrchestratorLiquibaseMigrationController,
  private val secretService: SecretService,
  private val buildVariant: BuildVariant,
  private val featureFlagsFacade: FeatureFlagsFacade,
  private val personalDataController: PersonalDataController,
) {
  companion object {
    const val REDIRECT_BASIC_AUTH_CFG = "playtime-api-redirect-cfg"
    const val PERSONAL_DATA_BASIC_AUTH_CFG = "playtime-api-personal-data-cfg"
    const val REDIRECT_BASIC_AUTH_USERNAME = "playtime"
  }

  suspend fun initApi() = with(application) {
    application.installLogging(buildVariant, featureFlagsFacade)
    install(StatusPages, orchestratorStatusPageConfig())
    install(ContentNegotiation) {
      json(json = getDefaultJsonConverter())
    }
    install(CORS, cors(buildVariant))
    initAuthenticationForRedirectController(secretService.secretValue(OrchestratorSecrets.BASIC_AUTH_PLAYTIME))
    initAuthenticationForPersonalDataController()
    routing {
      lifecycle()
      webhookController()
      redirectController()
      personalDataController()
      liquibaseMigrationController.startRouting(this)
    }
  }

  private fun Routing.lifecycle() {
    get("/_ah/warmup") {
      call.respond(HttpStatusCode.OK)
    }
  }
}

fun Application.initAuthenticationForRedirectController(password: String) {
  authentication {
    basic(REDIRECT_BASIC_AUTH_CFG) {
      realm = "playtime-api-redirect-realm"
      validate { credentials ->
        if (credentials.name == REDIRECT_BASIC_AUTH_USERNAME && credentials.password == password)
          UserIdPrincipal(credentials.name)
        else
          null
      }
    }
  }
}

fun Application.initAuthenticationForPersonalDataController() {
  authentication {
    basic(PERSONAL_DATA_BASIC_AUTH_CFG) {
      realm = "playtime-api-personal-data-realm"
      validate { credentials -> UserIdPrincipal(credentials.name) }
    }
  }
}

fun orchestratorStatusPageConfig(): StatusPages.Configuration.() -> Unit = {
  exception<ParameterRequiredException> { e ->
    logger().debug("Invalid request", e)
    call.respondText(e.message ?: "", ContentType.Text.Plain, HttpStatusCode.BadRequest)
  }
  exception<Throwable> { e ->
    logger().error("Error", e)
    call.respondText(e.message ?: "Unknown server error", ContentType.Text.Plain, HttpStatusCode.InternalServerError)
  }
}
