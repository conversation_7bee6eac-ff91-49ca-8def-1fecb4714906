package com.justplayapps.service.orchestrator

import com.github.blagerweij.sessionlock.MySQLLockService
import com.google.inject.Inject
import com.google.inject.name.Named
import com.moregames.base.app.BuildVariant
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.secret.BaseSecrets
import com.moregames.base.secret.SecretService
import com.moregames.base.util.basicAuth
import com.moregames.base.util.logger
import com.zaxxer.hikari.HikariDataSource
import io.ktor.application.*
import io.ktor.auth.*
import io.ktor.http.*
import io.ktor.response.*
import io.ktor.routing.*
import kotlinx.coroutines.runBlocking
import liquibase.Contexts
import liquibase.Liquibase
import liquibase.database.DatabaseFactory
import liquibase.database.jvm.JdbcConnection
import liquibase.lockservice.LockServiceFactory
import liquibase.resource.ClassLoaderResourceAccessor

class OrchestratorLiquibaseMigrationController @Inject constructor(
  private val buildVariant: BuildVariant,
  @Named("liquibase-data-source")
  private val liquibaseDataSource: HikariDataSource,
  private val secretService: SecretService,
  private val applicationConfig: ApplicationConfig,
) {
  companion object {
    const val LIQUIBASE_BASIC_AUTH_CONFIG_NAME = "liquibase-basic-auth"
    const val LIQUIBASE_BASIC_AUTH_REALM = "liquibase"
    const val LIQUIBASE_BASIC_AUTH_USER_NAME = "liquibase"
  }

  fun startRouting(root: Route) {
    root.route("/liquibase/") {
      application.authentication {
        basicAuth(
          configuration = LIQUIBASE_BASIC_AUTH_CONFIG_NAME,
          realm = LIQUIBASE_BASIC_AUTH_REALM,
          userName = LIQUIBASE_BASIC_AUTH_USER_NAME,
          password = runBlocking { secretService.secretValue(BaseSecrets.BASIC_AUTH_LIQUIBASE) }
        )
      }
      authenticate(LIQUIBASE_BASIC_AUTH_CONFIG_NAME) {
        get("update") {
          liquibaseDataSource.use { migrationDatasource ->
            migrationDatasource.connection.use { connection ->
              val database = DatabaseFactory.getInstance().findCorrectDatabaseImplementation(JdbcConnection(connection))
              database.defaultSchemaName = migrationDatasource.schema
              Liquibase("/database/liquibase-migration.xml", ClassLoaderResourceAccessor(), database).use {
                it.update(Contexts(buildVariant.name, applicationConfig.justplayMarket)) // https://www.liquibase.com/blog/contexts-vs-labels
                if (LockServiceFactory.getInstance().getLockService(it.database) is MySQLLockService) {
                  logger().info("Using com.github.blagerweij.sessionlock.MySQLLockService")
                }
              }
            }
          }
          call.respond(HttpStatusCode.OK)
        }
      }
    }
  }
}
