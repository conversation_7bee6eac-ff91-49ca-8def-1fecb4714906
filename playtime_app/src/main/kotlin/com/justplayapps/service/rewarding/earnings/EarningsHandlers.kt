package com.justplayapps.service.rewarding.earnings

import com.google.inject.Inject
import com.justplayapps.playtime.proto.PlaytimeEvents
import com.justplayapps.service.rewarding.facade.fromProto
import com.moregames.base.bus.MessageHandler

class EarningsHandlers @Inject constructor(
  private val userEarningsService: UserEarningsService,
  private val userEarningsPersistenceService: UserEarningsPersistenceService,
  private val userCurrentCoinsBalanceService: UserCurrentCoinsBalanceService,
) {

  @MessageHandler
  suspend fun handleUserCreatedEvent(effect: PlaytimeEvents.UserCreatedEvent) {
    userEarningsService.createZeroRevenue(effect.userId)
    userCurrentCoinsBalanceService.createZeroBalance(effect.userId)
  }

  @MessageHandler
  suspend fun handleEarningsAdded(effect: PlaytimeEvents.UnpaidUserEarningsAddedEvent) {
    userEarningsService.trackUnpaidUserEarnings(effect.userId, effect.earnings.fromProto())
  }

  @MessageHandler
  suspend fun handleClearEarningsTransactionCommand(message: PlaytimeEvents.ClearEarningsTransactionCommand) {
    userEarningsPersistenceService.clearEarningsTransactionInfo(listOf(message.cashoutTransactionId))
  }
}
