package com.justplayapps.service.rewarding.earnings

import com.google.inject.Inject
import com.google.protobuf.Int32Value
import com.google.protobuf.StringValue
import com.justplayapps.service.rewarding.earnings.proto.*
import com.justplayapps.service.rewarding.earnings.proto.EarningModel.*
import com.justplayapps.service.rewarding.facade.toProto
import com.justplayapps.service.rewarding.revenue.RevenuePersistenceService
import com.moregames.base.util.fromProto
import com.moregames.base.util.toProto
import java.time.LocalDate

class EmApiImpl @Inject constructor(
  private val emExperimentBaseService: EmExperimentBaseService,
  private val stashCoinsService: StashCoinsService,
  private val userEarningsService: UserEarningsService,
  private val userEarningsPersistenceService: UserEarningsPersistenceService,
  private val revenuePersistenceService: RevenuePersistenceService,
  private val userCurrentCoinsBalanceService: UserCurrentCoinsBalanceService,
) : EmApiGrpcKt.EmApiCoroutineImplBase() {

  override suspend fun getInflatingCoinsMultiplier(request: InflatingCoinsMultiplierRequest): InflatingCoinsMultiplierResponse {
    return inflatingCoinsMultiplierResponse {
      this.multiplier = emExperimentBaseService.inflatingCoinsMultiplier(request.userId)
    }
  }

  override suspend fun getRandomCoinsUsdAmountForEm2(request: GetRandomCoinsUsdAmountForEm2Request): GetRandomCoinsUsdAmountForEm2Response {
    return getRandomCoinsUsdAmountForEm2Response {
      emExperimentBaseService.randomCoinsUsdAmountForEm2(request.userId)?.toProto()?.let {
        this.amount = it
      }
    }
  }

  override suspend fun getWelcomeBonusAmount(request: GetWelcomeBonusAmountRequest): GetWelcomeBonusAmountResponse {
    return getWelcomeBonusAmountResponse {
      this.amount = emExperimentBaseService.getWelcomeBonusAmount(request.userId, request.platform.fromProto())
    }
  }

  override suspend fun seizeSomeCoinsFromStash(request: SeizeSomeCoinsFromStashRequest): SeizeSomeCoinsFromStashResponse {
    return seizeSomeCoinsFromStashResponse {
      this.amount = stashCoinsService.seizeSomeCoinsFromStash(request.userId).toProto()
    }
  }

  override suspend fun userEverHadEarnings(request: UserEverHadEarningsRequest): UserEverHadEarningsResponse {
    return userEverHadEarningsResponse {
      this.hasEarnings = userEarningsService.userEverHadEarnings(request.userId)
    }
  }

  override suspend fun getTotalUsdEarningsForUser(request: GetTotalUsdEarningsForUserRequest): GetTotalUsdEarningsForUserResponse {
    return getTotalUsdEarningsForUserResponse {
      this.amount = userEarningsService.getTotalUsdEarningsForUser(request.userId).toProto()
    }
  }

  override suspend fun getRevenueTotals(request: GetRevenueTotalsRequest): GetRevenueTotalsResponse {
    return revenuePersistenceService.getRevenueTotals(request.userId)?.let {
      getRevenueTotalsResponse {
        this.revenue = it.revenue.toProto()
        this.offerwalRevenue = it.offerwallRevenue.toProto()
        this.day0Revenue = it.day0Revenue.toProto()
        this.day2Revenue = it.day2Revenue.toProto()
      }
    } ?: GetRevenueTotalsResponse.getDefaultInstance()
  }

  override suspend fun loadUserEarningsForMetaId(request: LoadUserEarningsForMetaIdRequest): LoadUserEarningsForMetaIdResponse {
    return userEarningsPersistenceService.loadUserEarningsForMetaId(request.metaId)?.let {
      loadUserEarningsForMetaIdResponse {
        this.userId = it.userId
        if (it.metaUserEarningsId != null) this.metaUserEarningsId = Int32Value.of(it.metaUserEarningsId)
        this.calculatedUserEarningsUsd = it.calculatedUserEarningUsd.toProto()
        if (it.cashoutTransactionId != null) this.cashoutTransactionId = StringValue.of(it.cashoutTransactionId)
      }
    } ?: LoadUserEarningsForMetaIdResponse.getDefaultInstance()
  }

  override suspend fun loadUnpaidUserCurrencyEarnings(request: LoadUnpaidUserCurrencyEarningsRequest): LoadUnpaidUserCurrencyEarningsResponse {
    return userEarningsPersistenceService.loadUnpaidUserCurrencyEarnings(request.userId)?.let {
      loadUnpaidUserCurrencyEarningsResponse {
        this.amountUsd = it.amountUsd.toProto()
        this.userCurrencyAmount = it.userCurrencyAmount.toProto()
        this.userCurrencyCode = it.userCurrency.currencyCode
        if (it.nonBoostedAmountUsd != null) this.nonBoostedAmountUsd = it.nonBoostedAmountUsd.toProto()
        if (it.nonBoostedUserCurrencyAmount != null) this.nonBoostedUserCurrencyAmount = it.nonBoostedUserCurrencyAmount.toProto()
      }
    } ?: LoadUnpaidUserCurrencyEarningsResponse.getDefaultInstance()
  }

  override suspend fun noEarningsAfter(request: NoEarningsAfterRequest): NoEarningsAfterResponse {
    return noEarningsAfterResponse {
      this.noEarnings = userEarningsService.noEarningsAfter(request.userId, request.fromDate.fromProto())
    }
  }

  override suspend fun getNonCashedUserEarningsWithOfferwallAmount(request: GetNonCashedUserEarningsWithOfferwallAmountRequest): GetNonCashedUserEarningsWithOfferwallAmountResponse {
    return userEarningsService.getNonCashedUserEarningsWithOfferwallAmount(request.userId).let {
      getNonCashedUserEarningsWithOfferwallAmountResponse {
        this.offerwallRevenue = it.offerwallRevenue.toProto()
        this.totalRevenue = it.totalRevenue.toProto()
      }
    }
  }

  override suspend fun getLatestRevenueTime(request: GetLatestRevenueTimeRequest): GetLatestRevenueTimeResponse {
    return userEarningsService.getLatestRevenueTime(request.userId)?.let {
      getLatestRevenueTimeResponse {
        this.revenueTime = it.toProto()
      }
    } ?: GetLatestRevenueTimeResponse.getDefaultInstance()
  }

  override suspend fun getUnpaidUserEarningsUSD(request: GetUnpaidUserEarningsUSDRequest): GetUnpaidUserEarningsUSDResponse {
    return getUnpaidUserEarningsUSDResponse {
      this.earningsUsd = userEarningsPersistenceService.getUnpaidUserEarningsUSD(request.userId).toProto()
    }
  }

  override suspend fun getApplovinInterRevenue(request: GetApplovinInterRevenueRequest): GetApplovinInterRevenueResponse {
    return getApplovinInterRevenueResponse {
      this.amount = revenuePersistenceService.getApplovinInterRevenue(request.userId, request.after.fromProto()).toProto()
    }
  }

  override suspend fun getRevenueSumForUserForTimeInterval(request: GetRevenueSumForUserForTimeIntervalRequest): GetRevenueSumForUserForTimeIntervalResponse {
    return getRevenueSumForUserForTimeIntervalResponse {
      this.revenue =
        userEarningsPersistenceService.getRevenueSumForUserForTimeInterval(request.userId, request.startTime.fromProto(), request.endTime.fromProto()).toProto()
    }
  }

  override suspend fun getApplovinRevenue5minPeriodsCount(request: GetApplovinRevenue5minPeriodsCountRequest): GetApplovinRevenue5minPeriodsCountResponse {
    return getApplovinRevenue5minPeriodsCountResponse {
      this.periods.putAll(revenuePersistenceService.getApplovinRevenue5minPeriodsCount(request.userId, request.since.fromProto()))
    }
  }

  override suspend fun getLastLowEarningsCashoutPeriodEnd(request: GetLastLowEarningsCashoutPeriodEndRequest): GetLastLowEarningsCashoutPeriodEndResponse {
    return userEarningsPersistenceService.getLastLowEarningsCashoutPeriodEnd(request.userId)?.let {
      getLastLowEarningsCashoutPeriodEndResponse {
        this.periodEnd = it.toProto()
      }
    } ?: GetLastLowEarningsCashoutPeriodEndResponse.getDefaultInstance()
  }

  override suspend fun getUserNonBannerApplovinRevenueTransactionsCountByHours(request: GetUserNonBannerApplovinRevenueTransactionsCountByHoursRequest): GetUserNonBannerApplovinRevenueTransactionsCountByHoursResponse {
    return getUserNonBannerApplovinRevenueTransactionsCountByHoursResponse {
      this.revenueByPeriods.addAll(
        userEarningsPersistenceService.getUserNonBannerApplovinRevenueTransactionsCountByHours(request.userId, request.since.fromProto())
          .map {
            revenueByPeriod {
              this.periodStart = it.periodStart.toProto()
              this.transactionsCount = it.transactionsCount
            }
          }
      )
    }
  }

  override suspend fun get5MinIntervalsWithRevenueByGames(request: Get5MinIntervalsWithRevenueByGamesRequest): Get5MinIntervalsWithRevenueByGamesResponse {
    return get5MinIntervalsWithRevenueByGamesResponse {
      this.periods.putAll(
        userEarningsPersistenceService.get5MinIntervalsWithRevenueByGames(request.userId, request.since.fromProto())
          .map {
            it.key to Intervals.newBuilder().addAllIntervals(it.value.map { i -> i.toProto() }).build()
          }
          .toMap()
      )
    }
  }

  override suspend fun getUserPerGameRevenue(request: GetUserPerGameRevenueRequest): GetUserPerGameRevenueResponse {
    return revenuePersistenceService.getUserPerGameRevenue(request.userId).map {
      userPerGameRevenue {
        this.gameId = it.gameId
        this.revenue = it.revenue.toProto()
      }
    }.let {
      getUserPerGameRevenueResponse {
        gamesRevenue.addAll(it)
      }
    }
  }

  override suspend fun getUserCurrentCoinsBalance(request: GetUserCurrentCoinsBalanceRequest): GetUserCurrentCoinsBalanceResponse {
    return userCurrentCoinsBalanceService.getUserCurrentCoinsBalance(request.userId, request.platform.fromProto()).let {
      getUserCurrentCoinsBalanceResponse {
        coins = it.coins
        gameCoins = it.gameCoins
        goalCoins = it.goalCoins
      }
    }
  }

  override suspend fun getUninflatedGoalCoins(request: GetUninflatedGoalCoinsRequest): GetUninflatedGoalCoinsResponse {
    return getUninflatedGoalCoinsResponse {
      goalCoins = userCurrentCoinsBalanceService.loadUninflatedGoalCoins(request.userId)
    }
  }

  override suspend fun getCashoutRelatedRevenueSum(request: GetCashoutRelatedRevenueSumRequest): GetCashoutRelatedRevenueSumResponse {
    return getCashoutRelatedRevenueSumResponse {
      this.revenue = userEarningsPersistenceService.getCashoutRelatedRevenueSum(request.cashoutTransactionId).toProto()
    }
  }

  override suspend fun getEarningsSumForTransaction(request: GetEarningsSumForTransactionRequest): GetEarningsSumForTransactionResponse {
    return getEarningsSumForTransactionResponse {
      this.earnings = userEarningsPersistenceService.getEarningsSumForTransaction(request.cashoutTransactionId).toProto()
    }
  }

  override suspend fun updateUnpaidUserEarningsForDemand(request: UpdateUnpaidUserEarningsForDemandRequest): UpdateUnpaidUserEarningsForDemandResponse {
    return updateUnpaidUserEarningsForDemandResponse {
      this.updatedRows = userEarningsPersistenceService.updateUnpaidUserEarningsForDemand(request.userId, request.cashoutTransactionId)
    }
  }

  override suspend fun getUserRevenueLast2Days(request: GetUserRevenueLast2DaysRequest): GetUserRevenueLast2DaysResponse {
    return getUserRevenueLast2DaysResponse {
      revenue = revenuePersistenceService.getUserRevenueAfter(request.userId, LocalDate.now().minusDays(1)).toProto()
    }
  }
}