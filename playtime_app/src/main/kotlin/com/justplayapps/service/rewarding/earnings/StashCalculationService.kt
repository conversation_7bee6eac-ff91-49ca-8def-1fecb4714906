package com.justplayapps.service.rewarding.earnings


import com.justplayapps.service.rewarding.earnings.EarningsCalculationsService.Companion.revenueCutFactor
import com.justplayapps.service.rewarding.earnings.dto.Earnings
import com.moregames.base.abtesting.BaseVariation
import com.moregames.base.abtesting.Variations.*
import java.math.BigDecimal
import javax.inject.Inject

class StashCalculationService @Inject constructor(
  private val emExperimentBaseService: EmExperimentBaseService,
) {
  companion object {
    val earningsTargets = mapOf(
      EM2_STASH_TO_EARN_45 to BigDecimal("0.45"),
      EM2_STASH_TO_EARN_45_DQ to BigDecimal("0.45"),
      EM2_STASH_TO_EARN_55 to BigDecimal("0.55"),
      EM2_STASH_TO_EARN_55_DQ to BigDecimal("0.55"),
      EM2_STASH_TO_EARN_65 to BigDecimal("0.65"),
    )
  }

  suspend fun calculateUsdForStash(
    earningsFromRevenue: Earnings,
    realRevenue: BigDecimal,
    realGameRevenue: BigDecimal,
    gamesEm2CappedRevenue: BigDecimal,
    isHighlyTrustedUser: Boolean,
  ): BigDecimal? {
    val userId = earningsFromRevenue.userId
    val em2Variation = emExperimentBaseService.stashVariation(userId)

    return when (em2Variation) {
      EM2_STASH_5PCT, EM2_STASH_5PCT_DQ_FIXED, EM2_STASH_ONE_TIME_FIX, EM2_STASH_ONE_TIME_FIX_DQ -> calculateGameUsdToStash(
        realGameRevenue,
        gamesEm2CappedRevenue
      )

      EM2_STASH_TO_EARN_45, EM2_STASH_TO_EARN_45_DQ, EM2_STASH_TO_EARN_55, EM2_STASH_TO_EARN_55_DQ, EM2_STASH_TO_EARN_65 ->
        calculateEarnTargetUsdToStash(realRevenue, earningsFromRevenue, em2Variation, isHighlyTrustedUser)

      else -> null
    }
      ?.takeIf { it > BigDecimal.ZERO }
  }

  private fun calculateGameUsdToStash(realGameRevenue: BigDecimal, gamesEm2CappedRevenue: BigDecimal): BigDecimal? {
    if (realGameRevenue <= gamesEm2CappedRevenue) return null

    return (realGameRevenue - gamesEm2CappedRevenue)
  }

  private fun calculateEarnTargetUsdToStash(
    realRevenue: BigDecimal,
    earningsFromRevenue: Earnings,
    variation: BaseVariation,
    isHighlyTrustedUser: Boolean
  ): BigDecimal? {
    val target = earningsTargets[variation] ?: return null

    val finalEarning = earningsFromRevenue.earningsSum

    if (!isHighlyTrustedUser) return null

    // "revenue"-meaning value needed for stash, as it goes to em2 coins
    return (realRevenue * target - finalEarning) / revenueCutFactor
  }

}