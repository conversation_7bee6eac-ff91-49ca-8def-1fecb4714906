package com.justplayapps.service.rewarding.earnings

import com.moregames.base.bus.AsyncEffect
import com.moregames.base.bus.EffectHandler
import com.justplayapps.service.rewarding.earnings.UserStashPersistenceService
import java.math.BigDecimal
import javax.inject.Inject

class StashCoinsEffectHandler @Inject constructor(
  private val userStashPersistenceService: UserStashPersistenceService,
) {

  @EffectHandler
  suspend fun handleStashCoins(stashCoins: StashCoinsEffect) {
    val userId = stashCoins.userId
    val coinsForOneDollar = stashCoins.coinsForOneDollar

    stashCoins.usdToStash
      .multiply(coinsForOneDollar)
      .let { userStashPersistenceService.stashCoins(userId, it) }
  }

  data class StashCoinsEffect(
    val userId: String,
    val usdToStash: BigDecimal,
    val coinsForOneDollar: BigDecimal,
  ) : AsyncEffect
}