package com.justplayapps.service.rewarding.earnings


import com.justplayapps.service.rewarding.earnings.table.UserStashFixGivenTable
import com.moregames.base.base.BasePersistenceService
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.insertIgnore
import org.jetbrains.exposed.sql.select
import java.math.BigDecimal
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class StashFixPersistenceService @Inject constructor(database: Database) : BasePersistenceService(database) {

  suspend fun isStashFixGiven(userId: String): Boolean = dbQuery {
    UserStashFixGivenTable
      .slice(UserStashFixGivenTable.userId)
      .select { UserStashFixGivenTable.userId eq userId }
      .count() > 0
  }

  suspend fun trackStashFixGiven(userId: String, fixAmount: BigDecimal) = dbQuery {
    UserStashFixGivenTable
      .insertIgnore {
        it[UserStashFixGivenTable.userId] = userId
        it[UserStashFixGivenTable.fixAmount] = fixAmount
      }
  }
}