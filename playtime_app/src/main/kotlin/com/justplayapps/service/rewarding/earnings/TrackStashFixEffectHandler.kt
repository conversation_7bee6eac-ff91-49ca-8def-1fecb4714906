package com.justplayapps.service.rewarding.earnings


import com.moregames.base.bus.AsyncEffect
import com.moregames.base.bus.EffectHandler
import java.math.BigDecimal
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class TrackStashFixEffectHandler @Inject constructor(
  private val stashFixService: StashFixService,
) {

  @EffectHandler
  suspend fun handleTrackStashFixEffect(trackStashFixEffect: TrackStashFixEffect) = with(trackStashFixEffect) {
    stashFixService.trackStashFixGiven(userId, amount)
  }

  data class TrackStashFixEffect(
    val userId: String,
    val amount: BigDecimal,
  ) : AsyncEffect
}