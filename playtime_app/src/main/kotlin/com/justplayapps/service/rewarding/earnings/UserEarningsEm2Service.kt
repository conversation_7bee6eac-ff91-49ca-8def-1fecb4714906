package com.justplayapps.service.rewarding.earnings

import com.google.inject.Inject
import com.justplayapps.playtime.rewarding.proto.PlaytimeRewarding.GetUserDataResponse
import com.justplayapps.service.rewarding.earnings.dto.Earnings
import com.justplayapps.service.rewarding.earnings.dto.EarningsCalculationResult
import com.justplayapps.service.rewarding.facade.PlaytimeFacade
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.util.fromProto
import com.moregames.base.util.logger
import com.moregames.playtime.earnings.dto.GenericRevenueDto
import com.moregames.playtime.earnings.dto.gamesRevenues
import com.moregames.playtime.earnings.dto.sumNoExtra
import com.moregames.playtime.earnings.dto.sumWithExtra
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Instant
import java.util.*

class UserEarningsEm2Service @Inject constructor(
  private val userEarningsPersistenceService: UserEarningsPersistenceService,
  private val earningsCalculationsService: EarningsCalculationsService,
  private val userEarningsService: UserEarningsService,
  private val emExperimentBaseService: EmExperimentBaseService,
  private val playtimeFacade: PlaytimeFacade,
  private val userCurrentCoinsBalanceService: UserCurrentCoinsBalanceService,
  private val messageBus: MessageBus,
  private val stashCalculationService: StashCalculationService,
  private val stashFixService: StashFixService,
) {

  suspend fun convertRevenueToEarnings(userId: String, userData: GetUserDataResponse, cashOutPeriodStart: Instant): EarningsCalculationResult.Em2 {
    // revenue list needed to mark revenues as processed in the end and send ad events
    val revenueWithExtraList = userEarningsPersistenceService.getUnconvertedRevenue(userId)
    val totalRealRevenue = revenueWithExtraList.sumNoExtra() // need "raw" revenue -> NoExtra
    val gamesRealRevenues = revenueWithExtraList.gamesRevenues()
    val gamesRealRevenueSum = gamesRealRevenues.sumNoExtra() // games do not have extra
    val nonGamesRevenueWithExtra = revenueWithExtraList.sumWithExtra() - gamesRealRevenueSum

    val coinsCurrentBalance = userCurrentCoinsBalanceService.loadCurrentCoinsBalanceEm2(userId)
    val coinsForOneDollar = userData.offerWallCoinsToUsdConversionRatio.fromProto()

    if (BigDecimal.ZERO.compareTo(coinsCurrentBalance.total) == 0) {
      return processAndReturnZeroEarnings(
        userId,
        revenueWithExtraList,
        totalRealRevenue,
        gamesRealRevenueSum,
        coinsCurrentBalance,
        coinsForOneDollar,
        Currency.getInstance(userData.userCurrency)
      )
    }

    val gamesEm2CappedRevenue = playtimeFacade.calculateCappedGameRevenue(
      userId = userId,
      gameCoins = coinsCurrentBalance.gameCoins,
      coinsForOneDollar = coinsForOneDollar,
      gamesRealRevenues = gamesRealRevenues,
    )

    val cappedOfferCoins =
      limitOfferCoins(userId, coinsCurrentBalance.offerCoins, realRevenueCap = nonGamesRevenueWithExtra * coinsForOneDollar, userData = userData)

    val userCoinsSourcedRevenue = (coinsCurrentBalance.bonusCoins + cappedOfferCoins)
      .divide(coinsForOneDollar, 6, RoundingMode.DOWN)
      .plus(gamesEm2CappedRevenue)
    val userOfferwallRevenue = cappedOfferCoins.divide(coinsForOneDollar, 6, RoundingMode.DOWN)

    val userCalculatedEarnings = earningsCalculationsService.convertRevenueToEarnings(userId, userCoinsSourcedRevenue, userOfferwallRevenue, userData)

    val usdToStash = stashCalculationService.calculateUsdForStash(
      userCalculatedEarnings,
      totalRealRevenue,
      gamesRealRevenueSum,
      gamesEm2CappedRevenue,
      userData.isHighlyTrustedUser
    )
    val stashFixAmount = stashFixService.calculateExtraEarnings(userId, usdToStash, totalRealRevenue)
    val firstCpTopUp = userEarningsService.getFirstCpTopUp(userId, userCalculatedEarnings.earningsSum, userData)
    val additionsAmount = (stashFixAmount ?: BigDecimal.ZERO) + firstCpTopUp

    val (nonBoostedEarningsWithAdditionsAmountUsd, earningsWithAdditionsAmountUsd) =
      userEarningsService.boostEarnings(userId, userCalculatedEarnings.earningsSum, cashOutPeriodStart)
        ?.let { (earningsBefore, earningsAfter) -> (earningsBefore + additionsAmount) to (earningsAfter + additionsAmount) }
        ?: (null to (userCalculatedEarnings.earningsSum + additionsAmount))

    val earningsCalculationResult = EarningsCalculationResult.Em2(
      simpleCalculationResult = userEarningsService.giveEarningsToUser(
        userId = userId,
        revenueList = revenueWithExtraList,
        earningsFromRevenue = userCalculatedEarnings,
        earningsWithAdditionsAmountUsd = earningsWithAdditionsAmountUsd,
        nonBoostedEarningsWithAdditionsAmountUsd = nonBoostedEarningsWithAdditionsAmountUsd,
        userCurrency = Currency.getInstance(userData.userCurrency),
      ),
      earnings = userCalculatedEarnings,
      noEarnings = false,
      realRevenue = totalRealRevenue,
      realGameRevenue = gamesRealRevenueSum,
      em2Revenue = userCoinsSourcedRevenue,
      em2CoinsBalance = coinsCurrentBalance,
      coinsForOneDollar = coinsForOneDollar,
      em2GameRevenue = gamesEm2CappedRevenue,
    )

    usdToStash?.let { messageBus.publishAsync(StashCoinsEffectHandler.StashCoinsEffect(userId, it, coinsForOneDollar)) }
    stashFixAmount?.let { messageBus.publishAsync(TrackStashFixEffectHandler.TrackStashFixEffect(userId, it)) }

    return earningsCalculationResult
  }

  suspend fun processAndReturnZeroEarnings(
    userId: String,
    revenueWithExtraList: List<GenericRevenueDto>,
    totalRealRevenue: BigDecimal,
    gamesRealRevenueSum: BigDecimal,
    coinsCurrentBalance: UserCurrentCoinsBalance,
    coinsForOneDollar: BigDecimal,
    userCurrency: Currency,
  ): EarningsCalculationResult.Em2 {
    if (revenueWithExtraList.isNotEmpty())
      userEarningsPersistenceService.bindRevenueWithEarnings(
        userId = userId,
        revenueEventIds = revenueWithExtraList.map(GenericRevenueDto::id),
        calculatedUserEarningUsd = BigDecimal.ZERO,
        nonBoostedUserEarningUsd = null,
        userCurrency = userCurrency,
        userCurrencyEarningAmount = BigDecimal.ZERO,
        nonBoostedUserCurrencyEarningAmount = null,
      )

    return EarningsCalculationResult.Em2(
      simpleCalculationResult = EarningsCalculationResult.Simple(null, BigDecimal.ZERO, BigDecimal.ZERO),
      noEarnings = true,
      realRevenue = totalRealRevenue,
      realGameRevenue = gamesRealRevenueSum,
      em2Revenue = BigDecimal.ZERO,
      em2CoinsBalance = coinsCurrentBalance,
      coinsForOneDollar = coinsForOneDollar,
      em2GameRevenue = BigDecimal.ZERO,
      earnings = Earnings(userId, BigDecimal.ZERO),
    )
  }

  private suspend fun limitOfferCoins(userId: String, offerCoins: BigDecimal, realRevenueCap: BigDecimal, userData: GetUserDataResponse): BigDecimal {
    val userPlatform: AppPlatform = userData.platform.fromProto()

    val welcomeOfferExtra =
      emExperimentBaseService.getWelcomeBonusAmount(userId, userPlatform).toBigDecimal()
        .takeIf { it > BigDecimal.ZERO }
        ?.takeIf { userData.isWelcomeCoinsOfferCompleted }
        ?: BigDecimal.ZERO

    val capping = realRevenueCap + welcomeOfferExtra

    return if (offerCoins - capping > BigDecimal.ONE) {
      logger().warn(
        "Offers revenue capped for userId `$userId`. " +
          "User collected `${offerCoins}` coins and only `$capping` coins will be given " +
          "because capped by `$capping` USD non-games revenue "
      )
      capping
    } else {
      offerCoins
    }
  }
}