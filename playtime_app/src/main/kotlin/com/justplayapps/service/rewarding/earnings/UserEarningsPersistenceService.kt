package com.justplayapps.service.rewarding.earnings

import com.google.inject.Inject
import com.justplayapps.service.rewarding.earnings.dto.UserCurrencyEarnings
import com.justplayapps.service.rewarding.earnings.table.*
import com.moregames.base.abtesting.BaseVariation
import com.moregames.base.abtesting.VARIATION_KEY_DEFAULT
import com.moregames.base.abtesting.Variations
import com.moregames.base.base.BasePersistenceService
import com.moregames.base.messaging.dto.RevenueReceivedEventDto.RevenueSource
import com.moregames.base.table.CurrentGenericRevenueTable
import com.moregames.base.table.GenericRevenueTotalsTable
import com.moregames.base.table.UserApplovinRevenueByPeriodsTable
import com.moregames.base.table.UserTable
import com.moregames.base.user.FakeMetaEarnings
import com.moregames.base.util.*
import com.moregames.playtime.earnings.dto.GenericRevenueDto
import com.moregames.playtime.earnings.dto.UserEarningsQuotasDto
import com.moregames.playtime.user.RevenueByPeriod
import com.moregames.playtime.user.UserPersistenceService.Companion.DAYS_TO_STORE_BY_PERIODS_DATA
import org.jetbrains.exposed.exceptions.ExposedSQLException
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.isNotNull
import org.jetbrains.exposed.sql.SqlExpressionBuilder.isNull
import org.jetbrains.exposed.sql.SqlExpressionBuilder.less
import org.jetbrains.exposed.sql.`java-time`.CustomTimeStampFunction
import org.jetbrains.exposed.sql.`java-time`.dateTimeParam
import org.jetbrains.exposed.sql.transactions.TransactionManager
import java.math.BigDecimal
import java.sql.SQLIntegrityConstraintViolationException
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*
import javax.inject.Singleton

@Singleton
class UserEarningsPersistenceService @Inject constructor(
  database: Database,
  private val timeService: TimeService,
) : BasePersistenceService(database) {

  companion object {
    const val MAX_CHUNK_SIZE = 10000
    const val CURRENT_GENERIC_REVENUE_HOURS = 4L
    const val CURRENT_GENERIC_UNCONVERTED_REVENUE_HOURS = 48L // can be 4 hours as well, but after full rollout
  }

  private val genericRevenueTotalsUpdateQuery = with(GenericRevenueTotalsTable) {
    """INSERT INTO $tableName 
      |(${userId.name},${revenueAmount.name},${offerwallRevenue.name}) 
      |VALUES (?, ?, ?) 
      |ON DUPLICATE KEY UPDATE 
      |${revenueAmount.name} = ${revenueAmount.name} + VALUES(${revenueAmount.name}),
      |${offerwallRevenue.name} = ${offerwallRevenue.name} + VALUES(${offerwallRevenue.name}),
      |${day2Revenue.name} = ${day2Revenue.name} + IF(${createdAt.name} < %s, 0, VALUES(${revenueAmount.name})),
      |${day0Revenue.name} = ${day0Revenue.name} + IF(${createdAt.name} < %s, 0, VALUES(${revenueAmount.name}))
      """.trimToOneLineString()
  }

  suspend fun userEverHadEarnings(userId: String): Boolean = dbQuery {
    UserEarningsTable
      .slice(UserEarningsTable.id)
      .select { UserEarningsTable.userId eq userId }
      .limit(1)
      .firstOrNull()?.let { true } ?: false
  }

  //this method returns real user unpaid earnings without checking the 30$-threshold
  suspend fun loadUnpaidUserCurrencyEarnings(userId: String): UserCurrencyEarnings? =
    dbQuery {
      val nonBoostedThenNormalEarningsUsd = CustomFunction<BigDecimal>(
        functionName = "ifNull",
        _columnType = UserEarningsTable.nonBoostedUserEarningUsd.columnType,
        UserEarningsTable.nonBoostedUserEarningUsd,
        UserEarningsTable.calculatedUserEarningUsd,
      )
      val nonBoostedThenNormalUserCurrencyEarnings = CustomFunction<BigDecimal>(
        functionName = "ifNull",
        _columnType = UserEarningsTable.nonBoostedUserEarningUsd.columnType,
        UserEarningsTable.nonBoostedCurrencyEarningAmount,
        UserEarningsTable.userCurrencyEarningAmount,
      )

      UserEarningsTable
        .slice(
          UserEarningsTable.calculatedUserEarningUsd.sum(),
          UserEarningsTable.userCurrencyCode,
          UserEarningsTable.userCurrencyEarningAmount.sum(),
          UserEarningsTable.nonBoostedUserEarningUsd.sum(),
          nonBoostedThenNormalEarningsUsd.sum(),
          nonBoostedThenNormalUserCurrencyEarnings.sum()
        )
        .select { UserEarningsTable.cashoutTransactionId.isNull() and (UserEarningsTable.userId eq userId) }
        .groupBy(UserEarningsTable.userCurrencyCode)
        .map {
          // if no rows with nonBoosted values - do not try to calculate mixed (w/wo nonBoosted) data
          val (nonBoostedEarningsUsd, nonBoostedUserCurrencyEarnings) =
            if (it[UserEarningsTable.nonBoostedUserEarningUsd.sum()] != null)
              (it[nonBoostedThenNormalEarningsUsd.sum()]) to (it[nonBoostedThenNormalUserCurrencyEarnings.sum()])
            else null to null

          UserCurrencyEarnings(
            amountUsd = it[UserEarningsTable.calculatedUserEarningUsd.sum()] ?: BigDecimal.ZERO,
            userCurrency = Currency.getInstance(it[UserEarningsTable.userCurrencyCode]),
            userCurrencyAmount = it[UserEarningsTable.userCurrencyEarningAmount.sum()] ?: BigDecimal.ZERO,
            nonBoostedAmountUsd = nonBoostedEarningsUsd,
            nonBoostedUserCurrencyAmount = nonBoostedUserCurrencyEarnings
          )
        }
        .firstOrNull()
    }

  suspend fun loadUserEarningsForMetaId(metaId: Int) = dbQuery {
    UserEarningsTable
      .select { UserEarningsTable.metaUserEarningsId eq metaId }
      .toUserEarnings().firstOrNull()
  }

  suspend fun calculateTotalUsdEarningsForUser(userId: String): BigDecimal? =
    dbQuery {
      UserEarningsTable.slice(UserEarningsTable.calculatedUserEarningUsd.sum())
        .select { UserEarningsTable.userId eq userId }
        .firstOrNull()?.get(UserEarningsTable.calculatedUserEarningUsd.sum())
    }

  suspend fun updateUnpaidUserEarningsForDemand(userId: String, transactionId: String) = dbQuery {
    UserEarningsTable
      .update({ UserEarningsTable.userId eq userId and UserEarningsTable.cashoutTransactionId.isNull() }) {
        it[cashoutTransactionId] = transactionId
      }
  }

  suspend fun getEarningsSumForTransaction(transactionId: String): UserCurrencyEarnings =
    dbQuery {
      UserEarningsTable
        .slice(UserEarningsTable.calculatedUserEarningUsd.sum(), UserEarningsTable.userCurrencyCode, UserEarningsTable.userCurrencyEarningAmount.sum())
        .select { UserEarningsTable.cashoutTransactionId eq transactionId }
        .groupBy(UserEarningsTable.userCurrencyCode)
        .map {
          UserCurrencyEarnings(
            amountUsd = it[UserEarningsTable.calculatedUserEarningUsd.sum()] ?: BigDecimal.ZERO,
            userCurrency = Currency.getInstance(it[UserEarningsTable.userCurrencyCode]),
            userCurrencyAmount = it[UserEarningsTable.userCurrencyEarningAmount.sum()] ?: BigDecimal.ZERO
          )
        }
        .first()
    }

  suspend fun clearEarningsTransactionInfo(transactionIds: List<String>) {
    dbQuery {
      UserEarningsTable
        .update({ UserEarningsTable.cashoutTransactionId.inList(transactionIds) }) {
          it[cashoutTransactionId] = null
        }
    }
  }

  /**
   * @return true, if row is added (not filtered by constraints)
   */
  suspend fun addUserRevenue(
    eventId: String,
    userId: String,
    source: RevenueSource,
    timestamp: Instant,
    amount: BigDecimal,
    networkId: Int,
    gameId: Int?,
    amountExtra: BigDecimal?
  ): Boolean = dbQuery {
    try {
      CurrentGenericRevenueTable.insert {
        it[id] = eventId
        it[CurrentGenericRevenueTable.userId] = userId
        it[revenueSource] = source.name
        it[CurrentGenericRevenueTable.timestamp] = timestamp
        it[revenueAmount] = amount
        it[CurrentGenericRevenueTable.networkId] = networkId
        it[CurrentGenericRevenueTable.gameId] = gameId
      }
    } catch (e: ExposedSQLException) {
      if (e.cause is SQLIntegrityConstraintViolationException)
        return@dbQuery false // event duplication occurs - ignore and mute exception
      else
        throw e
    }

    if (amountExtra != null && amountExtra > BigDecimal.ZERO) {
      CurrentGenericRevenueExtraTable.insert {
        it[id] = eventId
        it[revenueAmountExtra] = amountExtra
      }
    }
    true
  }

  suspend fun trackApplovinNonBannerRevenueBy5min(userId: String, gameId: Int, timestamp: Instant, amount: BigDecimal) =
    dbQuery {
      UserApplovinRevenueByPeriodsTable.insertOrAdd(
        UserApplovinRevenueByPeriodsTable.revenue,
        UserApplovinRevenueByPeriodsTable.revenueTransactionsCount
      ) {
        it[UserApplovinRevenueByPeriodsTable.userId] = userId
        it[UserApplovinRevenueByPeriodsTable.gameId] = gameId
        it[periodStart] = timestamp.toBeginningOf5MinInterval()
        it[revenue] = amount
        it[revenueTransactionsCount] = 1
      }
    }

  suspend fun removeBatchOfRevenueTrackedByPeriods(batchSize: Int, userIdStartsWith: String) = dbQuery {
    UserApplovinRevenueByPeriodsTable.deleteWhere(limit = batchSize) {
      (UserApplovinRevenueByPeriodsTable.periodStart less timeService.now().minus(DAYS_TO_STORE_BY_PERIODS_DATA, ChronoUnit.DAYS)) and
        (UserApplovinRevenueByPeriodsTable.userId like ("$userIdStartsWith%"))
    }
  }

  suspend fun getUserNonBannerApplovinRevenueTransactionsCountByHours(userId: String, since: Instant): List<RevenueByPeriod> = dbQuery {
    val hoursString = CustomStringFunction(
      "DATE_FORMAT",
      UserApplovinRevenueByPeriodsTable.periodStart,
      stringParam("%Y-%m-%d %H:00:00")
    )
    val periodTruncatedToHours = CustomTimeStampFunction(
      "TIMESTAMP",
      hoursString
    )
    UserApplovinRevenueByPeriodsTable
      .slice(
        periodTruncatedToHours,
        UserApplovinRevenueByPeriodsTable.revenueTransactionsCount.sum()
      )
      .select {
        (UserApplovinRevenueByPeriodsTable.userId eq userId) and
          (UserApplovinRevenueByPeriodsTable.periodStart greater since)
      }
      .groupBy(periodTruncatedToHours)
      .mapNotNull {
        val periodStart = it.getOrNull(periodTruncatedToHours)
        val transactionsCount = it.getOrNull(UserApplovinRevenueByPeriodsTable.revenueTransactionsCount.sum())
        if (periodStart != null && transactionsCount != null) {
          RevenueByPeriod(periodStart, transactionsCount)
        } else null
      }
  }

  suspend fun get5MinIntervalsWithRevenueByGames(userId: String, since: Instant) = dbQuery {
    UserApplovinRevenueByPeriodsTable
      .slice(
        UserApplovinRevenueByPeriodsTable.gameId,
        UserApplovinRevenueByPeriodsTable.periodStart
      )
      .select {
        (UserApplovinRevenueByPeriodsTable.userId eq userId) and
          (UserApplovinRevenueByPeriodsTable.periodStart greater since)
      }
      .orderBy(UserApplovinRevenueByPeriodsTable.gameId to SortOrder.ASC, UserApplovinRevenueByPeriodsTable.periodStart to SortOrder.ASC)
      .groupBy({ it[UserApplovinRevenueByPeriodsTable.gameId] }) { it[UserApplovinRevenueByPeriodsTable.periodStart] }
  }

  suspend fun trackGenericRevenueTotals(userId: String, source: RevenueSource, timestamp: Instant, amount: BigDecimal) {
    val twoDaysBefore = dateTimeParam(timestamp.minus(2, ChronoUnit.DAYS).localUtcDateTime())
    val dayZero = dateTimeParam(timestamp.minus(1, ChronoUnit.DAYS).localUtcDateTime())
    val ofwAmount = if (source in RevenueSource.offerwallRevenueSources()) amount else BigDecimal.ZERO
    dbQuery {
      genericRevenueTotalsUpdateQuery.format(twoDaysBefore.toString(), dayZero.toString())
        .let { queryText ->
          val statement = TransactionManager.current().connection.prepareStatement(queryText, false)
          statement.fillParameters(
            listOf(
              VarCharColumnType() to userId,
              DecimalColumnType(16, 12) to amount,
              DecimalColumnType(16, 12) to ofwAmount
            )
          )
          statement.executeUpdate()
        }
    }
  }

  suspend fun trackGenericRevenueDailyTotals(userId: String, source: RevenueSource, timestamp: Instant, amount: BigDecimal) = dbQuery {
    GenericRevenueDailyTotalsMk2Table.insertOrAdd(
      GenericRevenueDailyTotalsMk2Table.revenueAmount
    ) {
      it[GenericRevenueDailyTotalsMk2Table.userId] = userId
      it[revenueSource] = source.name
      it[day] = timestamp.localUtcDate()
      it[revenueAmount] = amount
    }
  }

  suspend fun getUnconvertedRevenue(userId: String) =
    dbQuery {
      CurrentGenericRevenueTable
        .leftJoin(CurrentGenericRevenueExtraTable, { id }, { id })
        .select {
          (CurrentGenericRevenueTable.userId eq userId) and
            (CurrentGenericRevenueTable.metaUserEarningsId.isNull())
        }.map {
          GenericRevenueDto(
            id = it[CurrentGenericRevenueTable.id],
            userId = it[CurrentGenericRevenueTable.userId],
            source = RevenueSource.valueOf(it[CurrentGenericRevenueTable.revenueSource]),
            timestamp = it[CurrentGenericRevenueTable.timestamp],
            amount = it[CurrentGenericRevenueTable.revenueAmount],
            amountExtra = it[CurrentGenericRevenueExtraTable.revenueAmountExtra],
            gameId = it[CurrentGenericRevenueTable.gameId],
          )
        }
    }

  suspend fun getRevenueSumForUserForTimeInterval(userId: String, startTime: Instant, endTime: Instant): BigDecimal =
    dbQuery {
      CurrentGenericRevenueTable
        .slice(CurrentGenericRevenueTable.revenueAmount.sum())
        .select {
          (CurrentGenericRevenueTable.userId eq userId) and
            (CurrentGenericRevenueTable.timestamp.greaterEq(startTime) and CurrentGenericRevenueTable.timestamp.less(endTime))
        }
        .firstOrNull()
        ?.let { it[CurrentGenericRevenueTable.revenueAmount.sum()] }
        .orZero()
    }

  suspend fun bindRevenueWithEarnings(
    userId: String,
    revenueEventIds: List<String>,
    calculatedUserEarningUsd: BigDecimal,
    nonBoostedUserEarningUsd: BigDecimal?,
    userCurrency: Currency,
    userCurrencyEarningAmount: BigDecimal,
    nonBoostedUserCurrencyEarningAmount: BigDecimal?,
  ): Int = dbQuery {
    val metaId = MetaUserEarningsTable.insert { } get MetaUserEarningsTable.id

    val changedRowsCount = CurrentGenericRevenueTable.update(
      {
        (CurrentGenericRevenueTable.userId eq userId) and
          (CurrentGenericRevenueTable.id inList revenueEventIds) and
          (CurrentGenericRevenueTable.metaUserEarningsId.isNull())
      }
    ) {
      it[metaUserEarningsId] = metaId
    }
    if (changedRowsCount != revenueEventIds.size) throw IllegalStateException("Can't convert revenue '$revenueEventIds' to earnings. Part of revenue is already converted.")

    // This logic smart and should be chunk of service layer, but I don't want to extract transaction to service layer
    if (calculatedUserEarningUsd > BigDecimal.ZERO) {
      UserEarningsTable.insert {
        it[UserEarningsTable.userId] = userId
        it[metaUserEarningsId] = metaId
        it[UserEarningsTable.calculatedUserEarningUsd] = calculatedUserEarningUsd
        it[UserEarningsTable.nonBoostedUserEarningUsd] = nonBoostedUserEarningUsd
        it[userCurrencyCode] = userCurrency.currencyCode
        it[UserEarningsTable.userCurrencyEarningAmount] = userCurrencyEarningAmount
        it[nonBoostedCurrencyEarningAmount] = nonBoostedUserCurrencyEarningAmount
      }
      UserCoinGoalCurrentCoinsBalanceTable.update({ UserCoinGoalCurrentCoinsBalanceTable.userId eq userId })
      {
        it[coins] = 0
        it[offerCoins] = 0
        it[bonusCoins] = 0
        it[goalCoins] = 0
      }
      UserCoinGoalCurrentCoinsBalanceEm2Table.update({ UserCoinGoalCurrentCoinsBalanceEm2Table.userId eq userId })
      {
        it[this.gameCoins] = BigDecimal.ZERO
        it[this.offerCoins] = BigDecimal.ZERO
        it[this.bonusCoins] = BigDecimal.ZERO
        it[this.goalCoins] = BigDecimal.ZERO
      }
    }

    metaId
  }

  suspend fun removeOldDataFromCurrentGenericRevenue(maxBatchSize: Int, maxChunkSize: Int): Int {
    val threshold = Instant.now().minus(CURRENT_GENERIC_REVENUE_HOURS, ChronoUnit.HOURS)
    var totalRowsDeleted = 0

    val removeCondition = CurrentGenericRevenueTable.timestamp less threshold and
      (CurrentGenericRevenueTable.metaUserEarningsId.isNotNull())

    // a bit of trick here - separate transaction. But safe, as removeCondition proves data is already "consumed"
    // (by earnings calculation)
    // and CurrentGenericRevenueExtra is child to CurrentGenericRevenue and may be cleaned before, even if next
    // transaction will fail
    dbQuery {
      CurrentGenericRevenueExtraTable.deleteWhere(limit = maxChunkSize) {
        notExists(
          CurrentGenericRevenueTable
            .slice(CurrentGenericRevenueTable.id)
            .select { CurrentGenericRevenueTable.id eq CurrentGenericRevenueExtraTable.id }
        )
      }
    }

    do {
      val rowsDeleted = dbQuery {
        CurrentGenericRevenueTable.deleteWhere(limit = maxChunkSize, offset = 0) { removeCondition }
      }
      totalRowsDeleted += rowsDeleted
    } while (rowsDeleted > 0 && totalRowsDeleted < maxBatchSize)

    return totalRowsDeleted
  }

  suspend fun removeUnconvertedOldRevenueForDeletedUsers(): Pair<Int, BigDecimal> {
    val threshold = Instant.now().minus(CURRENT_GENERIC_UNCONVERTED_REVENUE_HOURS, ChronoUnit.HOURS)
    val mainCondition =
      CurrentGenericRevenueTable.timestamp less threshold and
        (CurrentGenericRevenueTable.metaUserEarningsId.isNull()) and
        exists(UserTable.slice(UserTable.id).select { (UserTable.id eq CurrentGenericRevenueTable.userId) and (UserTable.isDeleted eq true) })

    val amountSum = CurrentGenericRevenueTable.revenueAmount.sum()
    val deletedUnconvertedRevenue = dbQuery {
      CurrentGenericRevenueTable
        .slice(amountSum)
        .select { mainCondition }
        .firstOrNull()
        ?.get(amountSum)
    } ?: return 0 to BigDecimal.ZERO

    var totalRowsDeleted = 0
    do {
      val rowsDeleted = dbQuery {
        CurrentGenericRevenueTable
          .deleteWhere(limit = MAX_CHUNK_SIZE, offset = 0) { mainCondition }
      }
      totalRowsDeleted += rowsDeleted
    } while (rowsDeleted > 0)

    return totalRowsDeleted to deletedUnconvertedRevenue
  }

  suspend fun noEarningsAfter(userId: String, fromDate: Instant) = dbQuery {
    UserEarningsTable
      .slice(UserEarningsTable.userId)
      .select { UserEarningsTable.userId eq userId and (UserEarningsTable.createdAt greater fromDate) }
      .count() == 0L
  }

  suspend fun getUserQuotas(userId: String): UserEarningsQuotasDto? = dbQuery {
    UserEarningsQuotasTable
      .slice(UserEarningsQuotasTable.userId, UserEarningsQuotasTable.quotas, UserEarningsQuotasTable.periodEnd)
      .select { UserEarningsQuotasTable.userId eq userId }
      .firstOrNull()
      ?.let {
        UserEarningsQuotasDto(
          it[UserEarningsQuotasTable.userId],
          it[UserEarningsQuotasTable.quotas].split(","),
          it[UserEarningsQuotasTable.periodEnd]
        )
      }
  }

  suspend fun saveUserQuotas(userEarningsQuotas: UserEarningsQuotasDto) = dbQuery {
    UserEarningsQuotasTable.insertOrUpdate(
      UserEarningsQuotasTable.quotas,
      UserEarningsQuotasTable.periodEnd
    ) {
      it[this.userId] = userEarningsQuotas.userId
      it[this.quotas] = userEarningsQuotas.quotas.joinToString(",")
      it[this.periodEnd] = userEarningsQuotas.periodEnd
    }
  }

  suspend fun trackEm2EarningsCalculationData(data: UserCoinsBasedEarningsData) = dbQuery {
    UserCoinsBasedEarningsTable
      .insert {
        it[userId] = data.userId
        it[coinsForOneDollar] = data.coinsForOneDollar
        it[userQuality] = data.userQuality
        it[currentToFirstEcpmRatio] = data.currentToFirstEcpmRatio
        it[periodStart] = data.periodStart
        it[periodEnd] = data.periodEnd
        it[noEarnings] = data.noEarnings
        it[coinGoal] = data.coinGoal
        it[earningsAmount] = data.earningsAmount
        it[em2Coins] = data.em2Coins
        it[em2GameCoins] = data.em2GameCoins
        it[em2BonusCoins] = data.em2BonusCoins
        it[em2OfferCoins] = data.em2OfferCoins
        it[realRevenue] = data.realRevenue
        it[realGameRevenue] = data.realGameRevenue
        it[em2Revenue] = data.em2Revenue
        it[em2Capped] = data.em2Capped
        it[em2GameRevenue] = data.em2GameRevenue
        it[em2GameCapped] = data.em2GameCapped
        it[em2Variation] = if (data.em2Variation is Variations) data.em2Variation.variationKey else VARIATION_KEY_DEFAULT
        it[usedQuota] = data.usedQuota
      }
  }

  suspend fun trackLastLowEarningsCashoutPeriod(userId: String, periodEnd: Instant) = dbQuery {
    UserLastLowEarningsCashoutPeriodTable
      .insertOrUpdate(UserLastLowEarningsCashoutPeriodTable.periodEnd) {
        it[UserLastLowEarningsCashoutPeriodTable.userId] = userId
        it[UserLastLowEarningsCashoutPeriodTable.periodEnd] = periodEnd
      }
  }

  suspend fun getLastLowEarningsCashoutPeriodEnd(userId: String): Instant? =
    dbQuery {
      UserLastLowEarningsCashoutPeriodTable
        .slice(UserLastLowEarningsCashoutPeriodTable.periodEnd)
        .select { UserLastLowEarningsCashoutPeriodTable.userId eq userId }
        .firstOrNull()?.get(UserLastLowEarningsCashoutPeriodTable.periodEnd)
    }

  //this method is only for QA tests purpose
  suspend fun qaAddEarnings(userId: String, amountUsd: BigDecimal, amount: BigDecimal, currencyCode: String) = dbQuery {
    UserEarningsTable.insert {
      it[UserEarningsTable.userId] = userId
      it[metaUserEarningsId] = 1
      it[calculatedUserEarningUsd] = amountUsd
      it[userCurrencyCode] = currencyCode
      it[userCurrencyEarningAmount] = amount
    }
  }

  suspend fun trackEarningsCalculationData(metaId: Int, revenue: BigDecimal, offerwallRevenue: BigDecimal, extraRevenue: BigDecimal) = dbQuery {
    EarningsCalculationDataTable.insert {
      it[metaUserEarningsId] = metaId
      it[this.revenue] = revenue
      it[this.offerwallRevenue] = offerwallRevenue
      it[this.extraRevenue] = extraRevenue
    }
  }

  suspend fun getCashoutRelatedRevenueSum(transactionId: String): BigDecimal = dbQuery {
    EarningsCalculationDataTable
      .join(
        UserEarningsTable,
        JoinType.INNER,
        EarningsCalculationDataTable.metaUserEarningsId,
        UserEarningsTable.metaUserEarningsId
      )
      .slice(EarningsCalculationDataTable.revenue.sum())
      .select { (UserEarningsTable.cashoutTransactionId eq transactionId) }
      .firstOrNull()
      ?.let {
        it[EarningsCalculationDataTable.revenue.sum()] ?: BigDecimal.ZERO
      } ?: BigDecimal.ZERO
  }

  suspend fun loadUnpaidEarningsWithOfferwallAmount(userId: String): UserEarningsWithOfferwallAmount? = dbQuery {
    EarningsCalculationDataTable
      .join(
        UserEarningsTable,
        JoinType.INNER,
        EarningsCalculationDataTable.metaUserEarningsId,
        UserEarningsTable.metaUserEarningsId
      )
      .slice(EarningsCalculationDataTable.revenue.sum(), EarningsCalculationDataTable.offerwallRevenue.sum())
      .select { (UserEarningsTable.userId eq userId) and UserEarningsTable.cashoutTransactionId.isNull() }
      .firstOrNull()
      ?.let {
        val totalRevenue = it[EarningsCalculationDataTable.revenue.sum()] ?: BigDecimal.ZERO
        val offerwallRevenue = it[EarningsCalculationDataTable.offerwallRevenue.sum()] ?: BigDecimal.ZERO
        UserEarningsWithOfferwallAmount(totalRevenue, offerwallRevenue)
      }
  }

  suspend fun trackUnpaidUserEarnings(userId: String, earnings: UserCurrencyEarnings) = dbQuery {
    UserUnpaidEarningsTable
      .insertOrUpdate(
        UserUnpaidEarningsTable.amount,
        UserUnpaidEarningsTable.amountUsd,
        UserUnpaidEarningsTable.currencyCode
      )
      {
        it[UserUnpaidEarningsTable.userId] = userId
        it[amount] = earnings.userCurrencyAmount
        it[amountUsd] = earnings.amountUsd
        it[currencyCode] = earnings.userCurrency.currencyCode
      }
  }

  suspend fun getUnpaidUserEarningsUSD(userId: String): BigDecimal = dbQuery {
    UserUnpaidEarningsTable
      .slice(UserUnpaidEarningsTable.amountUsd)
      .select { UserUnpaidEarningsTable.userId eq userId }
      .firstOrNull()
      ?.get(UserUnpaidEarningsTable.amountUsd)
      ?: BigDecimal.ZERO
  }

  /**
   * @return if reward was actually added. "False" means reward was added earlier
   */
  suspend fun addChallengeEventReward(
    userId: String,
    eventId: String,
    rewardUsd: BigDecimal,
    currency: Currency,
    reward: BigDecimal,
  ): Boolean = dbQuery {
    val doReward = UserChallengeEventRewardedTable
      .insertIgnoreAndGetId {
        it[UserChallengeEventRewardedTable.userId] = userId
        it[UserChallengeEventRewardedTable.eventId] = eventId
      } != null

    if (doReward) {
      UserEarningsTable.insert {
        it[UserEarningsTable.userId] = userId
        it[metaUserEarningsId] = FakeMetaEarnings.FAKE_META_ID_CHALLENGE_REWARD.id
        it[calculatedUserEarningUsd] = rewardUsd
        it[userCurrencyCode] = currency.currencyCode
        it[userCurrencyEarningAmount] = reward
      }
    }

    doReward
  }

  /**
   * @return if reward was actually added. "False" means reward was added earlier
   */
  suspend fun addSpecialChallengeReward(
    userPotId: String,
    userId: String,
    rewardUsd: BigDecimal,
    currency: Currency,
    reward: BigDecimal,
  ): Boolean = dbQuery {
    val doReward = UserSpecialChallengeRewardedTable
      .insertIgnoreAndGetId {
        it[UserSpecialChallengeRewardedTable.userSpecialPotId] = userPotId
      } != null

    if (doReward) {
      UserEarningsTable.insert {
        it[UserEarningsTable.userId] = userId
        it[metaUserEarningsId] = FakeMetaEarnings.FAKE_META_ID_CHALLENGE_REWARD.id
        it[calculatedUserEarningUsd] = rewardUsd
        it[userCurrencyCode] = currency.currencyCode
        it[userCurrencyEarningAmount] = reward
      }
    }

    doReward
  }

  private fun Query.toUserEarnings() = map {
    UserEarnings(
      it[UserEarningsTable.userId],
      it[UserEarningsTable.metaUserEarningsId],
      it[UserEarningsTable.calculatedUserEarningUsd],
      it[UserEarningsTable.cashoutTransactionId]
    )
  }

  data class UserEarningsWithOfferwallAmount(
    val totalRevenue: BigDecimal,
    val offerwallRevenue: BigDecimal,
  )

  data class UserCoinsBasedEarningsData(
    val userId: String,
    val coinsForOneDollar: BigDecimal,
    val periodStart: Instant,
    val periodEnd: Instant,
    val userQuality: BigDecimal?,
    val currentToFirstEcpmRatio: BigDecimal,
    val noEarnings: Boolean,
    val coinGoal: Int,
    val earningsAmount: BigDecimal,
    val em2Coins: BigDecimal,
    val em2GameCoins: BigDecimal,
    val em2BonusCoins: BigDecimal,
    val em2OfferCoins: BigDecimal,
    val realRevenue: BigDecimal,
    val realGameRevenue: BigDecimal,
    val em2Revenue: BigDecimal,
    val em2Capped: BigDecimal,
    val em2GameRevenue: BigDecimal,
    val em2GameCapped: BigDecimal,
    val em2Variation: BaseVariation,
    val usedQuota: BigDecimal,
  )
}
