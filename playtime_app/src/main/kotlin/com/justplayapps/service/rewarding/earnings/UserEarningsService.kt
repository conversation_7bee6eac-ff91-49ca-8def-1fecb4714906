package com.justplayapps.service.rewarding.earnings

import com.google.inject.Inject
import com.justplayapps.playtime.rewarding.proto.PlaytimeRewarding.GetUserDataResponse
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService.Companion.MAX_CHUNK_SIZE
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService.UserCoinsBasedEarningsData
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService.UserEarningsWithOfferwallAmount
import com.justplayapps.service.rewarding.earnings.dto.Earnings
import com.justplayapps.service.rewarding.earnings.dto.EarningsCalculationResult
import com.justplayapps.service.rewarding.earnings.dto.UserCurrencyEarnings
import com.justplayapps.service.rewarding.earnings.proto.createUserRevenueMessagesMessage
import com.justplayapps.service.rewarding.facade.AbTestingFacade
import com.justplayapps.service.rewarding.facade.PlaytimeFacade
import com.justplayapps.service.rewarding.revenue.RevenuePersistenceService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.applovin.APPLOVIN_AD_UNIT_FORMAT_BANNER
import com.moregames.base.bus.MessageBus
import com.moregames.base.messaging.dto.EarningsAddedEventDto
import com.moregames.base.messaging.dto.RevenueReceivedEventDto
import com.moregames.base.messaging.dto.RevenueReceivedEventDto.RevenueSource.Companion.offerwallRevenueSources
import com.moregames.base.util.*
import com.moregames.playtime.earnings.dto.*
import com.moregames.playtime.user.cashout.dto.CashoutPeriodDto.Companion.PERIOD_END_SHIFT_SECONDS
import org.jetbrains.annotations.VisibleForTesting
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Instant
import java.util.*
import javax.inject.Singleton

@Singleton
class UserEarningsService @Inject constructor(
  private val revenuePersistenceService: RevenuePersistenceService,
  private val playtimeFacade: PlaytimeFacade,
  private val earningsCalculationsService: EarningsCalculationsService,
  private val userEarningsPersistenceService: UserEarningsPersistenceService,
  private val userCurrentCoinsBalanceService: UserCurrentCoinsBalanceService,
  private val timeService: TimeService,
  private val abTestingFacade: AbTestingFacade,
  private val messageBus: MessageBus,
) {

  companion object {
    val tenCentsBonus = BigDecimal("0.1")

    val thresholdForGettingTenCentsBonus = BigDecimal("29.90")
    val applovinImportThreshold = BigDecimal("0.8")
    val lowerEarningsThresholdUserCurrency = BigDecimal("0.01")
    const val GOT_REVENUE_EVENT_NEW_USER_DAYS = 7L
    const val FILTER_VALUE_ALL = "all"
    private val CRITICAL_CONVERT_RATIO = BigDecimal(0.75)
    val ANDROID_NEW_USER_CUTOFF_DATE: Instant = Instant.parse("2023-06-01T00:00:00.00Z")
    const val MAX_REVENUE_ROWS_TO_DELETE_PER_CRON_RUN = 500000
  }

  suspend fun saveRevenue(message: RevenueReceivedEventDto): Boolean = with(message) {
    val isNew = userEarningsPersistenceService.addUserRevenue(
      eventId = eventId,
      userId = userId,
      source = source,
      timestamp = timestamp,
      amount = amount,
      networkId = networkId ?: -1,
      gameId = gameId,
      amountExtra = amountExtra
    )
    if (isNew) {
      if (
        source == RevenueReceivedEventDto.RevenueSource.APPLOVIN &&
        !adUnitFormat.isNullOrBlank() &&
        adUnitFormat != APPLOVIN_AD_UNIT_FORMAT_BANNER &&
        gameId != null
      ) {
        userEarningsPersistenceService.trackApplovinNonBannerRevenueBy5min(
          userId = userId,
          gameId = gameId!!,
          timestamp = timestamp,
          amount = amount
        )
      }
      userEarningsPersistenceService.trackGenericRevenueTotals(
        userId = userId,
        source = source,
        timestamp = timestamp,
        amount = amount
      )
      userEarningsPersistenceService.trackGenericRevenueDailyTotals(
        userId = userId,
        source = source,
        timestamp = timestamp,
        amount = amount
      )
    }
    return isNew
  }

  suspend fun convertRevenueToEarnings(userId: String, userData: GetUserDataResponse, cashOutPeriodStart: Instant): EarningsCalculationResult.Simple {
    val revenueWithExtraList = userEarningsPersistenceService.getUnconvertedRevenue(userId)

    if (revenueWithExtraList.isEmpty()) return EarningsCalculationResult.Simple(null, BigDecimal.ZERO, BigDecimal.ZERO)

    // amountExtra is like bonus money and should be controlled
    val userRevenueNoExtra = revenueWithExtraList.sumNoExtra()
    val userRevenue = totalRevenueWithChallengesCut(userId, revenueWithExtraList)
    val userOfferWallRevenue = revenueWithExtraList.offerWallRevenues().sumWithExtra()

    val userEarnings = earningsCalculationsService.convertRevenueToEarnings(userId, userRevenue, userOfferWallRevenue, userData)

    val conversionRatioThreshold = CRITICAL_CONVERT_RATIO * userRevenueNoExtra

    val revenueEarningsAmountUsd =
      (if (userEarnings.earningsSum <= conversionRatioThreshold) userEarnings.earningsSum
      else {
        logger().warn(
          "Earnings exceeded critical conversation ratio and clipped. UserId = '$userId', " +
            "revenue (no extra) = '$userRevenueNoExtra', revenueWithExtra = '$userRevenue', " +
            "earnings = '${userEarnings.earningsSum}'"
        )
        // if the case, could mess with quotas, but ideally this should never happen
        conversionRatioThreshold
      })

    val topUpAmount = getFirstCpTopUp(userId, revenueEarningsAmountUsd, userData)

    val (nonBoostedEarningsWithAdditionsAmountUsd, earningsWithAdditionsAmountUsd) =
      boostEarnings(userId, revenueEarningsAmountUsd, cashOutPeriodStart)
        ?.let { (earningsBefore, earningsAfter) -> (earningsBefore + topUpAmount) to (earningsAfter + topUpAmount) }
        ?: (null to (revenueEarningsAmountUsd + topUpAmount))

    return giveEarningsToUser(
      userId = userId,
      revenueList = revenueWithExtraList,
      earningsFromRevenue = userEarnings,
      earningsWithAdditionsAmountUsd = earningsWithAdditionsAmountUsd,
      nonBoostedEarningsWithAdditionsAmountUsd = nonBoostedEarningsWithAdditionsAmountUsd,
      userCurrency = Currency.getInstance(userData.userCurrency),
    )
  }

  suspend fun getFirstCpTopUp(userId: String, earningsAmountUsd: BigDecimal, userData: GetUserDataResponse): BigDecimal {
    val cashoutPeriodCounter = if (userData.hasCashoutPeriodCounter()) userData.cashoutPeriodCounter.value else null
    val currentBalance = userCurrentCoinsBalanceService.getUserCurrentCoinsBalance(userId, userData.platform.fromProto())
    val isCoinGoalReached = currentBalance.goalCoins >= userData.coinGoal
    val isTopUpAllowed = (cashoutPeriodCounter == 1 &&
      userData.isHighlyTrustedUser &&
      isCoinGoalReached &&
      earningsAmountUsd <= thresholdForGettingTenCentsBonus)

    return when {
      !isTopUpAllowed -> BigDecimal.ZERO
      else -> tenCentsBonus
    }
  }

  suspend fun boostEarnings(userId: String, earnings: BigDecimal, cashoutPeriodStart: Instant): BoostedEarnings? =
    playtimeFacade.getBoostedModeConfig(userId, cashoutPeriodStart)
      ?.let { (earningsIncreaseCoefficient, earningsFakeDecreaseCoefficient) ->
        BoostedEarnings(
          earningsBefore = earnings.divideWithPrecision(earningsFakeDecreaseCoefficient),
          earningsAfter = earnings * earningsIncreaseCoefficient
        )
      }

  private suspend fun bindAndPublishZeroEarnings(
    userId: String,
    revenueList: List<GenericRevenueDto>,
    userCurrency: Currency,
  ) = bindAndPublishEarnings(
    userId = userId,
    revenueList = revenueList,
    userCurrency = userCurrency,
    earningsAmountUsd = BigDecimal.ZERO,
    nonBoostedEarningsAmountUsd = null,
    earningsAmountUserCurrency = BigDecimal.ZERO,
    nonBoostedEarningsAmountUserCurrency = null,
  )

  private suspend fun bindAndPublishEarnings(
    userId: String,
    revenueList: List<GenericRevenueDto>,
    earningsAmountUsd: BigDecimal,
    nonBoostedEarningsAmountUsd: BigDecimal?,
    userCurrency: Currency,
    earningsAmountUserCurrency: BigDecimal,
    nonBoostedEarningsAmountUserCurrency: BigDecimal?,
  ): Int {
    val metaId = userEarningsPersistenceService.bindRevenueWithEarnings(
      userId = userId,
      revenueEventIds = revenueList.map { it.id },
      calculatedUserEarningUsd = earningsAmountUsd,
      nonBoostedUserEarningUsd = nonBoostedEarningsAmountUsd,
      userCurrency = userCurrency,
      userCurrencyEarningAmount = earningsAmountUserCurrency,
      nonBoostedUserCurrencyEarningAmount = nonBoostedEarningsAmountUserCurrency,
    )
    trackEarningsCalculationData(revenueList, metaId)

    if (earningsAmountUsd > BigDecimal.ZERO) {
      messageBus.publish(
        EarningsAddedEventDto(
          metaId = metaId,
          userId = userId,
          amount = earningsAmountUsd,
          createdAt = timeService.now()
        ),
        timeService.now().plusSeconds(PERIOD_END_SHIFT_SECONDS)
      )
    }
    return metaId
  }

  enum class RevenueEvent(val value: String, val eventName: String) {
    EU_SINGLE_PERIOD_REACHED_01("0.1", "eu_single_period_reached_01"),
    EU_SINGLE_PERIOD_REACHED_025("0.25", "eu_single_period_reached_025"),
    SINGLE_PERIOD_REACHED_05("0.5", "single_period_reached_05"),
    SINGLE_PERIOD_REACHED_1("1", "single_period_reached_1"),
    SINGLE_PERIOD_REACHED_2("2", "single_period_reached_2"),
    SINGLE_PERIOD_REACHED_3("3", "single_period_reached_3"),
    SINGLE_PERIOD_REACHED_4("4", "single_period_reached_4");

    companion object {
      fun fromValue(value: String) = entries.first { it.value == value }
    }
  }

  /**
   * @return applied earnings in USD
   */
  suspend fun giveEarningsToUser(
    userId: String,
    revenueList: List<GenericRevenueDto>,
    earningsFromRevenue: Earnings,
    earningsWithAdditionsAmountUsd: BigDecimal,
    nonBoostedEarningsWithAdditionsAmountUsd: BigDecimal?,
    userCurrency: Currency,
  ): EarningsCalculationResult.Simple {
    val earningsAmountUserCurrency = playtimeFacade.getConvertedUsdToUserCurrency(earningsWithAdditionsAmountUsd, userCurrency)
    val nonBoostedEarningsAmountUserCurrency =
      nonBoostedEarningsWithAdditionsAmountUsd?.let { earnings -> playtimeFacade.getConvertedUsdToUserCurrency(earnings, userCurrency) }

    if (earningsAmountUserCurrency.amount < lowerEarningsThresholdUserCurrency) {
      //when cashout happens a fraction of the coins should be accumulated such that
      //the user will not see 0 coins and a small difference between cashouts and earnings
      val metaId = bindAndPublishZeroEarnings(userId, revenueList, userCurrency)
      userEarningsPersistenceService.trackLastLowEarningsCashoutPeriod(userId, timeService.now())

      return EarningsCalculationResult.Simple(metaId, BigDecimal.ZERO, earningsAmountUserCurrency.amountNoRounding)
    }
    earningsFromRevenue.quotasDto?.let { userEarningsPersistenceService.saveUserQuotas(it) }
    val metaId = bindAndPublishEarnings(
      userId, revenueList,
      userCurrency = userCurrency,
      earningsAmountUsd = earningsWithAdditionsAmountUsd,
      nonBoostedEarningsAmountUsd = nonBoostedEarningsWithAdditionsAmountUsd,
      earningsAmountUserCurrency = earningsAmountUserCurrency.amount,
      nonBoostedEarningsAmountUserCurrency = nonBoostedEarningsAmountUserCurrency?.amount,
    )

    val userRevenue = revenueList.sumOf { it.amount }
    val gamesRevenue = revenueList.gamesRevenues().sumNoExtra()
    val userRevenueWithoutEarnings = (userRevenue - earningsWithAdditionsAmountUsd).coerceAtLeast(BigDecimal.ZERO)

    messageBus.publish(
      createUserRevenueMessagesMessage {
        this.userId = userId
        this.userRevenue = userRevenue.toProto()
        this.gamesRevenue = gamesRevenue.toProto()
        this.userRevenueWithoutEarnings = userRevenueWithoutEarnings.toProto()
      }
    )

    return EarningsCalculationResult.Simple(metaId, earningsWithAdditionsAmountUsd, earningsAmountUserCurrency.amountNoRounding)
  }


  private suspend fun trackEarningsCalculationData(revenueList: List<GenericRevenueDto>, metaId: Int) {
    val revenue = revenueList.sumOf { it.amount }
    val offerwallRevenue = revenueList.filter { it.source in offerwallRevenueSources() }.sumOf { it.amount }
    val extraRevenue = revenueList.sumOf { it.amountExtra ?: BigDecimal.ZERO }
    if (revenue > BigDecimal.ZERO || offerwallRevenue > BigDecimal.ZERO) {
      userEarningsPersistenceService.trackEarningsCalculationData(metaId, revenue, offerwallRevenue, extraRevenue)
    }
  }

  //this method returns real all user earnings sum without checking the 30$-threshold for not cashed out part
  suspend fun getTotalUsdEarningsForUser(userId: String): BigDecimal =
    userEarningsPersistenceService.calculateTotalUsdEarningsForUser(userId) ?: BigDecimal.ZERO

  suspend fun getNonCashedUserEarningsWithOfferwallAmount(userId: String): UserEarningsWithOfferwallAmount =
    userEarningsPersistenceService.loadUnpaidEarningsWithOfferwallAmount(userId)
      ?: UserEarningsWithOfferwallAmount(BigDecimal.ZERO, BigDecimal.ZERO)

  suspend fun removeOldDataFromCurrentGenericRevenue(): Int {
    return userEarningsPersistenceService
      .removeOldDataFromCurrentGenericRevenue(
        maxBatchSize = MAX_REVENUE_ROWS_TO_DELETE_PER_CRON_RUN,
        maxChunkSize = MAX_CHUNK_SIZE
      )
  }

  suspend fun removeUnconvertedOldRevenueForDeletedUsers(): Pair<Int, BigDecimal> {
    return userEarningsPersistenceService.removeUnconvertedOldRevenueForDeletedUsers()
  }

  suspend fun noEarningsAfter(userId: String, fromDate: Instant): Boolean =
    userEarningsPersistenceService.noEarningsAfter(userId, fromDate)

  suspend fun checkFilteringAndTrackEm2EarningsCalculationData(
    userId: String,
    periodStart: Instant,
    periodEnd: Instant,
    earningsComputationData: EarningsCalculationResult.Em2,
    coinGoal: Int,
    userFilter: String?,
    userQuality: BigDecimal?,
    currentToFirstEcpmRatio: BigDecimal,
  ) {
    if (userFilter != null && (userFilter == FILTER_VALUE_ALL || userId.startsWith(userFilter))) {
      trackEm2EarningsCalculationData(
        userId = userId,
        periodStart = periodStart,
        periodEnd = periodEnd,
        earningsComputationData = earningsComputationData,
        coinGoal = coinGoal,
        userQuality = userQuality,
        currentToFirstEcpmRatio = currentToFirstEcpmRatio,
      )
    }
  }

  @VisibleForTesting
  suspend fun trackEm2EarningsCalculationData(
    userId: String,
    periodStart: Instant,
    periodEnd: Instant,
    earningsComputationData: EarningsCalculationResult.Em2,
    coinGoal: Int,
    userQuality: BigDecimal?,
    currentToFirstEcpmRatio: BigDecimal,
  ) {
    val earnings = UserCoinsBasedEarningsData(
      userId = userId,
      coinsForOneDollar = earningsComputationData.coinsForOneDollar,
      userQuality = userQuality,
      currentToFirstEcpmRatio = currentToFirstEcpmRatio,
      periodStart = periodStart,
      periodEnd = periodEnd,
      noEarnings = earningsComputationData.noEarnings,
      coinGoal = coinGoal,
      earningsAmount = earningsComputationData.simpleCalculationResult.amount,
      em2Coins = earningsComputationData.em2CoinsBalance.total,
      em2GameCoins = earningsComputationData.em2CoinsBalance.gameCoins,
      em2BonusCoins = earningsComputationData.em2CoinsBalance.bonusCoins,
      em2OfferCoins = earningsComputationData.em2CoinsBalance.offerCoins,
      realRevenue = earningsComputationData.realRevenue,
      realGameRevenue = earningsComputationData.realGameRevenue,
      em2Revenue = earningsComputationData.em2Revenue,
      em2Capped = earningsComputationData.toEm2Capped(),
      em2GameRevenue = earningsComputationData.em2GameRevenue,
      em2GameCapped = earningsComputationData.em2CoinsBalance.gameCoins
        .divide(earningsComputationData.coinsForOneDollar, 6, RoundingMode.DOWN)
        .minus(earningsComputationData.em2GameRevenue),
      usedQuota = earningsComputationData.earnings.usedQuota.sum,
      em2Variation = abTestingFacade.assignedVariation(userId, ClientExperiment.EARNINGS_MODEL_V2),
    )

    userEarningsPersistenceService.trackEm2EarningsCalculationData(earnings)
  }

  suspend fun trackUnpaidUserEarnings(userId: String, earnings: UserCurrencyEarnings) =
    userEarningsPersistenceService.trackUnpaidUserEarnings(userId, earnings)

  suspend fun removeBatchOfRevenueTrackedByPeriods(batchSize: Int, userIdStartsWith: String) =
    userEarningsPersistenceService.removeBatchOfRevenueTrackedByPeriods(batchSize, userIdStartsWith)

  suspend fun userEverHadEarnings(userId: String): Boolean = userEarningsPersistenceService.userEverHadEarnings(userId)

  suspend fun createZeroRevenue(userId: String) {
    revenuePersistenceService.createZeroRevenue(userId)
  }

  suspend fun getLatestRevenueTime(userId: String) =
    revenuePersistenceService.getLatestRevenueTime(userId)

  private suspend fun totalRevenueWithChallengesCut(userId: String, revenueWithExtraList: List<GenericRevenueDto>): BigDecimal {
    val totalRevenueSumAndExtra = revenueWithExtraList.sumWithExtra()
    val gamesRevenues = revenueWithExtraList.gamesRevenues()
    val gamesRevenueWithChallengeCut = playtimeFacade.revenueWithChallengesCut(userId, gamesRevenues)

    return (totalRevenueSumAndExtra - gamesRevenues.sumNoExtra() + gamesRevenueWithChallengeCut)
  }

  data class BoostedEarnings(
    val earningsBefore: BigDecimal,
    val earningsAfter: BigDecimal,
  )
}
