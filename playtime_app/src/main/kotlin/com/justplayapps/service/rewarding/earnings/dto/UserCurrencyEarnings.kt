package com.justplayapps.service.rewarding.earnings.dto

import com.moregames.playtime.util.roundDownToSecondDigit
import java.math.BigDecimal
import java.util.*

data class UserCurrencyEarnings(
  val amountUsd: BigDecimal,
  val userCurrency: Currency,
  val userCurrencyAmount: BigDecimal,
  val nonBoostedAmountUsd: BigDecimal? = null, // filled only for some flows
  val nonBoostedUserCurrencyAmount: BigDecimal? = null, // filled only for some flows
) {
  fun roundDownToSecondDigit() = this.copy(
    amountUsd = amountUsd.roundDownToSecondDigit(),
    nonBoostedAmountUsd = nonBoostedAmountUsd?.roundDownToSecondDigit(),
    userCurrencyAmount = userCurrencyAmount.roundDownToSecondDigit(),
    nonBoostedUserCurrencyAmount = nonBoostedUserCurrencyAmount?.roundDownToSecondDigit()
  )

  companion object {
    fun zeroEarnings(userCurrency: Currency) =
      UserCurrencyEarnings(
        amountUsd = BigDecimal.ZERO,
        nonBoostedAmountUsd = null,
        userCurrency = userCurrency,
        userCurrencyAmount = BigDecimal.ZERO,
        nonBoostedUserCurrencyAmount = null,
      )
  }
}