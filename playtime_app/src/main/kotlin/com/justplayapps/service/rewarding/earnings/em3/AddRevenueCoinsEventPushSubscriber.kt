package com.justplayapps.service.rewarding.earnings.em3


import com.justplayapps.service.rewarding.earnings.em3.dto.AddRevenueCoinsEvent
import com.moregames.base.messaging.push.GenericPushSubscriber
import javax.inject.Inject

class AddRevenueCoinsEventPushSubscriber @Inject constructor(
  private val em3CoinsService: Em3CoinsService,
) : GenericPushSubscriber<AddRevenueCoinsEvent>(AddRevenueCoinsEvent::class) {

  override suspend fun handle(message: AddRevenueCoinsEvent) = with(message) {
    em3CoinsService.giveRevenueBasedCoins(userId, platform, takeRevenueFrom, takeRevenueTo)
  }

  override val url: String
    get() = AddRevenueCoinsEvent.TOPIC_NAME
}