package com.justplayapps.service.rewarding.earnings.em3


import com.justplayapps.service.rewarding.earnings.EmExperimentBaseService
import com.justplayapps.service.rewarding.earnings.UserCurrentCoinsBalancePersistenceService
import com.justplayapps.service.rewarding.earnings.UserCurrentCoinsBalanceService
import com.justplayapps.service.rewarding.earnings.em3.dto.AddRevenueCoinsEvent
import com.justplayapps.service.rewarding.earnings.proto.UserCoinsAddedEventKt.em3UserGameCoins
import com.justplayapps.service.rewarding.earnings.proto.userCoinsAddedEvent
import com.justplayapps.service.rewarding.facade.PlaytimeFacade
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.util.TimeService
import com.moregames.base.util.ex
import com.moregames.base.util.redis.SafeJedisClient
import redis.clients.jedis.params.SetParams
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.time.Duration.Companion.hours

@Singleton
class Em3CoinsService @Inject constructor(
  private val jedisClient: SafeJedisClient,
  private val revenueCoinsPersistenceService: RevenueCoinsPersistenceService,
  private val userCurrentCoinsBalancePersistenceService: UserCurrentCoinsBalancePersistenceService,
  private val userCurrentCoinsBalanceService: UserCurrentCoinsBalanceService,
  private val timeService: TimeService,
  private val playtimeFacade: PlaytimeFacade,
  private val emExperimentBaseService: EmExperimentBaseService,
  private val messageBus: MessageBus,
) {

  companion object {
    private val CHECK_INTERVAL = Duration.ofMinutes(5)
  }

  suspend fun launchRevenueCheckTask(userId: String, platform: AppPlatform) {
    val now = timeService.now()
    val revenueCheckAt = getRevenueCheckAt(userId)

    // already planned
    if (revenueCheckAt != null && revenueCheckAt >= now) return

    val checkIntervalStart = revenueCheckAt ?: now.truncatedTo(ChronoUnit.SECONDS)
    val checkIntervalEnd = now.truncatedTo(ChronoUnit.SECONDS) + CHECK_INTERVAL

    messageBus.publish(
      AddRevenueCoinsEvent(userId, checkIntervalStart, checkIntervalEnd, platform),
      checkIntervalEnd
    )

    setCachedRevenueCheckAt(userId, checkIntervalEnd)
    revenueCoinsPersistenceService.trackRevenueCoinsCheckAt(userId, checkIntervalEnd)
  }

  suspend fun giveRevenueBasedCoins(userId: String, platform: AppPlatform, from: Instant, to: Instant) {
    val inflatingCoinsMultiplier = emExperimentBaseService.inflatingCoinsMultiplier(userId)
    val revenue = revenueCoinsPersistenceService.getApplovinRevenue(userId, from, to)
    val coinsCost = playtimeFacade.getOfferwallCoinsToUsdConversionRatio()

    val coinsToGive = revenue * coinsCost
    val inflatedCoinsEarned = (coinsToGive * inflatingCoinsMultiplier.toBigDecimal()).toInt()

    if (inflatedCoinsEarned <= 0) return

    userCurrentCoinsBalancePersistenceService.addCoinsToCurrentBalance(userId, coinsToGive)
    val coins = userCurrentCoinsBalanceService.getUserCurrentCoinsBalance(userId, platform).coins

    // game coin goals currently will not work for em3: https://app.asana.com/0/1155692811605665/1209727124312606/f
    // COINS_DO_NOT_RESET_ANDROID exp is not supported, but it's ok: https://app.asana.com/0/1155692811605665/1209727124312608/f

    messageBus.publish(
      userCoinsAddedEvent {
        this.userId = userId
        this.coins = coins
        this.em3GameCoinsData = em3UserGameCoins {}
      }
    )
  }

  private suspend fun getRevenueCheckAt(userId: String): Instant? =
    getCachedRevenueCheckAt(userId)
      ?: revenueCoinsPersistenceService.getRevenueCoinsCheckAt(userId)

  private suspend fun getCachedRevenueCheckAt(userId: String): Instant? =
    jedisClient
      .get(cacheKey(userId))
      ?.let { Instant.parse(it) }

  private suspend fun setCachedRevenueCheckAt(userId: String, checkAt: Instant) =
    jedisClient.set(
      cacheKey(userId),
      checkAt.toString(),
      SetParams().ex(1.hours) // means nothing much, value updated in interval (5 minutes, next trigger], but gives profit if "next trigger" reads redis.
    )


  private fun cacheKey(userId: String) = "RevenueCoinsCheckAt:$userId"
}