package com.justplayapps.service.rewarding.earnings.em3.dto

import com.moregames.base.dto.AppPlatform
import com.moregames.base.messaging.dto.MessageDto
import com.moregames.base.util.InstantAsString
import kotlinx.serialization.Serializable

@Serializable
data class AddRevenueCoinsEvent(
  val userId: String,
  val takeRevenueFrom: InstantAsString,
  val takeRevenueTo: InstantAsString,
  val platform: AppPlatform,
) : MessageDto {
  override fun defaultPubsubTopicName(): String = TOPIC_NAME

  companion object {
    const val TOPIC_NAME = "add-revenue-coins"
  }
}
