package com.justplayapps.service.rewarding.facade

import com.google.protobuf.Int32Value
import com.justplayapps.base.Common
import com.justplayapps.base.nonBoostedAmountUsdOrNull
import com.justplayapps.base.nonBoostedUserCurrencyAmountOrNull
import com.justplayapps.base.userCurrencyEarningsProto
import com.justplayapps.playtime.rewarding.proto.PlaytimeRewarding.*
import com.justplayapps.playtime.rewarding.proto.genericRevenueProto
import com.justplayapps.service.rewarding.earnings.dto.UserCurrencyEarnings
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.util.fromProto
import com.moregames.base.util.toProto
import com.moregames.playtime.cashstreak.model.CashStreakReward
import com.moregames.playtime.earnings.dto.GenericRevenueDto
import com.moregames.playtime.user.CountryTierSettings
import java.util.*

fun HigherQuotasRndRange.fromProto(): AbTestingService.QuotasRndRange =
  AbTestingService.QuotasRndRange(
    lowerMultiplier = this.lowerMultiplier,
    upperMultiplier = this.upperMultiplier,
  )

fun CashStreakRewardProto.fromProto(): CashStreakReward =
  CashStreakReward(
    achievementDay = this.achievementDay,
    type = this.type.fromProto(),
    value = this.value.fromProto(),
    bigReward = this.bigReward,
  )

fun CountryTierSettingsProto.fromProto(): CountryTierSettings =
  CountryTierSettings(
    maxCashoutAmountMultiplier = this.maxCashoutAmountMultiplier.fromProto(),
    dailyEarningsQuotas = this.dailyEarningsQuotasList.map { it.fromProto() },
  )

fun GenericRevenueProto.fromProto(): GenericRevenueDto =
  GenericRevenueDto(
    id = this.id,
    userId = this.userId,
    source = this.source.fromProto(),
    timestamp = this.timestamp.fromProto(),
    amount = this.amount.fromProto(),
    amountExtra = if (this.hasAmountExtra()) this.amountExtra.fromProto() else null,
    gameId = if (this.hasGameId()) this.gameId.value else null,
  )

fun GenericRevenueDto.toProto(): GenericRevenueProto =
  genericRevenueProto {
    id = <EMAIL>
    userId = <EMAIL>
    source = <EMAIL>()
    timestamp = <EMAIL>()
    amount = <EMAIL>()
    if (<EMAIL> != null) {
      amountExtra = <EMAIL>()
    }
    if (<EMAIL> != null) {
      gameId = Int32Value.of(<EMAIL>)
    }
  }

fun UserCurrencyEarnings.toProto(): Common.UserCurrencyEarningsProto =
  userCurrencyEarningsProto {
    this.amountUsd = <EMAIL>()
    this.userCurrencyAmount = <EMAIL>()
    this.userCurrencyCode = <EMAIL>
    if (<EMAIL> != null) this.nonBoostedAmountUsd = <EMAIL>()
    if (<EMAIL> != null) this.nonBoostedUserCurrencyAmount = <EMAIL>()
  }

fun Common.UserCurrencyEarningsProto.fromProto(): UserCurrencyEarnings =
  UserCurrencyEarnings(
    amountUsd = this.amountUsd.fromProto(),
    userCurrency = Currency.getInstance(this.userCurrencyCode),
    userCurrencyAmount = this.userCurrencyAmount.fromProto(),
    nonBoostedAmountUsd = this.nonBoostedAmountUsdOrNull?.fromProto(),
    nonBoostedUserCurrencyAmount = this.nonBoostedUserCurrencyAmountOrNull?.fromProto(),
  )
