package com.justplayapps.service.rewarding.facade

import com.google.inject.Inject
import com.google.inject.Provider
import com.google.protobuf.Empty
import com.justplayapps.playtime.rewarding.boostedmode.BoostedModeApiGrpc
import com.justplayapps.playtime.rewarding.boostedmode.BoostedModeRewarding
import com.justplayapps.playtime.rewarding.boostedmode.BoostedModeRewarding.GetBoostedModeStatusResponse.StatusCase.*
import com.justplayapps.playtime.rewarding.boostedmode.getBoostedModeStatusRequest
import com.justplayapps.playtime.rewarding.proto.*
import com.moregames.base.dto.AppPlatform
import com.moregames.base.grpc.client.GenericFacade
import com.moregames.base.util.fromProto
import com.moregames.base.util.toProto
import com.moregames.playtime.earnings.currency.dto.CurrencyExchangeResultDto
import com.moregames.playtime.earnings.dto.GenericRevenueDto
import java.math.BigDecimal
import java.time.Instant
import java.util.*

class PlaytimeFacade @Inject constructor(
  private val playtimeApi: Provider<PlaytimeRewardingApiGrpc.PlaytimeRewardingApiStub>,
  private val boostedModeApi: Provider<BoostedModeApiGrpc.BoostedModeApiStub>,
  private val genericFacade: GenericFacade,
) {

  suspend fun getUserData(userId: String): PlaytimeRewarding.GetUserDataResponse {
    return genericFacade.get(
      playtimeApi.get()::getUserData,
      getUserDataRequest { this.userId = userId }
    )
  }

  suspend fun getConvertedUsdToUserCurrency(amountUsd: BigDecimal, userCurrency: Currency): CurrencyExchangeResultDto {
    return genericFacade.get(
      playtimeApi.get()::getConvertedUsdToUserCurrency,
      getConvertedUsdToUserCurrencyRequest {
        this.amountUsd = amountUsd.toProto()
        this.userCurrency = userCurrency.currencyCode
      }
    ).let {
      CurrencyExchangeResultDto(
        usdAmount = it.usdAmount.fromProto(),
        userCurrency = Currency.getInstance(it.userCurrency),
        amount = it.amount.fromProto(),
        amountNoRounding = it.amountNoRounding.fromProto(),
      )
    }
  }

  suspend fun revenueWithChallengesCut(userId: String, revenues: List<GenericRevenueDto>): BigDecimal {
    return genericFacade.get(
      playtimeApi.get()::getRevenueWithChallengesCut,
      getRevenueWithChallengesCutRequest {
        this.userId = userId
        this.revenues.addAll(revenues.map { it.toProto() })
      }
    ).revenue.fromProto()
  }

  suspend fun getOfferwallCoinsToUsdConversionRatio(): BigDecimal {
    return genericFacade.get(playtimeApi.get()::getOfferwallCoinsToUsdConversionRatio, Empty.getDefaultInstance()).ratio.fromProto()
  }

  suspend fun getBoostedModeConfig(userId: String, cashOutPeriodStart: Instant): BoostedModeConfig? =
    genericFacade.get(
      boostedModeApi.get()::getBoostedModeStatus,
      getBoostedModeStatusRequest {
        this.userId = userId
        this.cashoutPeriodStart = cashOutPeriodStart.toProto()
      }
    )
      .let { response -> BoostedModeConfig.fromProto(response) }

  suspend fun getCurrentTaskForGame(userId: String, gameId: Int): String? {
    return genericFacade.get(
      playtimeApi.get()::getCurrentTaskForGame,
      getCurrentTaskForGameRequest {
        this.userId = userId
        this.gameId = gameId
      }
    )
      .let { it.taskIdOrNull?.value }
  }

  suspend fun calculateCappedGameRevenue(
    userId: String,
    gameCoins: BigDecimal,
    coinsForOneDollar: BigDecimal,
    gamesRealRevenues: List<GenericRevenueDto>,
  ): BigDecimal =
    genericFacade.get(
      playtimeApi.get()::calculateCappedGameRevenue,
      calculateCappedGameRevenueRequest {
        this.userId = userId
        this.gameCoins = gameCoins.toProto()
        this.coinsForOneDollar = coinsForOneDollar.toProto()
        this.gamesRealRevenues.addAll(gamesRealRevenues.map { it.toProto() })
      }
    ).revenue.fromProto()

  data class UserData(
    val userId: String,
    val platform: AppPlatform,
    val coinGoalMilestones: List<Int>,
    val cashoutPeriodCounter: Int?,
  ) {
    companion object {
      fun PlaytimeRewarding.GetUserDataResponse.toDto(userId: String): UserData =
        UserData(
          userId = userId,
          platform = this.platform.fromProto(),
          coinGoalMilestones = this.coinGoalMilestonesList,
          cashoutPeriodCounter = if (this.hasCashoutPeriodCounter()) this.cashoutPeriodCounter.value else null
        )
    }
  }

  data class BoostedModeConfig(
    val earningsIncreaseCoefficient: BigDecimal,
    val earningsFakeDecreaseCoefficient: BigDecimal,
  ) {
    companion object {
      fun fromProto(proto: BoostedModeRewarding.GetBoostedModeStatusResponse): BoostedModeConfig? =
        when (proto.statusCase) {
          ENABLED -> BoostedModeConfig(
            earningsIncreaseCoefficient = proto.enabled.earningsIncreaseCoefficient.toBigDecimal(),
            earningsFakeDecreaseCoefficient = proto.enabled.earningsFakeDecreaseCoefficient.toBigDecimal(),
          )

          null, DISABLED, STATUS_NOT_SET -> null
        }
    }
  }
}