package com.justplayapps.service.rewarding.revenue

import com.google.inject.Inject
import com.justplayapps.service.rewarding.earnings.table.GenericRevenueDailyTotalsMk2Table
import com.moregames.base.applovin.JP_VIDEO_AD_ITEM_IDS
import com.moregames.base.base.BasePersistenceService
import com.moregames.base.messaging.dto.RevenueReceivedEventDto
import com.moregames.base.table.CurrentGenericRevenueTable
import com.moregames.base.table.GenericRevenueTotalsTable
import com.moregames.base.table.UserApplovinRevenueByPeriodsTable
import com.moregames.base.user.FakeMetaEarnings
import com.moregames.base.user.RevenueTotals
import com.moregames.base.util.TimeService
import com.moregames.base.util.localUtcDate
import com.moregames.base.util.toBeginningOf5MinInterval
import org.jetbrains.exposed.sql.*
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import javax.inject.Singleton

@Singleton
class RevenuePersistenceService @Inject constructor(
  database: Database,
  private val timeService: TimeService
) : BasePersistenceService(database) {

  companion object {
    const val DAYS_TO_STORE_DAILY_REVENUE_TOTALS = 180L
  }

  suspend fun getRevenueTotals(userId: String): RevenueTotals? =
    dbQuery {
      GenericRevenueTotalsTable
        .slice(
          GenericRevenueTotalsTable.revenueAmount,
          GenericRevenueTotalsTable.offerwallRevenue,
          GenericRevenueTotalsTable.day2Revenue,
          GenericRevenueTotalsTable.day0Revenue
        )
        .select { GenericRevenueTotalsTable.userId eq userId }
        .firstOrNull()?.let {
          RevenueTotals(
            revenue = it[GenericRevenueTotalsTable.revenueAmount],
            offerwallRevenue = it[GenericRevenueTotalsTable.offerwallRevenue],
            day2Revenue = it[GenericRevenueTotalsTable.day2Revenue],
            day0Revenue = it[GenericRevenueTotalsTable.day0Revenue]
          )
        }
    }

  suspend fun getLatestRevenueTime(userId: String): Instant? = dbQuery {
    GenericRevenueTotalsTable
      .slice(GenericRevenueTotalsTable.updatedAt)
      .select {
        (GenericRevenueTotalsTable.userId eq userId) and
          (GenericRevenueTotalsTable.revenueAmount greater BigDecimal.ZERO)
      }
      .firstOrNull()
      ?.get(GenericRevenueTotalsTable.updatedAt)
  }

  suspend fun resetCurrentRevenue(userId: String) = dbQuery {
    CurrentGenericRevenueTable.update({
      (CurrentGenericRevenueTable.userId eq userId) and
        (CurrentGenericRevenueTable.metaUserEarningsId.isNull())
    })
    {
      it[metaUserEarningsId] = FakeMetaEarnings.FAKE_META_ID_REVENUE_RESET.id
    }
  }

  suspend fun createZeroRevenue(userId: String) = dbQuery {
    GenericRevenueTotalsTable.insert {
      it[GenericRevenueTotalsTable.userId] = userId
      it[revenueAmount] = BigDecimal.ZERO
    }
  }

  suspend fun removeDailyRevenueOldTotalsBatch(batchSize: Int): Int = dbQuery {
    val longAgo = timeService.now().minus(DAYS_TO_STORE_DAILY_REVENUE_TOTALS, ChronoUnit.DAYS).localUtcDate()
    GenericRevenueDailyTotalsMk2Table
      .deleteWhere(limit = batchSize) {
        GenericRevenueDailyTotalsMk2Table.day less longAgo
      }
  }

  suspend fun getApplovinInterRevenue(userId: String, from: Instant, to: Instant): BigDecimal = dbQuery {
    UserApplovinRevenueByPeriodsTable
      .slice(UserApplovinRevenueByPeriodsTable.revenue.sum())
      .select {
        (UserApplovinRevenueByPeriodsTable.userId eq userId) and
          (UserApplovinRevenueByPeriodsTable.periodStart greaterEq from) and
          (UserApplovinRevenueByPeriodsTable.periodStart less to)
      }
      .firstOrNull()
      ?.let { it[UserApplovinRevenueByPeriodsTable.revenue.sum()] }
      ?: BigDecimal.ZERO
  }

  suspend fun getApplovinInterRevenue(userId: String, after: Instant): BigDecimal = dbQuery {
    UserApplovinRevenueByPeriodsTable
      .slice(UserApplovinRevenueByPeriodsTable.revenue.sum())
      .select {
        (UserApplovinRevenueByPeriodsTable.userId eq userId) and
          (UserApplovinRevenueByPeriodsTable.periodStart greaterEq after) and
          (UserApplovinRevenueByPeriodsTable.gameId notInList JP_VIDEO_AD_ITEM_IDS)
      }
      .groupBy(UserApplovinRevenueByPeriodsTable.userId)
      .firstOrNull()
      ?.let { it[UserApplovinRevenueByPeriodsTable.revenue.sum()] }
      ?: BigDecimal.ZERO
  }

  suspend fun getUserGameRevenueByPeriod(userId: String, periodStart: Instant, periodEnd: Instant): BigDecimal = dbQuery {
    CurrentGenericRevenueTable
      .slice(CurrentGenericRevenueTable.revenueAmount.sum())
      .select {
        (CurrentGenericRevenueTable.userId eq userId) and
          (CurrentGenericRevenueTable.timestamp greaterEq periodStart) and
          (CurrentGenericRevenueTable.timestamp less periodEnd) and
          (CurrentGenericRevenueTable.gameId notInList JP_VIDEO_AD_ITEM_IDS) and
          (CurrentGenericRevenueTable.revenueSource eq RevenueReceivedEventDto.RevenueSource.APPLOVIN.name)
      }
      .firstOrNull()
      ?.let { it.getOrNull(CurrentGenericRevenueTable.revenueAmount.sum()) ?: BigDecimal.ZERO }
      ?: BigDecimal.ZERO
  }

  suspend fun getGameRevenueByPeriod(periodStart: Instant, periodEnd: Instant): BigDecimal = dbQuery {
    CurrentGenericRevenueTable
      .slice(CurrentGenericRevenueTable.revenueAmount.sum())
      .select {
        (CurrentGenericRevenueTable.timestamp greaterEq periodStart) and
          (CurrentGenericRevenueTable.timestamp less periodEnd) and
          (CurrentGenericRevenueTable.gameId notInList JP_VIDEO_AD_ITEM_IDS) and
          (CurrentGenericRevenueTable.revenueSource eq RevenueReceivedEventDto.RevenueSource.APPLOVIN.name)
      }
      .firstOrNull()
      ?.getOrNull(CurrentGenericRevenueTable.revenueAmount.sum())
      ?: BigDecimal.ZERO
  }

  suspend fun getApplovinRevenue5minPeriodsCount(userId: String, since: Instant) = dbQuery {
    UserApplovinRevenueByPeriodsTable
      .slice(UserApplovinRevenueByPeriodsTable.gameId, UserApplovinRevenueByPeriodsTable.periodStart.count())
      .select {
        (UserApplovinRevenueByPeriodsTable.userId eq userId) and
          (UserApplovinRevenueByPeriodsTable.periodStart greaterEq since.toBeginningOf5MinInterval())
      }
      .groupBy(UserApplovinRevenueByPeriodsTable.gameId)
      .associate {
        it[UserApplovinRevenueByPeriodsTable.gameId] to it[UserApplovinRevenueByPeriodsTable.periodStart.count()]
      }
  }

  suspend fun getUserPerGameRevenue(userId: String) = dbQuery {
    UserApplovinRevenueByPeriodsTable
      .slice(UserApplovinRevenueByPeriodsTable.gameId, UserApplovinRevenueByPeriodsTable.revenue.sum())
      .select {
        (UserApplovinRevenueByPeriodsTable.userId eq userId) and
          (UserApplovinRevenueByPeriodsTable.gameId notInList JP_VIDEO_AD_ITEM_IDS)
      }
      .groupBy(UserApplovinRevenueByPeriodsTable.gameId)
      .map {
        it.toUserGameRevenue()
      }
  }

  suspend fun getUserRevenueAfter(userId: String, afterDay: LocalDate) = dbQuery {
    GenericRevenueDailyTotalsMk2Table
      .slice(GenericRevenueDailyTotalsMk2Table.revenueAmount.sum())
      .select {
        (GenericRevenueDailyTotalsMk2Table.userId eq userId) and
          (GenericRevenueDailyTotalsMk2Table.day greaterEq afterDay)
      }
      .singleOrNull()
      ?.get(GenericRevenueDailyTotalsMk2Table.revenueAmount.sum()) ?: BigDecimal.ZERO
  }

  private fun ResultRow.toUserGameRevenue() =
    UserGameRevenue(
      gameId = this[UserApplovinRevenueByPeriodsTable.gameId],
      revenue = this[UserApplovinRevenueByPeriodsTable.revenue.sum()] ?: BigDecimal.ZERO
    )

  data class UserGameRevenue(
    val gameId: Int,
    val revenue: BigDecimal
  )

}
