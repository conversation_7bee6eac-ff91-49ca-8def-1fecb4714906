package com.moregames.playtime.app

import com.google.inject.Inject
import com.justplayapps.service.rewarding.earnings.UserEarningsService
import com.justplayapps.service.rewarding.revenue.RevenueAggregationService
import com.moregames.base.app.OfferWallType
import com.moregames.base.util.TimeService
import com.moregames.base.util.cronLogger
import com.moregames.base.util.logger
import com.moregames.playtime.app.CronTempDataPersistenceService.Companion.FORCE_ASSIGN_GA_USER_PROPERTIES
import com.moregames.playtime.app.CronTempDataPersistenceService.Companion.REMOVE_COINS_BY_PERIODS_CRON_JOB
import com.moregames.playtime.app.CronTempDataPersistenceService.Companion.REMOVE_EMPTY_GAME_COINS_TOTALS_CRON_JOB
import com.moregames.playtime.app.CronTempDataPersistenceService.Companion.REMOVE_REVENUE_BY_PERIODS_CRON_JOB
import com.moregames.playtime.app.CronTempDataPersistenceService.Companion.SCHEDULE_PROMOTIONS_NOTIFICATIONS
import com.moregames.playtime.app.CronTempDataPersistenceService.Companion.SCHEDULE_PROMOTIONS_NOTIFICATIONS_FYBER
import com.moregames.playtime.app.CronTempDataPersistenceService.Companion.SEND_PROMOTIONS_EMAILS
import com.moregames.playtime.app.CronTempDataPersistenceService.Companion.TIDY_UP_ACTIVE_USERS
import com.moregames.playtime.cashstreak.CashStreakEngine
import com.moregames.playtime.earnings.currency.CurrencyExchangeService
import com.moregames.playtime.notifications.MassEmailNotificationService
import com.moregames.playtime.notifications.MassPushNotificationService
import com.moregames.playtime.notifications.PromotionsNotificationService
import com.moregames.playtime.revenue.applovin.ApplovinUserEarningsService
import com.moregames.playtime.revenue.applovin.DataImportManager
import com.moregames.playtime.user.ActiveUsersService
import com.moregames.playtime.user.UserFirebasePropertiesForceAssignmentsService
import com.moregames.playtime.user.UserPersistenceService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.abmigration.AbTestingMigrationService
import com.moregames.playtime.user.cashout.CashoutPeriodsService
import com.moregames.playtime.user.verification.VerificationService
import com.moregames.playtime.util.roundDownToSecondDigit
import io.ktor.application.*
import io.ktor.http.*
import io.ktor.response.*
import io.ktor.routing.*
import java.math.BigDecimal
import java.time.Duration
import javax.inject.Singleton
import kotlin.system.measureTimeMillis

@Singleton
class CronController @Inject constructor(
  private val dataImportManager: DataImportManager,
  private val userEarningsService: UserEarningsService,
  private val applovinUserEarningsService: ApplovinUserEarningsService,
  private val userPersistenceService: UserPersistenceService,
  private val revenueAggregationService: RevenueAggregationService,
  private val cashoutPeriodsService: CashoutPeriodsService,
  private val currencyExchangeService: CurrencyExchangeService,
  private val cronTempDataPersistenceService: CronTempDataPersistenceService,
  private val verificationService: VerificationService,
  private val promotionsNotificationService: PromotionsNotificationService,
  private val massPushNotificationService: MassPushNotificationService,
  private val massEmailNotificationService: MassEmailNotificationService,
  private val activeUsersService: ActiveUsersService,
  private val abTestingMigrationService: AbTestingMigrationService,
  private val cashStreakEngine: CashStreakEngine,
  private val userService: UserService,
  private val timeService: TimeService,
  private val userFirebasePropertiesForceAssignmentsService: UserFirebasePropertiesForceAssignmentsService,
) {
  companion object {
    val validUUIDChars = (('0'..'9') + ('a'..'f')).toList().map { it.toString() }
    val unconvertedRevenueByDeletedUsersThreshold = BigDecimal("50")
  }

  fun startRouting(cronRoute: Route) {
    cronRoute.get("/applovinRevenuesImport") {
      cronLogger().info("Starting 'applovinRevenuesImport'")
      dataImportManager.onApplovinRevenuesImportTriggered()
      call.respond(HttpStatusCode.OK)
      cronLogger().info("Finished 'applovinRevenuesImport'")
    }

    cronRoute.get("/processImportedApplovinRevenue") {
      cronLogger().info("Starting 'processImportedApplovinRevenue'")
      applovinUserEarningsService.processImportedApplovinRevenue()
      call.respond(HttpStatusCode.OK)
      cronLogger().info("Finished 'processImportedApplovinRevenue'")
    }

    cronRoute.get("/removeOldDataFromCurrentGenericRevenue") {
      cronLogger().info("Starting 'removeOldDataFromCurrentGenericRevenue'")
      userEarningsService.removeOldDataFromCurrentGenericRevenue().let {
        cronLogger().info("$it rows deleted 'removeOldDataFromCurrentGenericRevenue'")
      }
      call.respond(HttpStatusCode.OK)
      cronLogger().info("Finished 'removeOldDataFromCurrentGenericRevenue'")
    }

    cronRoute.get("/removeUnconvertedOldRevenueForDeletedUsers") {
      cronLogger().info("Starting 'removeUnconvertedOldRevenueForDeletedUsers'")
      userEarningsService.removeUnconvertedOldRevenueForDeletedUsers().let { (rows, revenue) ->
        cronLogger().info("$rows rows deleted 'removeUnconvertedOldRevenueForDeletedUsers'")
        if (revenue > unconvertedRevenueByDeletedUsersThreshold) {
          logger().error("${revenue.roundDownToSecondDigit()}$ of unconverted revenue by deleted users was removed")
        }
      }
      call.respond(HttpStatusCode.OK)
      cronLogger().info("Finished 'removeUnconvertedOldRevenueForDeletedUsers'")
    }

    cronRoute.get("/checkUsersWithoutOpenCashoutPeriods") {
      cronLogger().info("Check users without open cashout periods. Start")
      cashoutPeriodsService.checkUsersWithoutOpenCashoutPeriods().let {
        cronLogger().info("Check users without open cashout periods. Finished, $it users found")
      }
      call.respond(HttpStatusCode.OK)
    }

    cronRoute.get("/syncCurrencyExchangeRates") {
      cronLogger().info("Sync currency exchange rates. Start")
      currencyExchangeService.syncCurrencyExchangeRates()
      cronLogger().info("Sync currency exchange rates. Finish")
      call.respond(HttpStatusCode.OK)
    }

    cronRoute.get("/removeEmptyGameCoinsTotals") {
      cronLogger().info("Removing empty game coins totals. Start.")
      val batchSize = call.parameters["batchSize"]!!.toInt()
      val runData = cronTempDataPersistenceService.getCronJobLastRunData(REMOVE_EMPTY_GAME_COINS_TOTALS_CRON_JOB)
      val rowsRemoved = userPersistenceService.removeBatchOfZeroGameTotals(batchSize, runData.lastChunkUserIdFirstLetter)
      runData.copy(
        lastChunkUserIdFirstLetter = runData.lastChunkUserIdFirstLetter.takeIf { rowsRemoved == batchSize }
          ?: getNextUUIDPrefix(runData.lastChunkUserIdFirstLetter),
        processedAt = timeService.now()
      ).also { cronTempDataPersistenceService.saveCronJobLastRunData(REMOVE_EMPTY_GAME_COINS_TOTALS_CRON_JOB, it) }

      cronLogger().info("Removing empty game coins totals. Finish. Rows removed :$rowsRemoved")
      call.respond(HttpStatusCode.OK)
    }

    cronRoute.get("/removeCoinsByPeriods") {
      cronLogger().info("Removing coins by periods. Start")
      val batchSize = call.parameters["batchSize"]!!.toInt()
      val runData = cronTempDataPersistenceService.getCronJobLastRunData(REMOVE_COINS_BY_PERIODS_CRON_JOB)
      val rowsRemoved = userPersistenceService.removeBatchOfCoinsTrackedByPeriods(batchSize, runData.lastChunkUserIdFirstLetter)
      val rowsRemovedFractional = userPersistenceService.removeBatchOfFractionalCoinsTrackedByPeriods(batchSize, runData.lastChunkUserIdFirstLetter)
      runData.copy(
        lastChunkUserIdFirstLetter = runData.lastChunkUserIdFirstLetter.takeIf { rowsRemoved == batchSize || rowsRemovedFractional == batchSize }
          ?: getNextUUIDPrefix(runData.lastChunkUserIdFirstLetter),
        processedAt = timeService.now()
      ).also { cronTempDataPersistenceService.saveCronJobLastRunData(REMOVE_COINS_BY_PERIODS_CRON_JOB, it) }

      cronLogger().info("Removing coins by periods. Finish. Rows removed :$rowsRemoved")
      call.respond(HttpStatusCode.OK)
    }

    cronRoute.get("/removeRevenueByPeriods") {
      cronLogger().info("Removing revenue by periods. Start")
      val batchSize = call.parameters["batchSize"]!!.toInt()
      val runData = cronTempDataPersistenceService.getCronJobLastRunData(REMOVE_REVENUE_BY_PERIODS_CRON_JOB)
      val rowsRemoved = userEarningsService.removeBatchOfRevenueTrackedByPeriods(batchSize, runData.lastChunkUserIdFirstLetter)
      runData.copy(
        lastChunkUserIdFirstLetter = runData.lastChunkUserIdFirstLetter.takeIf { rowsRemoved == batchSize }
          ?: getNextUUIDPrefix(runData.lastChunkUserIdFirstLetter),
        processedAt = timeService.now()
      ).also { cronTempDataPersistenceService.saveCronJobLastRunData(REMOVE_REVENUE_BY_PERIODS_CRON_JOB, it) }

      cronLogger().info("Removing revenue by periods. Finish. Rows removed :$rowsRemoved")
      call.respond(HttpStatusCode.OK)
    }

    cronRoute.get("/removeOldFaceMaps") {
      cronLogger().info("Removing old FaceMaps from Facetec. Start")
      val olderThanDays = call.parameters["olderThanDays"]!!.toInt()
      val limit = call.parameters["limit"]?.toInt()
      val isDryRun = (call.parameters["dryRun"]?.toInt() ?: 1) != 0
      val duration = measureTimeMillis {
        verificationService.removeOldFaceMaps(olderThanDays.toLong(), limit ?: 10000, isDryRun).let {
          cronLogger().info("Removed FaceMaps older than $olderThanDays days from Facetec. Finished, $it FaceMaps removed")
        }
      }
      logger().info(
        "removeOldFaceMaps duration: ${
          Duration.ofMillis(duration).toString()
            .substring(2)
            .replace("(\\d[HMS])(?!$)", "$1 ")
            .lowercase()
        }"
      )
      call.respond(HttpStatusCode.OK)
    }

    cronRoute.get("/sendScheduledGenericPushNotifications") {
      cronLogger().info("Send scheduled generic push notifications. Start")
      val batchSize = call.parameters["batchSize"]!!.toInt()
      massPushNotificationService.sendScheduledGenericPushNotificationsBatch(batchSize)
        .also { cronLogger().info("Send scheduled generic push notifications. Finish. Notifications sent :$it") }
      call.respond(HttpStatusCode.OK)
    }

    cronRoute.get("/sendScheduledEmailNotifications") {
      cronLogger().info("Send scheduled email notifications batch. Start")
      val batchSize = call.parameters["batchSize"]!!.toInt()
      massEmailNotificationService.sendScheduledEmailNotificationsBatch(batchSize)
        .also { cronLogger().info("Send scheduled email notifications batch. Finish. Emails sent :$it") }
      call.respond(HttpStatusCode.OK)
    }

    cronRoute.get("/schedulePromotionsNotifications") {
      cronLogger().info("Schedule promotions notifications. Start.")
      val batchSize = call.parameters["batchSize"]!!.toInt()
      val runData = cronTempDataPersistenceService.getCronJobLastRunData(SCHEDULE_PROMOTIONS_NOTIFICATIONS)
      val userIdsProcessed = promotionsNotificationService.scheduleNotifications(
        batchSize = batchSize,
        lastChunkUserIdFirstLetter = runData.lastChunkUserIdFirstLetter,
        offerWallType = OfferWallType.TAPJOY
      )
      runData.copy(
        lastChunkUserIdFirstLetter = runData.lastChunkUserIdFirstLetter.takeIf { userIdsProcessed == batchSize }
          ?: getNextUUIDPrefix(runData.lastChunkUserIdFirstLetter),
        processedAt = timeService.now()
      ).also { cronTempDataPersistenceService.saveCronJobLastRunData(SCHEDULE_PROMOTIONS_NOTIFICATIONS, it) }

      cronLogger().info("Schedule promotions notifications. Finish. Users' ids processed :$userIdsProcessed")
      call.respond(HttpStatusCode.OK)
    }

    cronRoute.get("/schedulePromotionsNotificationsFyber") {
      cronLogger().info("Schedule promotions notifications fyber. Start.")
      val batchSize = call.parameters["batchSize"]!!.toInt()
      val runData = cronTempDataPersistenceService.getCronJobLastRunData(SCHEDULE_PROMOTIONS_NOTIFICATIONS_FYBER)
      val userIdsProcessed = promotionsNotificationService.scheduleNotifications(
        batchSize = batchSize,
        lastChunkUserIdFirstLetter = runData.lastChunkUserIdFirstLetter,
        offerWallType = OfferWallType.FYBER
      )
      runData.copy(
        lastChunkUserIdFirstLetter = runData.lastChunkUserIdFirstLetter.takeIf { userIdsProcessed == batchSize }
          ?: getNextUUIDPrefix(runData.lastChunkUserIdFirstLetter),
        processedAt = timeService.now()
      ).also { cronTempDataPersistenceService.saveCronJobLastRunData(SCHEDULE_PROMOTIONS_NOTIFICATIONS_FYBER, it) }

      cronLogger().info("Schedule promotions notification fyber. Finish. Users' ids processed :$userIdsProcessed")
      call.respond(HttpStatusCode.OK)
    }

    cronRoute.get("/sendPromotionsEmailNotifications") {
      cronLogger().info("Send promotions email notifications. Start.")
      val batchSize = call.parameters["batchSize"]!!.toInt()
      val runData = cronTempDataPersistenceService.getCronJobLastRunData(SEND_PROMOTIONS_EMAILS)
      val sentEmailsCount = promotionsNotificationService.sendPromotionsEmailsBatch(
        batchSize = batchSize,
        userIdPrefix = runData.lastChunkUserIdFirstLetter,
      )
      runData.copy(
        lastChunkUserIdFirstLetter = runData.lastChunkUserIdFirstLetter.takeIf { sentEmailsCount == batchSize }
          ?: getNextUUIDPrefix(runData.lastChunkUserIdFirstLetter),
        processedAt = timeService.now()
      ).also { cronTempDataPersistenceService.saveCronJobLastRunData(SEND_PROMOTIONS_EMAILS, it) }

      cronLogger().info("Send promotions email notifications. Finish. Emails sent :$sentEmailsCount")
      call.respond(HttpStatusCode.OK)
    }

    cronRoute.get("/cleanUpProcessedScheduledNotifications") {
      cronLogger().info("Clean up processed scheduled notifications. Start.")
      val batchSize = call.parameters["batchSize"]!!.toInt()
      val rowsRemoved = massPushNotificationService.cleanUpProcessedScheduledNotifications(batchSize)
      cronLogger().info("Clean up processed scheduled notifications. Finish. Rows removed :$rowsRemoved")
      call.respond(HttpStatusCode.OK)
    }

    cronRoute.get("/cleanUpProcessedEmailNotifications") {
      cronLogger().info("Clean up processed email notifications. Start.")
      val batchSize = call.parameters["batchSize"]!!.toInt()
      val rowsRemoved = massEmailNotificationService.cleanUpProcessedEmailNotifications(batchSize)
      cronLogger().info("Clean up processed email notifications. Finish. Rows removed :$rowsRemoved")
      call.respond(HttpStatusCode.OK)
    }

    cronRoute.get("/cleanUpProcessedPromotionsEmails") {
      cronLogger().info("Clean up processed promotions emails notifications. Start.")
      val batchSize = call.parameters["batchSize"]!!.toInt()
      val rowsRemoved = promotionsNotificationService.cleanUpProcessedPromotionsEmails(batchSize)
      cronLogger().info("Clean up processed promotions emails notifications. Finish. Rows removed :$rowsRemoved")
      call.respond(HttpStatusCode.OK)
    }

    cronRoute.get("/cleanUpGenericRevenueDailyTotals") {
      cronLogger().info("Clean up generic revenue daily totals. Start.")
      val batchSize = call.parameters["batchSize"]!!.toInt()
      val rowsRemoved = revenueAggregationService.removeDailyRevenueOldTotalsBatch(batchSize)
      cronLogger().info("Clean up generic revenue daily totals. Finish. Rows removed :$rowsRemoved")
      call.respond(HttpStatusCode.OK)
    }

    cronRoute.get("/tidyUpActiveUsersList") {
      val batchSize = call.parameters["batchSize"]!!.toInt()
      val runData = cronTempDataPersistenceService.getCronJobLastRunData(TIDY_UP_ACTIVE_USERS)
      cronLogger().info("tidyUpActiveUsersList. Start. ${runData.lastChunkUserIdFirstLetter}")
      val usersRemoved = activeUsersService.removeNonActiveUsers(userIdPrefix = runData.lastChunkUserIdFirstLetter, batchSize)
      runData.copy(
        lastChunkUserIdFirstLetter = runData.lastChunkUserIdFirstLetter.takeIf { usersRemoved == batchSize }
          ?: getNextUUIDPrefix(runData.lastChunkUserIdFirstLetter),
        processedAt = timeService.now()
      ).also { cronTempDataPersistenceService.saveCronJobLastRunData(TIDY_UP_ACTIVE_USERS, it) }
      cronLogger().info("tidyUpActiveUsersList. Finish. Users removed: $usersRemoved")
      call.respond(HttpStatusCode.OK)
    }

    cronRoute.get("/abForceAssignmentProcessing") {
      cronLogger().info("Ab testing force assignment processing")
      val batchSize = call.parameters["batchSize"]!!.toInt()
      val userIdsProcessed = abTestingMigrationService.processBatchOfForceAssigningUsers(batchSize)
      cronLogger().info("Ab testing force assignment processing. Finish. User ids processed :$userIdsProcessed")
      call.respond(HttpStatusCode.OK)
    }

    cronRoute.get("/cleanAbForceAssignmentProcessed") {
      cronLogger().info("cleanAbForceAssignmentProcessed")
      val batchSize = call.parameters["batchSize"]!!.toInt()
      val rowsRemoved = abTestingMigrationService.cleanAbForceAssignmentProcessed(batchSize)
      cronLogger().info("cleanAbForceAssignmentProcessed. Finish. Rows removed :$rowsRemoved")
      call.respond(HttpStatusCode.OK)
    }

    cronRoute.get("/notifyUsersCloseToStreakDeadline") {
      cronLogger().info("notifyUsersCloseToStreakDeadline")
      val batchSize = call.parameters["batchSize"]!!.toInt()
      val notifiedCount = cashStreakEngine.notifyUsersCloseToStreakDeadline(batchSize)
      cronLogger().info("notifyUsersCloseToStreakDeadline. Finish. Notifications count :$notifiedCount")
      call.respond(HttpStatusCode.OK)
    }

    cronRoute.get("/calculateMinThresholdsForEcpmGroups") {
      cronLogger().info("calculateMinThresholdsForEcpmGroups")
      userService.calculateMinThresholdsForEcpmGroups()
      cronLogger().info("calculateMinThresholdsForEcpmGroups. Finish. ")
      call.respond(HttpStatusCode.OK)
    }

    cronRoute.get("/forceAssignGaUserProperties") {
      val batchSize = call.parameters["batchSize"]!!.toInt()
      cronLogger().info("forceAssignGaUserProperties")
      val runData = cronTempDataPersistenceService.getCronJobLastRunData(FORCE_ASSIGN_GA_USER_PROPERTIES)
      val usersAssigned = userFirebasePropertiesForceAssignmentsService.forceAssignGaUserProperties(runData.lastChunkUserIdFirstLetter, batchSize)
      runData.copy(
        lastChunkUserIdFirstLetter = runData.lastChunkUserIdFirstLetter.takeIf { usersAssigned == batchSize } ?: getNextUUIDPrefix(runData.lastChunkUserIdFirstLetter),
        processedAt = timeService.now()
      ).also { cronTempDataPersistenceService.saveCronJobLastRunData(FORCE_ASSIGN_GA_USER_PROPERTIES, it) }
      cronLogger().info("forceAssignGaUserProperties. Finish. ")
      call.respond(HttpStatusCode.OK)
    }
  }

  private fun getNextUUIDPrefix(currentLetter: String): String =
    when {
      currentLetter.isBlank() || currentLetter == "f" -> "0"
      else -> validUUIDChars.indexOf(currentLetter).let { validUUIDChars[it + 1] }
    }
}
