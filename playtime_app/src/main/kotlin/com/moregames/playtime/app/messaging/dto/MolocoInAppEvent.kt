package com.moregames.playtime.app.messaging.dto


import com.moregames.base.dto.AppPlatform
import java.math.BigDecimal

data class MolocoInAppEvent(
  val ipAddress: String,
  val userAgent: String,
  val idfa: String?,
  val idfv: String?,
  val platform: AppPlatform,
  val amount: BigDecimal,
  val timestamp: Long,
  val type: EventType
)


enum class EventType(
  val eventName: String,
  val eventType: String,
) {
  SC_WITH_REVENUE("sc_with_revenue", "PURCHASE"),
  OW_WITH_REVENUE("ow_with_revenue", "PURCHASE"),
}