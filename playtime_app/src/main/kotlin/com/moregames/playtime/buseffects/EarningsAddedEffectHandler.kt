package com.moregames.playtime.buseffects

import com.justplayapps.playtime.proto.PlaytimeEvents
import com.moregames.base.bus.MessageBus
import com.moregames.base.bus.MessageHandler
import com.moregames.base.util.fromProto
import com.moregames.playtime.earnings.CashoutSettingsService
import com.moregames.playtime.user.UserService
import javax.inject.Inject

class EarningsAddedEffectHandler @Inject constructor(
  private val cashoutSettingsService: CashoutSettingsService,
  private val userService: UserService,
  private val messageBus: MessageBus,
) {

  @MessageHandler
  suspend fun handleEarningsAdded(effect: PlaytimeEvents.UnpaidUserEarningsAddedEvent) {
    if (effect.earnings.amountUsd.fromProto().compareTo(cashoutSettingsService.getUserMaxEarningsAmount(effect.userId)) == 0) {
      createEarningsThresholdReachedPopupMessage(effect.userId)
    }
  }

  private suspend fun createEarningsThresholdReachedPopupMessage(userId: String) {
    userService.getUser(userId).let {
      messageBus.publishAsync(CreateThresholdReachedPopupMessageEffect(it.id, it.locale))
    }
  }
}