package com.moregames.playtime.carousel

import com.google.inject.Singleton
import com.moregames.base.base.BasePersistenceService
import com.moregames.base.exceptions.RecordNotFoundException
import com.moregames.base.util.TimeService
import com.moregames.playtime.carousel.domain.UserCarouselTaskEntity
import org.jetbrains.exposed.sql.*
import java.time.Instant
import java.util.*
import javax.inject.Inject

@Singleton
class CarouselPersistenceService @Inject constructor(
  database: Database,
  private val timeService: TimeService,
) : BasePersistenceService(database) {
  suspend fun findVisibleCarouselTasks(userId: String) = dbQuery {
    UserCarouselTaskTable
      .join(CarouselTaskTable, JoinType.INNER, UserCarouselTaskTable.taskDefinitionId, CarouselTaskTable.id)
      .select { UserCarouselTaskTable.userId eq userId and (UserCarouselTaskTable.state neq UserCarouselTaskState.COMPLETED) }
      .map { it.toUserCarouselTaskEntity() }
  }

  suspend fun findActiveCarouselTaskPerGame(userId: String, gameId: Int) = dbQuery {
    UserCarouselTaskTable
      .join(CarouselTaskTable, JoinType.INNER, UserCarouselTaskTable.taskDefinitionId, CarouselTaskTable.id)
      .select {
        (UserCarouselTaskTable.userId eq userId) and (CarouselTaskTable.gameId eq gameId) and (UserCarouselTaskTable.state inList listOf(
          UserCarouselTaskState.IN_PROGRESS
        ))
      }
      .singleOrNull()
      ?.toUserCarouselTaskEntity()
  }

  suspend fun getUserCarouselTask(taskId: UUID) = dbQuery {
    UserCarouselTaskTable
      .join(CarouselTaskTable, JoinType.INNER, UserCarouselTaskTable.taskDefinitionId, CarouselTaskTable.id)
      .select { UserCarouselTaskTable.id eq taskId }
      .singleOrNull()
      ?.toUserCarouselTaskEntity() ?: throw RecordNotFoundException(UserCarouselTaskTable::class, taskId.toString())
  }

  suspend fun markTaskAsStarted(taskId: UUID) {
    updateState(taskId, UserCarouselTaskState.IN_PROGRESS)
  }

  suspend fun markTaskAsFinished(taskId: UUID) {
    updateState(taskId, UserCarouselTaskState.UNCLAIMED)
  }

  suspend fun markTaskAsClaimed(taskId: UUID, blockedUntil: Instant) {
    dbQuery {
      UserCarouselTaskTable.update({ UserCarouselTaskTable.id eq taskId }) {
        it[state] = UserCarouselTaskState.CLAIMED
        it[UserCarouselTaskTable.blockedUntil] = blockedUntil
      }
    }
  }

  suspend fun completeTask(taskId: UUID) = dbQuery {
    UserCarouselTaskTable.update({ UserCarouselTaskTable.id eq taskId }) {
      it[state] = UserCarouselTaskState.COMPLETED
      it[completedAt] = timeService.now()
    }
  }

  suspend fun updateTaskProgress(taskId: UUID, currentProgress: Int, progress: Int, achievement: String? = null) = dbQuery {
    UserCarouselTaskTable.update({
      (UserCarouselTaskTable.id eq taskId)
        .and(UserCarouselTaskTable.progress eq currentProgress)
    }) { row ->
      row[UserCarouselTaskTable.progress] = progress
      achievement?.also { row[UserCarouselTaskTable.achievement] = it }
    } == 1
  }

  suspend fun countUnclaimedTasks(userId: String): Long = dbQuery {
    UserCarouselTaskTable
      .select { (UserCarouselTaskTable.userId eq userId) and (UserCarouselTaskTable.state eq UserCarouselTaskState.UNCLAIMED) }
      .count()
  }

  private suspend fun updateState(taskId: UUID, state: UserCarouselTaskState) = dbQuery {
    UserCarouselTaskTable.update({ UserCarouselTaskTable.id eq taskId }) {
      it[UserCarouselTaskTable.state] = state
    }
  }

  suspend fun findActiveTaskDefinitions() = dbQuery {
    CarouselTaskTable
      .select { CarouselTaskTable.enabled eq true }
      .map { it[CarouselTaskTable.id] }
  }

  suspend fun createNewTasks(userId: String, ids: List<String>) = dbQuery {
    UserCarouselTaskTable.batchInsert(ids, shouldReturnGeneratedValues = false) { id ->
      this[UserCarouselTaskTable.userId] = userId
      this[UserCarouselTaskTable.taskDefinitionId] = id
      this[UserCarouselTaskTable.state] = UserCarouselTaskState.NEW
      this[UserCarouselTaskTable.progress] = 0
    }
  }

  suspend fun createToken(userId: String, taskId: UUID) {
    dbQuery {
      UserCarouselToken.insert {
        it[UserCarouselToken.userId] = userId
        it[UserCarouselToken.taskId] = taskId
        it[status] = TokenState.UNREWARDED
      }
    }
  }

  suspend fun countUnrewardedTokens(userId: String): Long = dbQuery {
    UserCarouselToken
      .select { (UserCarouselToken.userId eq userId) and (UserCarouselToken.status eq TokenState.UNREWARDED) }
      .count()
  }

  suspend fun markTokenAsRewarded(userId: String, taskId: UUID) = dbQuery {
    UserCarouselToken.update({ (UserCarouselToken.userId eq userId) and (UserCarouselToken.taskId eq taskId) }) {
      it[status] = TokenState.REWARDED
    }
  }

  private fun ResultRow.toUserCarouselTaskEntity() = UserCarouselTaskEntity(
    taskId = this[UserCarouselTaskTable.id].value,
    userId = this[UserCarouselTaskTable.userId],
    taskDefinitionId = this[UserCarouselTaskTable.taskDefinitionId],
    state = this[UserCarouselTaskTable.state],
    progress = this[UserCarouselTaskTable.progress],
    achievement = this[UserCarouselTaskTable.achievement],
    completedAt = this[UserCarouselTaskTable.completedAt],
    blockedUntil = this[UserCarouselTaskTable.blockedUntil],
    titleTranslation = this[CarouselTaskTable.title],
    icon = this[CarouselTaskTable.icon],
    progressMax = this[CarouselTaskTable.progressMax],
    goal = this[CarouselTaskTable.goal],
    gameId = this[CarouselTaskTable.gameId],
    calculator = this[CarouselTaskTable.calculator],
    enabled = this[CarouselTaskTable.enabled],
    order = this[CarouselTaskTable.order],
  )
}