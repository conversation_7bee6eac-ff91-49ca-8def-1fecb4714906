package com.moregames.playtime.carousel

import com.moregames.base.bus.MessageBus
import com.moregames.base.db.OptimisticLockException
import com.moregames.base.util.TimeService
import com.moregames.playtime.carousel.UserCarouselTaskState.*
import com.moregames.playtime.carousel.domain.TaskDefinition
import com.moregames.playtime.carousel.domain.UserCarouselTask
import com.moregames.playtime.carousel.domain.UserCarouselTaskEntity
import com.moregames.playtime.util.minus
import com.moregames.playtime.util.plus
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.minutes

@Singleton
class CarouselService @Inject constructor(
  private val carouselPersistenceService: CarouselPersistenceService,
  private val timeService: TimeService,
  private val messageBus: MessageBus,
) {
  suspend fun generateInitialTasksList(userId: String) {
    val ids = carouselPersistenceService.findActiveTaskDefinitions()
    carouselPersistenceService.createNewTasks(userId, ids)
  }

  suspend fun createTask(userId: String, taskDefinitionId: String) {
    carouselPersistenceService.createNewTasks(userId, listOf(taskDefinitionId))
  }

  suspend fun findActiveUserTask(userId: String, gameId: Int): UserCarouselTask.InProgress? {
    return carouselPersistenceService.findActiveCarouselTaskPerGame(userId, gameId)
      ?.toUserCarouselTask() as? UserCarouselTask.InProgress
  }

  suspend fun getTask(taskId: UUID): UserCarouselTask {
    return carouselPersistenceService.getUserCarouselTask(taskId).toUserCarouselTask()
  }

  suspend fun countUnclaimedTasks(userId: String): Long {
    return carouselPersistenceService.countUnclaimedTasks(userId)
  }

  suspend fun updateTaskProgress(task: UserCarouselTask.InProgress, progress: Int, achievement: String?): UserCarouselTask {
    val updated = carouselPersistenceService.updateTaskProgress(task.taskId, task.progress, progress, achievement)
    if (!updated) throw OptimisticLockException("Carousel task ${task.taskId} update failed due to optimistic locking")

    return carouselPersistenceService.getUserCarouselTask(task.taskId).toUserCarouselTask()
  }

  suspend fun markTaskAsFinished(taskId: UUID) {
    carouselPersistenceService.markTaskAsFinished(taskId)
  }

  suspend fun claimTask(taskId: UUID) {
    val task = getTask(taskId)
    if (task !is UserCarouselTask.Unclaimed) throw IllegalStateException("Task $taskId is not in UNCLAIMED state")

    val blockedUntil = timeService.now() + 1.hours
    carouselPersistenceService.markTaskAsClaimed(taskId, blockedUntil)
    messageBus.publish(CarouselRecreateTaskCommand(taskId), blockedUntil - 1.minutes) // small shift to give time for event to be processed
  }

  private fun UserCarouselTaskEntity.toUserCarouselTask(): UserCarouselTask {
    val definition = TaskDefinition(
      gameId = gameId,
      titleTranslation = titleTranslation,
      icon = icon,
      progressMax = progressMax,
      goal = goal,
      order = order,
      calculator = calculator,
      enabled = enabled,
      id = taskDefinitionId,
    )
    return when (state) {
      NEW -> UserCarouselTask.New(taskId, userId, definition)
      IN_PROGRESS -> UserCarouselTask.InProgress(taskId, userId, definition, progress, achievement)
      UNCLAIMED -> UserCarouselTask.Unclaimed(taskId, userId, definition)
      CLAIMED -> UserCarouselTask.Claimed(taskId, userId, definition, blockedUntil!!)
      COMPLETED -> UserCarouselTask.Completed(taskId, userId, definition)
    }
  }
}
