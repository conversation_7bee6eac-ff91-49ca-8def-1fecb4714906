package com.moregames.playtime.carousel

import com.justplayapps.playtime.carousel.firstTaskCompletedEvent
import com.moregames.base.bus.Message
import com.moregames.base.bus.MessageBus
import com.moregames.base.bus.MessageHandler
import com.moregames.playtime.carousel.domain.UserCarouselTask.Claimed
import java.util.*
import javax.inject.Inject

class CarouselTaskLifecycleHandlers @Inject constructor(
  private val carouselService: CarouselService,
  private val messageBus: MessageBus,
) {
  @MessageHandler
  suspend fun handleCarouselTaskFinishedEvent(event: CarouselTaskFinishedEvent) {
    val count = carouselService.countUnclaimedTasks(event.userId)
    if (count == 1L) {
      messageBus.publish(firstTaskCompletedEvent {
        taskId = event.taskId.toString()
      })
    }
  }

  @MessageHandler
  suspend fun handleCarouselRecreateTaskCommand(command: CarouselRecreateTaskCommand) {
    val previousTask = carouselService.getTask(command.taskId)
    if (previousTask !is Claimed) return

    carouselService.markTaskAsFinished(previousTask.taskId)

    if (!previousTask.definition.enabled) return

    carouselService.createTask(previousTask.userId, previousTask.definition.id)
  }
}

data class CarouselTaskFinishedEvent(val taskId: UUID, val userId: String) : Message
data class CarouselRecreateTaskCommand(val taskId: UUID) : Message