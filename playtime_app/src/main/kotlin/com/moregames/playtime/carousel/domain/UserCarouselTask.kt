package com.moregames.playtime.carousel.domain

import com.moregames.playtime.user.challenge.progress.ObjectiveProgressCalculatorType
import com.moregames.playtime.user.objectives.UserCurrentObjectiveProgress
import com.moregames.playtime.user.objectives.UserCurrentObjectiveProgress.Objective
import java.time.Instant
import java.util.*

sealed interface UserCarouselTask {
  val taskId: UUID
  val userId: String
  val definition: TaskDefinition

  data class New(override val taskId: UUID, override val userId: String, override val definition: TaskDefinition) : UserCarouselTask
  data class InProgress(
    override val taskId: UUID,
    override val userId: String,
    override val definition: TaskDefinition,
    override val progress: Int,
    override val achievement: String?
  ) : UserCarouselTask, UserCurrentObjectiveProgress {
    override val objective = Objective(definition.progressMax, definition.goal)
  }

  data class Unclaimed(override val taskId: UUID, override val userId: String, override val definition: TaskDefinition) : UserCarouselTask
  data class Claimed(override val taskId: UUID, override val userId: String, override val definition: TaskDefinition, val blockedUntil: Instant) :
    UserCarouselTask

  data class Completed(override val taskId: UUID, override val userId: String, override val definition: TaskDefinition) : UserCarouselTask
}

data class TaskDefinition(
  val id: String,
  val gameId: Int,
  val titleTranslation: String,
  val icon: String,
  val progressMax: Int,
  val goal: Int?,
  val order: Int,
  val calculator: ObjectiveProgressCalculatorType,
  val enabled: Boolean,
)