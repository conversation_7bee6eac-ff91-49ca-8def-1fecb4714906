package com.moregames.playtime.revenue.applovin

import com.google.inject.Inject
import com.google.inject.Provider
import com.justplayapps.service.rewarding.earnings.table.ApplovinRevenuesTable
import com.moregames.base.base.BasePersistenceService
import com.moregames.base.table.UserTable
import com.moregames.base.util.IoCoroutineScope
import com.moregames.base.util.cronLogger
import com.moregames.playtime.revenue.applovin.dto.ApplovinRevenue
import com.moregames.playtime.revenue.applovin.dto.MetaApplovinRevenue
import com.moregames.playtime.revenue.applovin.exception.NoApplovinRevenuesAvailableException
import com.moregames.playtime.revenue.applovin.table.MetaReportedApplovinRevenuesTable
import com.moregames.playtime.revenue.applovin.table.ReportedApplovinRevenuesTable
import com.moregames.playtime.util.useWithCommitAndRollback
import kotlinx.coroutines.withContext
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVPrinter
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.`java-time`.date
import org.jetbrains.exposed.sql.transactions.TransactionManager
import java.io.BufferedWriter
import java.io.File
import java.io.FileWriter
import java.math.BigDecimal
import java.sql.Connection
import java.time.LocalDate
import javax.inject.Singleton
import kotlin.use

@Singleton
class ApplovinRevenuePersistenceService @Inject constructor(
  private val database: Database,
  private val coroutineScope: Provider<IoCoroutineScope>,
) : BasePersistenceService(database) {


  @Deprecated("moved to reporting")
  suspend fun loadLatestMetaApplovinRevenue(applicationId: String) =
    dbQuery {
      MetaReportedApplovinRevenuesTable
        .select { MetaReportedApplovinRevenuesTable.applicationId eq applicationId }
        .orderBy(MetaReportedApplovinRevenuesTable.day, SortOrder.DESC)
        .limit(1)
        .map {
          MetaApplovinRevenue(
            it[MetaReportedApplovinRevenuesTable.id],
            it[MetaReportedApplovinRevenuesTable.applicationId],
            it[MetaReportedApplovinRevenuesTable.day],
            it[MetaReportedApplovinRevenuesTable.recordsLoaded]
          )
        }
        .firstOrNull()
    }

  @Deprecated("moved to reporting")
  suspend fun saveMetaApplovinRevenues(data: MetaApplovinRevenue) =
    dbQuery {
      MetaReportedApplovinRevenuesTable
        .insert {
          it[applicationId] = data.applicationId
          it[day] = data.day
          it[recordsLoaded] = data.recordsLoaded
        }
    }

  suspend fun saveApplovinRevenues(revenues: List<ApplovinRevenue>, importDay: LocalDate, applicationId: String) =
    withContext(coroutineScope.get().coroutineContext) {
      val file = File.createTempFile("applovin-import", null)

      CSVPrinter(BufferedWriter(FileWriter(file)), CSVFormat.DEFAULT.withAutoFlush(true)).use { printer ->
        revenues.forEach { printer.printRecord(it.adUnitId, it.googleAdId, it.revenueUsd, importDay, applicationId) }
      }

      val sql = "" +
        "LOAD DATA LOCAL INFILE '${file.absolutePath.replace('\\', '/')}' " +
        "INTO TABLE ${ReportedApplovinRevenuesTable.tableName} " +
        "FIELDS TERMINATED BY ',' " +
        "LINES TERMINATED BY '\\r\\n'" +
        "(${ReportedApplovinRevenuesTable.adUnitId.name}, ${ReportedApplovinRevenuesTable.googleAdId.name}, ${ReportedApplovinRevenuesTable.revenueUsd.name}, @var1, ${ReportedApplovinRevenuesTable.applicationId.name})" +
        "set ${ReportedApplovinRevenuesTable.day.name} = STR_TO_DATE(@var1, '%Y-%m-%d')"
      (database.connector().connection as Connection).useWithCommitAndRollback { conn ->
        conn.createStatement().use {
          try {
            it.execute(sql)
          } finally {
            file.delete()
          }
        }
      }
    }

  suspend fun loadImportedApplovinApplicationIds() =
    dbQuery {
      ReportedApplovinRevenuesTable
        .slice(ReportedApplovinRevenuesTable.applicationId)
        .selectAll()
        .withDistinct(true)
        .map { it[ReportedApplovinRevenuesTable.applicationId] }
        .toSet()
    }

  suspend fun loadApplovinRevenueByApplicationId(day: LocalDate) =
    dbQuery {
      ApplovinRevenuesTable
        .slice(ApplovinRevenuesTable.applicationId, ApplovinRevenuesTable.revenueUsd.sum())
        .select { ApplovinRevenuesTable.day eq day }
        .groupBy(ApplovinRevenuesTable.applicationId)
        .associate { it[ApplovinRevenuesTable.applicationId] to (it[ApplovinRevenuesTable.revenueUsd.sum()] ?: BigDecimal.ZERO) }
    }

  @Throws(NoApplovinRevenuesAvailableException::class)
  suspend fun transferApplovinRevenuesFromExternal() {
    cronLogger().info("Transferring applovin revenues. Start")
    var insertedTotal = 0
    val validUUIDChars = ('0'..'9').map { it.toString() } + ('a'..'f').map { it.toString() }
    validUUIDChars.forEach { validUUIDChar ->
      cronLogger().info("Transferring applovin revenues. Transferring data for users id starting with $validUUIDChar")
      dbQuery {
        val reportedApplovinRevenuesQuery = ReportedApplovinRevenuesTable
          .join(
            UserTable,
            JoinType.INNER,
            ReportedApplovinRevenuesTable.googleAdId,
            UserTable.googleAdId
          )
          .slice(
            UserTable.id,
            ReportedApplovinRevenuesTable.revenueUsd,
            ReportedApplovinRevenuesTable.adUnitId,
            ReportedApplovinRevenuesTable.day,
            ReportedApplovinRevenuesTable.applicationId
          )
          .select {
            (UserTable.id.like("$validUUIDChar%")) and
              (UserTable.isDeleted eq false) and
              (UserTable.createdAt.date().lessEq(ReportedApplovinRevenuesTable.day.date()))
          }
        val inserted = ApplovinRevenuesTable
          .insert(
            reportedApplovinRevenuesQuery,
            listOf(
              ApplovinRevenuesTable.userId,
              ApplovinRevenuesTable.revenueUsd,
              ApplovinRevenuesTable.adUnitId,
              ApplovinRevenuesTable.day,
              ApplovinRevenuesTable.applicationId
            )
          )
        insertedTotal += (inserted ?: 0)
        cronLogger().info("Transferring applovin revenues. Transferred $inserted applovin revenues")
      }
    }
    if (insertedTotal < 1)
      throw NoApplovinRevenuesAvailableException()
    cronLogger().info("Transferring applovin revenues. Total rows transferred $insertedTotal")
    cronLogger().info("Transferring applovin revenues. Deleting all data from external.reported_applovin_revenues")
    dbQuery {
      TransactionManager.current().exec("TRUNCATE TABLE ${ReportedApplovinRevenuesTable.tableName}")
    }
    cronLogger().info("Transferring applovin revenues. Transferring applovin revenues completed")
  }
}