package com.moregames.playtime.revenue.applovin

import com.google.inject.Inject
import com.justplayapps.service.rewarding.earnings.UserEarningsService.Companion.applovinImportThreshold
import com.moregames.base.util.cronLogger
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import java.time.ZoneOffset
import javax.inject.Singleton

@Singleton
class ApplovinUserEarningsService @Inject constructor(
  private val applovinRevenuePersistenceService: ApplovinRevenuePersistenceService,
) {

  suspend fun processImportedApplovinRevenue(ignoreThresholdValidation: Boolean = false) {
    val importedApplovinApplicationIds = applovinRevenuePersistenceService.loadImportedApplovinApplicationIds()
    if (importedApplovinApplicationIds.isEmpty()) {
      cronLogger().info("Skipping earnings calculation: No imported applovin revenue found")
      return
    }

    cronLogger().info("Found imported applovin revenue of ${importedApplovinApplicationIds.joinToString()}")
    if (!ignoreThresholdValidation) {
      val previousRevenueByApplicationId = applovinRevenuePersistenceService.loadApplovinRevenueByApplicationId(LocalDate.now(ZoneOffset.UTC).minusDays(2))
      val previousRevenueSum = previousRevenueByApplicationId.values.sumOf { it }
      val previousRevenueSumOfImportedApplicationIds =
        previousRevenueByApplicationId.filterKeys { importedApplovinApplicationIds.contains(it) }.values.sumOf { it }
      if (previousRevenueSum > BigDecimal.ZERO && previousRevenueSumOfImportedApplicationIds.divide(
          previousRevenueSum,
          RoundingMode.HALF_UP
        ) < applovinImportThreshold
      ) {
        cronLogger().info(
          "Skipping earnings calculation: Revenue of imported applovin application IDs ($previousRevenueSumOfImportedApplicationIds) is " +
            "less than ${applovinImportThreshold.multiply(BigDecimal("100"))}% of yesterdays total applovin revenue ($previousRevenueSum)"
        )
        return
      }
    }

    applovinRevenuePersistenceService.transferApplovinRevenuesFromExternal()

    cronLogger().info("Applovin CSV-revenue load was finished")
  }
}