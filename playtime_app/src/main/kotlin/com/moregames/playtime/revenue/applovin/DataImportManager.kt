package com.moregames.playtime.revenue.applovin

import com.google.inject.Inject
import com.moregames.base.dto.AppPlatform
import com.moregames.base.secret.SecretService
import com.moregames.base.util.cronLogger
import com.moregames.playtime.games.GamePersistenceService
import com.moregames.playtime.revenue.applovin.dto.ApplovinRequest
import com.moregames.playtime.revenue.applovin.dto.MetaApplovinRevenue
import com.moregames.playtime.revenue.exception.RevenuesImportFailedException
import java.time.LocalDate
import javax.inject.Singleton

// TODO: migrate to reporting (and store results in BigQuery)
@Deprecated("Use RevenueImportManager instead")
@Singleton
class DataImportManager @Inject constructor(
  private val gamePersistenceService: GamePersistenceService,
  private val applovinRevenuePersistenceService: ApplovinRevenuePersistenceService,
  private val dataFetchingService: DataFetchingService,
  private val secretService: SecretService
) {

  suspend fun onApplovinRevenuesImportTriggered() {
    var success = true
    val applovinRequests = prepareApplovinRequests()
    for (applovinRequest in applovinRequests) {
      try {
        processLoad(applovinRequest)
      } catch (e: Exception) {
        success = false
        cronLogger().error("Load for $applovinRequest failed", e)
      }
    }

    if (!success)
      throw RevenuesImportFailedException()
  }

  private suspend fun prepareApplovinRequests() = gamePersistenceService.loadGames(AppPlatform.ANDROID)
    .map {
      val apiKey = secretService.secretValue(ApplovinApiKeys.byKey(it.applovinApiKey).secret)
      ApplovinRequest(it.applicationId, apiKey)
    }

  private suspend fun processLoad(applovinRequest: ApplovinRequest) {
    val importDay = LocalDate.now().minusDays(1)

    val lastImport = applovinRevenuePersistenceService.loadLatestMetaApplovinRevenue(applovinRequest.applicationId)

    if (lastImport == null || lastImport.day < importDay) {
      val revenues = dataFetchingService.fetchApplovinRevenues(applovinRequest, importDay)

      if (revenues.isNotEmpty())
        applovinRevenuePersistenceService.saveApplovinRevenues(revenues, importDay, applovinRequest.applicationId)

      applovinRevenuePersistenceService.saveMetaApplovinRevenues(
        MetaApplovinRevenue(
          null,
          applovinRequest.applicationId,
          importDay,
          revenues.size
        )
      )
      cronLogger().info("Imported ${revenues.size} Applovin data for ${applovinRequest.applicationId} on $importDay")
    } else {
      cronLogger().info("Applovin data already imported on $importDay for ${applovinRequest.applicationId}")
    }
  }
}
