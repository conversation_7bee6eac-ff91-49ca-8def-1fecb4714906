package com.moregames.playtime.rewarding

import com.justplayapps.base.Common
import com.justplayapps.base.nonBoostedAmountUsdOrNull
import com.justplayapps.base.nonBoostedUserCurrencyAmountOrNull
import com.justplayapps.base.userCurrencyEarningsProto
import com.moregames.base.util.fromProto
import com.moregames.base.util.toProto
import com.moregames.playtime.earnings.dto.UserCurrencyEarnings
import java.util.*

fun UserCurrencyEarnings.toProto(): Common.UserCurrencyEarningsProto =
  userCurrencyEarningsProto {
    this.amountUsd = <EMAIL>()
    this.userCurrencyAmount = <EMAIL>()
    this.userCurrencyCode = <EMAIL>
    if (<EMAIL> != null) this.nonBoostedAmountUsd = <EMAIL>()
    if (<EMAIL> != null) this.nonBoostedUserCurrencyAmount = <EMAIL>()
  }

fun Common.UserCurrencyEarningsProto.fromProto(): UserCurrencyEarnings =
  UserCurrencyEarnings(
    amountUsd = this.amountUsd.fromProto(),
    userCurrency = Currency.getInstance(this.userCurrencyCode),
    userCurrencyAmount = this.userCurrencyAmount.fromProto(),
    nonBoostedAmountUsd = this.nonBoostedAmountUsdOrNull?.fromProto(),
    nonBoostedUserCurrencyAmount = this.nonBoostedUserCurrencyAmountOrNull?.fromProto(),
  )
