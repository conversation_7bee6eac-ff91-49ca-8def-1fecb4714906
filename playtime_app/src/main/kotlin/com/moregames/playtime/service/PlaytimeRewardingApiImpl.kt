package com.moregames.playtime.service

import com.google.inject.Inject
import com.google.protobuf.BoolValue
import com.google.protobuf.Empty
import com.google.protobuf.Int32Value
import com.google.protobuf.StringValue
import com.justplayapps.playtime.rewarding.proto.*
import com.justplayapps.service.rewarding.facade.fromProto
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.coins.UserQualityService
import com.moregames.base.offers.dto.OfferAction
import com.moregames.base.util.TimeService
import com.moregames.base.util.fromProto
import com.moregames.base.util.toProto
import com.moregames.playtime.cashstreak.CashStreakRewardsService
import com.moregames.playtime.checks.ExaminationPersistenceService
import com.moregames.playtime.earnings.CashoutSettingsService
import com.moregames.playtime.earnings.Em2CoinsCappingService
import com.moregames.playtime.earnings.currency.CurrencyExchangeService
import com.moregames.playtime.earnings.dto.GenericRevenueDto
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.general.SettingsService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutPeriodsConfigService
import com.moregames.playtime.user.cashout.CashoutPeriodsPersistenceService
import com.moregames.playtime.user.challenge.ChallengeRevenueService
import com.moregames.playtime.user.fraudscore.FraudScoreService
import com.moregames.playtime.user.fraudscore.HighlyTrustedUsersService
import com.moregames.playtime.user.offer.OfferService
import java.util.*
import kotlin.time.Duration.Companion.minutes
import kotlin.time.toJavaDuration

class PlaytimeRewardingApiImpl @Inject constructor(
  private val fraudScoreService: FraudScoreService,
  private val userService: UserService,
  private val cashoutSettingsService: CashoutSettingsService,
  private val examinationPersistenceService: ExaminationPersistenceService,
  private val settingsService: SettingsService,
  private val highlyTrustedUsersService: HighlyTrustedUsersService,
  private val abTestingService: AbTestingService,
  private val cashStreakRewardsService: CashStreakRewardsService,
  private val offerService: OfferService,
  private val timeService: TimeService,
  private val challengeRevenueService: ChallengeRevenueService,
  private val currencyExchangeService: CurrencyExchangeService,
  private val marketService: MarketService,
  private val cashoutPeriodsPersistenceService: CashoutPeriodsPersistenceService,
  private val userQualityService: UserQualityService,
  private val cashoutPeriodsConfigService: CashoutPeriodsConfigService,
  private val em2CoinsCappingService: Em2CoinsCappingService,
) : PlaytimeRewardingApiGrpcKt.PlaytimeRewardingApiCoroutineImplBase() {

  override suspend fun getUserData(request: PlaytimeRewarding.GetUserDataRequest): PlaytimeRewarding.GetUserDataResponse {
    val userId = request.userId
    val user = userService.getUser(userId, includingDeleted = true)
    return getUserDataResponse {
      this.isDeleted = user.isDeleted
      this.isUserBlocked = fraudScoreService.isUserBlocked(userId)
      this.fraudScore = fraudScoreService.getFraudScore(userId)
      this.isUserRestricted = fraudScoreService.isUserRestricted(userId)
      this.isUserWhitelisted = user.isWhitelisted
      this.platform = user.appPlatform.toProto()
      this.userCountryCode = user.countryCode
      userService.getUserCountryTierSettings(userId)?.let { settings ->
        this.countryTierSettings = countryTierSettingsProto {
          maxCashoutAmountMultiplier = settings.maxCashoutAmountMultiplier.toProto()
          dailyEarningsQuotas.addAll(settings.dailyEarningsQuotas.map { it.toProto() })
        }
      }
      this.userMaxEarningsAmount = cashoutSettingsService.getUserMaxEarningsAmount(userId).toProto()
      examinationPersistenceService.getCtsProfileMatchStatus(userId)?.let {
        ctsProfileMatchStatus = BoolValue.of(it)
      }
      this.iosEarningsIncreasePercentage = settingsService.getIosEarningsIncreasePercentage()
      this.isHighlyTrustedUser = highlyTrustedUsersService.isHighlyTrustedUser(userId)
      abTestingService.higherQuotasRndRange(userId)?.let {
        higherQuotasRndRange {
          lowerMultiplier = it.lowerMultiplier
          upperMultiplier = it.upperMultiplier
        }
      }
      this.currentBoosters.addAll(
        cashStreakRewardsService.getCurrentBoosters(userId).map {
          cashStreakRewardProto {
            achievementDay = it.achievementDay
            type = it.type.toProto()
            value = it.value.toProto()
            bigReward = it.bigReward
          }
        }
      )
      this.offerWallCoinsToUsdConversionRatio = cashoutSettingsService.getOfferwallCoinsToUsdConversionRatio().toProto()
      this.userCurrency = marketService.getUserCurrency(userId).currencyCode
      cashoutPeriodsConfigService.getCashoutPeriodConfig(userId).let { config ->
        this.cashoutPeriodsConfig = cashoutPeriodsConfigProto {
          firstCashoutPeriodMinutes = config.firstCashoutPeriodMinutes
          secondCashoutPeriodMinutes = config.secondCashoutPeriodMinutes
          defaultCashoutPeriodMinutes = config.defaultCashoutPeriodMinutes
          config.firstCashoutPeriodVideoOfferReward?.let { firstCashoutPeriodVideoOfferReward = Int32Value.of(it) }
        }
        val cashoutDuration = config
          .defaultCashoutPeriodMinutes.minutes
          .toJavaDuration()
        this.isWelcomeCoinsOfferCompleted =
          offerService.additionalOfferCompleted(userId, OfferAction.WELCOME_COINS, after = (timeService.now() - cashoutDuration))
      }
      this.isEuCountry = marketService.isEUCountry(user.countryCode)
      this.coinGoal =
        // without this check it's 100% UserRecordNotFoundException for deleted user. and goal value irrelevant if user deleted
        if (!user.isDeleted) userService.loadCoinGoalUser(userId).coinsGoal
        else 0
      cashoutPeriodsPersistenceService.getCurrentCashoutPeriod(userId)?.let { currentCashoutPeriod ->
        this.cashoutPeriodCounter = Int32Value.of(currentCashoutPeriod.counter)
        this.coinGoalMilestones.addAll(currentCashoutPeriod.coinGoalMilestones)
      }
      settingsService.getExpEarningsUserFilter()?.let { filter ->
        this.expEarningsUserFilter = StringValue.of(filter)
      }
      userQualityService.getUserQuality(userId)?.let { quality ->
        this.userQuality = quality.toProto()
      }
      this.currentToFirstEcpmRatio = userQualityService.getCurrentToFirstEcpmRatio(userId).toProto()
    }
  }

  override suspend fun getConvertedUsdToUserCurrency(request: PlaytimeRewarding.GetConvertedUsdToUserCurrencyRequest): PlaytimeRewarding.GetConvertedUsdToUserCurrencyResponse {
    return currencyExchangeService.convert(request.amountUsd.fromProto(), Currency.getInstance(request.userCurrency)).let {
      getConvertedUsdToUserCurrencyResponse {
        this.usdAmount = it.usdAmount.toProto()
        this.userCurrency = it.userCurrency.currencyCode
        this.amount = it.amount.toProto()
        this.amountNoRounding = it.amountNoRounding.toProto()
      }
    }
  }

  override suspend fun getRevenueWithChallengesCut(request: PlaytimeRewarding.GetRevenueWithChallengesCutRequest): PlaytimeRewarding.GetRevenueWithChallengesCutResponse {
    return getRevenueWithChallengesCutResponse {
      this.revenue = challengeRevenueService.revenueWithChallengesCut(
        request.userId,
        request.revenuesList.map {
          GenericRevenueDto(
            id = it.id,
            userId = it.userId,
            source = it.source.fromProto(),
            timestamp = it.timestamp.fromProto(),
            amount = it.amount.fromProto(),
            amountExtra = if (it.hasAmountExtra()) it.amountExtra.fromProto() else null,
            gameId = if (it.hasGameId()) it.gameId.value else null,
          )
        }
      ).toProto()
    }
  }

  override suspend fun getOfferwallCoinsToUsdConversionRatio(request: Empty): PlaytimeRewarding.GetOfferwallCoinsToUsdConversionRatioResponse {
    return getOfferwallCoinsToUsdConversionRatioResponse {
      this.ratio = cashoutSettingsService.getOfferwallCoinsToUsdConversionRatio().toProto()
    }
  }

  override suspend fun calculateCappedGameRevenue(request: PlaytimeRewarding.CalculateCappedGameRevenueRequest): PlaytimeRewarding.CalculateCappedGameRevenueResponse {
    return calculateCappedGameRevenueResponse {
      this.revenue = em2CoinsCappingService.calculateCappedGameRevenue(
        userId = request.userId,
        gameCoins = request.gameCoins.fromProto(),
        coinsForOneDollar = request.coinsForOneDollar.fromProto(),
        gamesRealRevenues = request.gamesRealRevenuesList.map { it.fromProto() },
      ).toProto()
    }
  }
}