package com.moregames.playtime.subscribers

import com.google.inject.Inject
import com.justplayapps.playtime.proto.unpaidUserEarningsAddedEvent
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment.ANDROID_INCOMPLETE_CASHOUT_RESTORING
import com.moregames.base.bus.MessageBus
import com.moregames.base.messaging.dto.EarningsAddedEventDto
import com.moregames.base.messaging.push.GenericPushSubscriber
import com.moregames.playtime.buseffects.PushNotificationEffect
import com.moregames.playtime.earnings.dto.UserCurrencyEarnings
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.ContinueIncompleteCashoutNotification
import com.moregames.playtime.notifications.PushNotification.CrossPlatformPushNotification.EarningsAddedNotification
import com.moregames.playtime.rewarding.toProto
import com.moregames.playtime.user.UserCheckManager
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutService
import com.moregames.playtime.user.cashout.IncompleteCashoutService
import com.moregames.playtime.util.roundDownToSecondDigit
import java.math.BigDecimal.ZERO

class EarningsAddedMessagePushSubscriber @Inject constructor(
  private val userCheckManager: UserCheckManager,
  private val userService: UserService,
  private val cashoutService: CashoutService,
  private val abTestingService: AbTestingService,
  private val incompleteCashoutService: IncompleteCashoutService,
  private val messageBus: MessageBus,
) : GenericPushSubscriber<EarningsAddedEventDto>(EarningsAddedEventDto::class) {

  override suspend fun handle(message: EarningsAddedEventDto) {
    if (!userService.userExists(message.userId)) return

    val availableUserEarnings = cashoutService.getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(message.userId)

    if (availableUserEarnings.userCurrencyAmount.roundDownToSecondDigit() > ZERO) {
      sendEarningsAddedPushNotification(message, availableUserEarnings)
    }

    messageBus.publish(
      unpaidUserEarningsAddedEvent {
        this.userId = message.userId
        this.earnings = availableUserEarnings.toProto()
      }
    )

    userCheckManager.onUserEarningsCalculated(message.metaId)
  }

  private suspend fun sendEarningsAddedPushNotification(message: EarningsAddedEventDto, availableUserEarnings: UserCurrencyEarnings) {
    val notification = if (abTestingService.isUserExperimentParticipant(message.userId, ANDROID_INCOMPLETE_CASHOUT_RESTORING)
      && incompleteCashoutService.hasTrackedInitiatedCashouts(message.userId)
    ) {
      ContinueIncompleteCashoutNotification(
        userId = message.userId,
        earnings = availableUserEarnings
      )
    } else {
      EarningsAddedNotification(
        userId = message.userId,
        earnings = availableUserEarnings,
        userHasCashouts = cashoutService.userHasCashouts(message.userId)
      )
    }

    messageBus.publishAsync(PushNotificationEffect(notification))
  }

  override val url: String = EarningsAddedEventDto.TOPIC_NAME
}