package com.moregames.playtime.subscribers

import com.justplayapps.playtime.proto.unpaidUserEarningsAddedEvent
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.MessageBus
import com.moregames.base.messaging.push.GenericPushSubscriber
import com.moregames.playtime.buseffects.PushNotificationEffect
import com.moregames.playtime.earnings.dto.EarningsAddedBqEventDto
import com.moregames.playtime.earnings.dto.UserCurrencyEarnings
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.RewardEarningsAddedNotification
import com.moregames.playtime.rewarding.toProto
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.RewardSpecialChallengesEarningsAddedNotification
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutService
import com.moregames.playtime.user.challenge.dto.ChallengeType
import com.moregames.playtime.user.challenge.dto.RewardEarningsAddedEventDto
import java.math.BigDecimal.ZERO
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class RewardEarningsAddedEventSubscriber @Inject constructor(
  private val userService: UserService,
  private val cashoutService: CashoutService,
  private val messageBus: MessageBus,
  private val bigQueryEventPublisher: BigQueryEventPublisher,
) : GenericPushSubscriber<RewardEarningsAddedEventDto>(RewardEarningsAddedEventDto::class) {

  override suspend fun handle(message: RewardEarningsAddedEventDto) {
    if (!userService.userExists(message.userId)) return

    if (message.amountUserCurrency > ZERO) {
      val reward = UserCurrencyEarnings(
        amountUsd = message.amount,
        userCurrency = Currency.getInstance(message.userCurrencyCode),
        userCurrencyAmount = message.amountUserCurrency
      )
      sendEarningsAddedPushNotification(message, reward)
    }

    val availableUserEarnings = cashoutService.getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(message.userId)
    messageBus.publish(
      unpaidUserEarningsAddedEvent {
        this.userId = message.userId
        this.earnings = availableUserEarnings.toProto()
      }
    )
    val earningsEvent = with(message) {
      EarningsAddedBqEventDto(
        metaId = metaId,
        userId = userId,
        amount = amount,
        challengeEventId = challengeEventId,
        createdAt = createdAt
      )
    }

    bigQueryEventPublisher.publish(earningsEvent)
  }

  private suspend fun sendEarningsAddedPushNotification(message: RewardEarningsAddedEventDto, earningsReward: UserCurrencyEarnings) {
    val locale = userService.getUser(message.userId).locale
    val notification = when(message.challengeType) {
      ChallengeType.REGULAR, null -> RewardEarningsAddedNotification(message.userId, earningsReward, locale)
      ChallengeType.SPECIAL -> RewardSpecialChallengesEarningsAddedNotification(message.userId, earningsReward, locale)
    }

    messageBus.publishAsync(PushNotificationEffect(notification))
  }

  override val url: String = RewardEarningsAddedEventDto.TOPIC_NAME
}