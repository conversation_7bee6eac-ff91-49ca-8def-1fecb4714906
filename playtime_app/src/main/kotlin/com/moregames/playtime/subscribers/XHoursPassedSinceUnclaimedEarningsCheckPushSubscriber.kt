package com.moregames.playtime.subscribers

import com.google.inject.Inject
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.variations.ModifiedUnclaimedEarningsNotificationVariation
import com.moregames.base.bus.MessageBus
import com.moregames.base.messaging.dto.XHoursPassedSinceUnclaimedEarningsCheckDto
import com.moregames.base.messaging.push.GenericPushSubscriber
import com.moregames.base.util.RandomGenerator
import com.moregames.base.util.TimeService
import com.moregames.playtime.buseffects.PushNotificationEffect
import com.moregames.playtime.earnings.dto.UserCurrencyEarnings
import com.moregames.playtime.notifications.PushNotification.CrossPlatformPushNotification.EarningsAddedNotification
import com.moregames.playtime.notifications.PushNotification.CrossPlatformPushNotification.UnclaimedEarningNotification
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutService
import com.moregames.playtime.user.timezone.UserTimeZoneService
import com.moregames.playtime.util.roundDownToSecondDigit
import java.math.BigDecimal
import java.time.temporal.ChronoUnit

private typealias Message = XHoursPassedSinceUnclaimedEarningsCheckDto

class XHoursPassedSinceUnclaimedEarningsCheckPushSubscriber @Inject constructor(
  private val rewardingFacade: RewardingFacade,
  private val timeService: TimeService,
  private val userService: UserService,
  private val cashoutService: CashoutService,
  private val abTestingService: AbTestingService,
  private val userTimeZoneService: UserTimeZoneService,
  private val randomGenerator: RandomGenerator,
  private val messageBus: MessageBus,
) : GenericPushSubscriber<XHoursPassedSinceUnclaimedEarningsCheckDto>(XHoursPassedSinceUnclaimedEarningsCheckDto::class) {

  override suspend fun handle(message: XHoursPassedSinceUnclaimedEarningsCheckDto) {
    if (!userService.userExists(message.userId)) return
    val unclaimedEarnings = cashoutService.getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(message.userId)
    if (unclaimedEarnings.userCurrencyAmount.roundDownToSecondDigit() > BigDecimal.ZERO &&
      rewardingFacade.noEarningsAfter(message.userId, message.fromDate)
    ) {
      notifyUserAndCreateNewTask(message, unclaimedEarnings)
    }
  }

  private suspend fun notifyUserAndCreateNewTask(message: Message, unclaimedEarnings: UserCurrencyEarnings) = with(userTimeZoneService) {
    val modifiedNotificationVariation = abTestingService.assignedVariationValue(message.userId, ClientExperiment.MODIFIED_UNCLAIMED_EARNINGS_NOTIFICATION)
    val now = timeService.now()

    if (modifiedNotificationVariation != DEFAULT && !now.isInBusinessHours(message.userId)) { // rescheduling notification, if it was sent outside of business hours
      messageBus.publish(message, now.closestBusinessHour(message.userId))
      return@with
    }

    if (modifiedNotificationVariation is ModifiedUnclaimedEarningsNotificationVariation) {
      sendModifiedNotification(message)
      when (modifiedNotificationVariation) {
        ModifiedUnclaimedEarningsNotificationVariation.EnhancedDefaultCycle -> rescheduleAsUsual(message)
        ModifiedUnclaimedEarningsNotificationVariation.Enhanced24hCycle -> rescheduleIn24Hours(message)
      }
      return@with
    }

    // non-experimental behavior below
    messageBus.publishAsync(
      PushNotificationEffect(
        EarningsAddedNotification(
          userId = message.userId,
          earnings = unclaimedEarnings,
          userHasCashouts = cashoutService.userHasCashouts(message.userId)
        )
      )
    )
    rescheduleAsUsual(message)
  }

  private fun sendModifiedNotification(message: Message) {
    val (title, text) = experimentalTexts.random(randomGenerator.getRandom())
    messageBus.publishAsync(
      PushNotificationEffect(
        UnclaimedEarningNotification(
          userId = message.userId,
          title = title,
          text = text,
        )
      )
    )
  }

  private suspend fun rescheduleIn24Hours(message: Message) {
    if (message.counter >= 5) return // send notification 5 times

    messageBus.publish(
      message.copy(counter = message.counter + 1),
      timeService.now().plus(24, ChronoUnit.HOURS)
    )
  }

  private suspend fun rescheduleAsUsual(message: Message) {
    if (message.counter >= delays.size) return

    val delay = delays[message.counter]
    messageBus.publish(
      message.copy(counter = message.counter + 1),
      timeService.now().plus(delay, ChronoUnit.HOURS)
    )
  }

  override val url: String = "x-hours-passed-since-unclaimed-earnings"

  companion object {
    val delays = listOf(0L) + (1..15).map { 3L } + (1..4).map { 12L }

    val experimentalTexts = listOf(
      "\uD83D\uDCA5 Cash In Your Wins!" to "You’ve got earnings waiting for you! Tap here to cash out and enjoy your rewards!",
      "\uD83C\uDF81 Grab Your Rewards Now!" to "Your hard-earned money is ready to be cashed out. Don’t miss out!",
      "\uD83D\uDCB8 Instant Cash-Out Available!" to "You’ve earned it! Head to the app now to claim what’s yours!",
      "\uD83C\uDFC6 It’s Payout Time!" to "Your earnings are ready for you! Cash out now and enjoy your rewards instantly!",
    )
  }
}