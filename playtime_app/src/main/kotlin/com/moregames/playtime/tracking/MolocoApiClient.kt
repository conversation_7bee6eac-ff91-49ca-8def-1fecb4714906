package com.moregames.playtime.tracking

import com.google.inject.name.Named
import com.moregames.base.app.BuildVariant
import com.moregames.base.dto.AppPlatform.*
import com.moregames.base.secret.SecretService
import com.moregames.base.util.Constants.JUSTPLAY_APPLICATION_ID
import com.moregames.base.util.logger
import com.moregames.playtime.app.CoreModule.Companion.MOLOCO_HTTP_CLIENT
import com.moregames.playtime.app.PlaytimeSecrets
import com.moregames.playtime.app.messaging.dto.MolocoInAppEvent
import io.github.resilience4j.circuitbreaker.CircuitBreaker
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig.SlidingWindowType.COUNT_BASED
import io.github.resilience4j.kotlin.circuitbreaker.executeSuspendFunction
import io.ktor.client.*
import io.ktor.client.features.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import kotlinx.serialization.json.JsonPrimitive
import java.time.Duration
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class MolocoApiClient @Inject constructor(
  @Named(MOLOCO_HTTP_CLIENT) private val httpClient: HttpClient,
  private val secretService: SecretService,
  private val buildVariant: BuildVariant,
) {
  companion object {
    const val API_BASE_URL = "https://ingestfnt.adsmoloco.com/app-event/v1/apps/"
    const val IOS_APP_ID = "6444946155"
    const val API_EVENTS_ENDPOINT = "/events"
    const val API_KEY_HEADER = "X-API-Key"
    val circuitBreaker = CircuitBreaker.of(
      "moloco", CircuitBreakerConfig.custom()
      .automaticTransitionFromOpenToHalfOpenEnabled(true)
      .failureRateThreshold(50f)
      .slidingWindowType(COUNT_BASED)
      .slidingWindowSize(10)
      .minimumNumberOfCalls(10)
      .permittedNumberOfCallsInHalfOpenState(5)
      .waitDurationInOpenState(Duration.ofSeconds(60))
      .recordException {
        when (it) {
          is ClientRequestException -> it.response.status.value in arrayOf(400, 401, 403)
          is ServerResponseException -> true
          is HttpRequestTimeoutException -> true
          else -> false
        }
      }
      .build()
    ).apply {
      eventPublisher.onStateTransition {
        logger().info("Moloco circuit breaker state changed from ${it.stateTransition.fromState} to ${it.stateTransition.toState}")
      }
    }
  }

  suspend fun sendInAppEvent(
    inAppEvent: MolocoInAppEvent
  ): HttpResponse? {
    if (buildVariant != BuildVariant.PRODUCTION) {
      return null
    }
    val platformDependentParams = preparePlatformDependentData(inAppEvent)
    val apiAddress = API_BASE_URL + platformDependentParams.appId + API_EVENTS_ENDPOINT

    return try {
      circuitBreaker.executeSuspendFunction {
        httpClient.post(apiAddress) {
          header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
          header(API_KEY_HEADER, secretService.secretValue(PlaytimeSecrets.MOLOCO_S2S_AUTH))
          body = InAppEventDto(
            id = UUID.randomUUID().toString(),
            app = InAppEventDto.App(platformDependentParams.appId),
            device = InAppEventDto.Device(
              ip = inAppEvent.ipAddress,
              ua = inAppEvent.userAgent,
              os = platformDependentParams.osName,
              ifv = platformDependentParams.ifv,
              ifa = platformDependentParams.ifa
            ),
            timestamp = inAppEvent.timestamp,
            eventName = inAppEvent.type.eventName,
            eventType = inAppEvent.type.eventType,
            eventProps = InAppEventDto.EventProps(
              revenue = InAppEventDto.Money(currency = "USD", amount = JsonPrimitive(inAppEvent.amount))
            )
          )
        }
      }
    } catch (e: ClientRequestException) {
      logger().warn("Client error occurred: ${e.response.status}", e)
      throw e
    } catch (e: ServerResponseException) {
      logger().warn("Server error occurred: ${e.response.status}", e)
      throw e
    }
  }

  private fun preparePlatformDependentData(inAppEvent: MolocoInAppEvent): PlatformData {
    val platformDependentParams = when (inAppEvent.platform) {
      IOS, IOS_WEB -> PlatformData(
        appId = IOS_APP_ID,
        osName = IOS.name,
        ifv = inAppEvent.idfv,
        ifa = inAppEvent.idfa
      )

      ANDROID -> PlatformData(
        appId = JUSTPLAY_APPLICATION_ID,
        osName = ANDROID.name,
        ifv = inAppEvent.idfv,
        ifa = inAppEvent.idfa
      )
    }
    return platformDependentParams
  }

  data class PlatformData(
    val appId: String,
    val osName: String,
    val ifv: String? = null,
    val ifa: String? = null
  )
}