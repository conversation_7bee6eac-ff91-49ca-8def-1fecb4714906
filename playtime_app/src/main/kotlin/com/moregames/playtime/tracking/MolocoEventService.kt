package com.moregames.playtime.tracking

import com.google.inject.Inject
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.TrackingDataType
import com.moregames.base.util.TimeService
import com.moregames.base.util.logger
import com.moregames.playtime.app.messaging.dto.EventType
import com.moregames.playtime.app.messaging.dto.MolocoInAppEvent
import com.moregames.playtime.buseffects.SendMolocoInAppEventEffect
import com.moregames.playtime.user.UserPersistenceService
import java.math.BigDecimal
import javax.inject.Singleton

@Singleton
class MolocoEventService @Inject constructor(
  private val timeService: TimeService,
  private val userPersistenceService: UserPersistenceService,
  private val adjustService: AdjustService,
  private val messageBus: MessageBus
) {

  suspend fun sendEvent(
    userId: String,
    revenue: BigDecimal,
    eventType: EventType
  ) {
    val userExternalIds = userPersistenceService.fetchExternalIds(userId)
    userExternalIds?.trackingData?.let { trackingData ->
      val idfa = if (trackingData.type == TrackingDataType.IDFA) trackingData.id else userExternalIds.idfa
      val idfv = if (trackingData.type == TrackingDataType.IDFV) trackingData.id else null

      if (idfa != null || idfv != null) {
        val molocoEvent = MolocoInAppEvent(
          ipAddress = adjustService.getUserIp(userId),
          userAgent = adjustService.getUserAgent(userId),
          idfa = idfa,
          idfv = idfv,
          platform = trackingData.platform,
          amount = revenue,
          timestamp = timeService.now().toEpochMilli(),
          type = eventType
        )
        messageBus.publishAsync(SendMolocoInAppEventEffect(molocoEvent))
      } else {
        logger().info("[MOLOCO] No IDFA or IDFV. Event not sent for userId = $userId")
      }
    }
  }
}