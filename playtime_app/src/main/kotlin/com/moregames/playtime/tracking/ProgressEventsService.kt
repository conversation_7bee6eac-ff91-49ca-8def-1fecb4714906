package com.moregames.playtime.tracking

import com.moregames.base.dto.AppPlatform
import com.moregames.base.util.ApplicationId
import com.moregames.base.util.TimeService
import com.moregames.playtime.user.UserService
import java.time.Duration
import javax.inject.Inject

class ProgressEventsService @Inject constructor(
  private val adMarketService: AdMarketService,
  private val userService: UserService,
  private val timeService: TimeService,
) {

  suspend fun createAndSendProgressEvents(userId: String, applicationId: String, progress: Int) {
    val progressThresholdsToEvents = gameProgressReachedEventThresholds[applicationId] ?: return
    val user = userService.getUser(userId, includingDeleted = true)
    if (Duration.between(user.createdAt, timeService.now()).toDays() > DAYS_TO_FIRE_PROGRESS_EVENTS) return

    val marketEvents = buildList {
      if (user.appPlatform == AppPlatform.ANDROID) {
        progressThresholdsToEvents
          .filter { (progressReached, _) -> progressReached <= progress }
          .map { (_, eventName) -> add(eventName) }
      }
    }.ifEmpty { return }

    adMarketService.sendMarketEvents(userId, marketEvents)
  }

  private companion object {
    const val DAYS_TO_FIRE_PROGRESS_EVENTS = 30
    val gameProgressReachedEventThresholds = mapOf(
      ApplicationId.TREASURE_MASTER_APP_ID to listOf(3, 5, 10, 15, 20).map { it to "action_beat_${it}_stages_treasure_master" },
      ApplicationId.SOLITAIRE_VERSE_APP_ID to listOf(3, 5, 10, 15, 20).map { it to "action_win_${it}_games_solitaire_verse" },
      ApplicationId.WATER_SORTER_APP_ID to listOf(3, 5, 10, 15, 20).map { it to "action_complete_${it}_lvls_water_sorter" },
      ApplicationId.SPACE_CONNECT_APP_ID to listOf(3, 5, 10, 15, 20).map { it to "action_complete_${it}_lvls_space_connect" },
      ApplicationId.MAD_SMASH_APP_ID to listOf(3, 5, 10, 15, 20).map { it to "action_complete_${it}_lvls_mad_smash" },
      ApplicationId.BLOCKBUSTER_APP_ID to listOf(3, 5, 10, 15, 20).map { it to "action_win_${it}_games_in_block_buster" },
      ApplicationId.PUZZLE_POP_BLASTER_APP_ID to listOf(3, 5, 10, 15, 20).map { it to "action_complete_${it}_lvls_puzzle_pop" },
      ApplicationId.WORD_SEEKER_APP_ID to listOf(3, 5, 10, 15, 20).map { it to "action_complete_${it}_lvls_word_seeker" },
      ApplicationId.TANGRAM_APP_ID to listOf(3, 5, 10, 15, 20).map { it to "action_complete_${it}_lvls_tangram_heaven" },
      ApplicationId.TILE_MATCH_PRO_APP_ID to listOf(3, 5, 10, 15, 20).map { it to "action_complete_${it}_lvls_title_match" },
    )
  }
}