package com.moregames.playtime.user

import com.google.inject.Inject
import com.google.inject.Provider
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.app.BuildVariant
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.MessageBus
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.messaging.dto.GameProgressBqEventDto
import com.moregames.base.messaging.dto.RevenueReceivedEventDto
import com.moregames.base.user.dto.CreateUserData
import com.moregames.base.user.dto.ExperimentVariationKey
import com.moregames.base.user.dto.UserDefinitionRules
import com.moregames.base.util.RandomGenerator
import com.moregames.base.util.TimeService
import com.moregames.playtime.administration.qa.QaUserSetting
import com.moregames.playtime.administration.qa.QaUserSettingsService
import com.moregames.playtime.checks.ExaminationService
import com.moregames.playtime.games.GamePersistenceService
import com.moregames.playtime.user.attestation.UserAttestationPersistenceService
import com.moregames.playtime.user.fraudscore.FraudScorePersistenceService
import com.moregames.playtime.user.offer.AndroidOfferwallPersistenceService
import com.moregames.playtime.user.verification.VerificationPersistenceService
import com.moregames.playtime.user.verification.dto.VerificationSessionDto
import java.time.temporal.ChronoUnit
import javax.inject.Singleton

@Singleton
class CreateUserOverwriteService @Inject constructor(
  private val buildVariantProvider: Provider<BuildVariant>,
  private val androidOfferwallPersistenceService: AndroidOfferwallPersistenceService,
  private val verificationPersistenceService: VerificationPersistenceService,
  private val timeService: TimeService,
  private val fraudScorePersistenceService: FraudScorePersistenceService,
  private val messageBus: MessageBus,
  private val bigQueryEventPublisher: BigQueryEventPublisher,
  private val randomUUIDGenerator: RandomGenerator,
  private val gamePersistenceService: GamePersistenceService,
  private val examinationService: ExaminationService,
  private val abTestingService: AbTestingService,
  private val qaUserSettingsService: QaUserSettingsService,
  private val applicationConfig: ApplicationConfig,
  private val userAttestationPersistenceService: UserAttestationPersistenceService,
) {
  fun redefineCreateUserData(createUserData: CreateUserData): CreateUserData {
    if (buildVariantProvider.get() == BuildVariant.PRODUCTION)
      throw IllegalStateException("Not allowed in production mode")

    if (createUserData.userRequestDto?.userDefinitionRules == null) return createUserData

    val metadata = createUserData.userRequestMetadata
    val requestDto = createUserData.userRequestDto
    val rules = requestDto?.userDefinitionRules!!

    return createUserData.copy(
      userRequestMetadata = metadata.copy(
        ip = rules.ip ?: metadata.ip,
        forwardedIp = rules.ip ?: metadata.forwardedIp,
        countryCode = rules.countryCode ?: metadata.countryCode,
      ),
      userRequestDto = requestDto.copy(
        networkCountry = rules.networkCountry ?: requestDto.networkCountry,
        simCountry = rules.simCountry ?: requestDto.simCountry,
        deviceLocale = rules.deviceLocale ?: requestDto.deviceLocale,
        userDefinitionRules = rules,
        //if we have some rules, we don't want actual values to affect further logic somehow, so let's remove sim list
        simInfoList = requestDto.simInfoList.takeIf { rules.simCountry == null && rules.networkCountry == null }
      )
    )
  }

  suspend fun afterGettingUserIdActions(rules: UserDefinitionRules, userId: String) {
    if (buildVariantProvider.get() == BuildVariant.PRODUCTION)
      throw IllegalStateException("Not allowed in production mode")

    listOfNotNull(
      if (rules.skipOnboarding == true) QaUserSetting.SKIP_ONBOARDING else null,
      if (rules.speedUpCashoutPeriodEnd == true) QaUserSetting.SPEED_UP_CASHOUT_PERIOD_END else null,
      if (rules.restrictBan == true) QaUserSetting.RESTRICT_BAN else null,
      if (rules.useUsualCashoutPeriod == true) QaUserSetting.USE_USUAL_CASHOUT_PERIODS else null
    ).also { qaUserSettingsService.setUserSettings(userId, it) }
  }

  suspend fun postUserCreatedActions(createUserData: CreateUserData, userId: String) {
    if (buildVariantProvider.get() == BuildVariant.PRODUCTION)
      throw IllegalStateException("Not allowed in production mode")

    if (createUserData.userRequestDto?.userDefinitionRules == null) return

    val rules = createUserData.userRequestDto?.userDefinitionRules

    if (rules?.deviceAttestationPassed == true || rules?.strongAttestationPassed == true) {
      examinationService.markUserAsPassedDeviceAttestation(userId)
    }

    if (rules?.strongAttestationPassed == true)
      userAttestationPersistenceService.saveUserPassedStrongAttestation(userId)

    rules?.facetecVerificationSessionId?.let { sessionId ->
      verificationPersistenceService.createSession(
        VerificationSessionDto(
          sessionId = sessionId,
          userId = userId,
          expiredAt = timeService.now().plus(60, ChronoUnit.MINUTES),
          verification = listOf(
            VerificationSessionDto.VerificationStep(
              type = VerificationSessionDto.VerificationType.FACE,
              status = VerificationSessionDto.VerificationStatus.VERIFIED,
              order = 1
            )
          )
        )
      )
    }

    rules?.frozenFraudScore?.let { frozenScore ->
      fraudScorePersistenceService.setFrozenFraudScore(userId, frozenScore.toDouble())
    }

    rules?.coinsToAdd?.let { coins ->
      val game = gamePersistenceService.loadVisibleGames("en", createUserData.appVersion.platform).first()

      // differs from QA coins - no new entry in ReportedGameProgressTable
      bigQueryEventPublisher.publish(
        GameProgressBqEventDto(
          eventId = randomUUIDGenerator.nextUUID(),
          userId = userId,
          gameId = game.id,
          coins = coins,
          method = "REPLACE",
          applicationIdParam = game.applicationId,
          market = applicationConfig.justplayMarket,
          platform = createUserData.appVersion.platform.name,
          createdAt = timeService.now(),
        )
      )
    }

    rules?.revenueToAdd?.let { amount ->
      messageBus.publish(
        RevenueReceivedEventDto(
          eventId = randomUUIDGenerator.nextUUID(),
          userId = userId,
          timestamp = timeService.now(),
          source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
          amount = amount,
          networkId = -1,
          gameId = null,
          createdAt = timeService.now()
        )
      )
    }

    rules?.offerWallType?.let {
      androidOfferwallPersistenceService.trackOfferwallTypesReturnResult(userId, listOf(it))
    }
  }

  suspend fun applyExperiments(userId: String, experiments: Set<ExperimentVariationKey>?) {
    if (buildVariantProvider.get() == BuildVariant.PRODUCTION)
      throw IllegalStateException("Not allowed in production mode")

    if (experiments.isNullOrEmpty()) return

    val existingVariations = abTestingService.loadAllExpVariations()
    val existingKeysToVariations = existingVariations.associateBy { ExperimentVariationKey(it.experiment.key, it.key) }
    val existingVariationsKeys = existingKeysToVariations.keys

    experiments
      .minus(existingVariationsKeys)
      .takeIf { it.isNotEmpty() }
      ?.let { throw IllegalStateException("Variation(s) unknown or client-dependant for $userId: $it") }

    experiments.forEach {
      val variation = existingKeysToVariations[it]!!

      abTestingService.createOrUpdateParticipant(
        userId = userId,
        variation = variation,
        activated = false
      )
    }
  }
}
