package com.moregames.playtime.user

import com.google.inject.Inject
import com.google.inject.Provider
import com.moregames.base.dto.AppPlatform
import com.moregames.base.user.RevenueTotals
import com.moregames.base.util.ApplicationId
import com.moregames.base.util.DefaultCoroutineScope
import com.moregames.base.util.TimeService
import com.moregames.base.util.logger
import com.moregames.playtime.app.messaging.dto.EventType
import com.moregames.playtime.games.GamePersistenceService
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.general.TrackedEventsPersistenceService
import com.moregames.playtime.general.dto.TrackedEvent
import com.moregames.playtime.general.dto.TrackedEvent.Companion.buildTrackedEventsForAllPlatforms
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.tracking.AdMarketService
import com.moregames.playtime.tracking.MolocoEventService
import com.moregames.playtime.user.attestation.UserAttestationPersistenceService
import com.moregames.playtime.user.cashout.CashoutPeriodCounters
import com.moregames.playtime.user.cashout.CashoutPeriodsPersistenceService
import com.moregames.playtime.user.cashout.CashoutPersistenceService
import com.moregames.playtime.user.cashout.dto.CashoutTransactionDto
import com.moregames.playtime.user.fraudscore.FraudScoreService
import kotlinx.coroutines.launch
import java.math.BigDecimal
import java.time.Duration
import java.time.temporal.ChronoUnit
import javax.inject.Singleton
import kotlin.time.Duration.Companion.minutes

@Singleton
class UserCheckManager @Inject constructor(
  private val userService: UserService,
  private val userSuspicionService: UserSuspicionService,
  private val rewardingFacade: RewardingFacade,
  private val cashoutPersistenceService: CashoutPersistenceService,
  private val fraudScoreService: FraudScoreService,
  private val timeService: TimeService,
  private val trackedEventsPersistenceService: TrackedEventsPersistenceService,
  private val coroutineScope: Provider<DefaultCoroutineScope>,
  private val adMarketService: AdMarketService,
  private val cashoutPeriodsPersistenceService: CashoutPeriodsPersistenceService,
  private val gamePersistenceService: GamePersistenceService,
  private val gamesService: GamesService,
  private val userAttestationPersistenceService: UserAttestationPersistenceService,
  private val molocoEventService: MolocoEventService
) {

  companion object {
    val anyGameBannersShownThresholds = mapOf(
      AppPlatform.IOS to listOf(
        2.minutes to "2_min",
        5.minutes to "5_min",
        10.minutes to "10_min"
      ).map { (duration, postfix) -> duration.asThreshold(postfix) },

      AppPlatform.ANDROID to listOf(
        2.minutes to "2_min",
        5.minutes to "5_min",
        10.minutes to "10_min",
        20.minutes to "20_min",
        30.minutes to "30_min",
        40.minutes to "40_min",
      ).map { (duration, postfix) -> duration.asThreshold(postfix) }
    )

    val particularGameBannersShownThresholds = mapOf(
      AppPlatform.ANDROID to listOf(
        5.minutes to "5min",
        10.minutes to "10min",
        15.minutes to "15min",
      ),
    )

    val particularGamesBannerShownAppIdToEventNamePrefix = mapOf(
      ApplicationId.SOLITAIRE_VERSE_APP_ID to "play_solitaire",
      ApplicationId.TREASURE_MASTER_APP_ID to "play_treasuremaster",
      ApplicationId.MAD_SMASH_APP_ID to "play_madsmash",
      ApplicationId.BUBBLE_POP_APP_ID to "play_bubblepop",
      ApplicationId.WATER_SORTER_APP_ID to "play_watersorter",
    )

    val totalCashoutLimit = BigDecimal("300")
    const val MAX_DUPLICATE_CASHOUT_EMAIL_COUNT = 20
    const val MAX_TRACKED_EVENT_CHUNK_SIZE = 10000
    const val REVENUE_TRACK_THRESHOLD_DAYS = 8
    const val DAYS_TO_FIRE_PLAYED_ANY_GAME_X_MIN_EVENTS = 30
    const val DAYS_TO_FIRE_PLAYED_PARTICULAR_GAME_X_MIN_EVENTS = 7
    val cashout2ReachedXEventCountryToUSDThreshold = mapOf(
      "US" to 2.00,
      "GB" to 1.40,
      "AU" to 1.40,
      "CH" to 1.35,
      "DE" to 1.35,
      "CA" to 1.30,
      "NZ" to 1.20,
      "NL" to 1.10,
      "NO" to 1.05,
      "DK" to 1.05,
      "SE" to 1.00,
      "SG" to 0.90,
      "FI" to 0.85,
      "IE" to 0.70,
      "FR" to 0.60,
      "PL" to 0.55,
      "BE" to 0.50,
      "IT" to 0.50,
      "ES" to 0.35,
      "PT" to 0.30
    )

    private val earningsReachedMinXEventsThresholds =
      listOf(
        BigDecimal(30.0) to "earnings_reached_min_30",
        BigDecimal(20.0) to "earnings_reached_min_20",
        BigDecimal(15.0) to "earnings_reached_min_15",
        BigDecimal(10.0) to "earnings_reached_min_10",
        BigDecimal(9.0) to "earnings_reached_min_9",
        BigDecimal(8.0) to "earnings_reached_min_8",
        BigDecimal(7.0) to "earnings_reached_min_7",
        BigDecimal(6.0) to "earnings_reached_min_6",
        BigDecimal(5.0) to "earnings_reached_min_5",
        BigDecimal(4.0) to "earnings_reached_min_4",
        BigDecimal(3.0) to "earnings_reached_min_3",
        BigDecimal(2.0) to "earnings_reached_min_2",
        BigDecimal(1.0) to "earnings_reached_min_1",
        BigDecimal(0.5) to "earnings_reached_min_05",
      )

    private fun kotlin.time.Duration.asThreshold(postfix: String) = Threshold(inWholeSeconds..(inWholeSeconds + 59), postfix)
  }

  fun onBannersShown(userId: String, gameId: Int, timeElapsedInSeconds: Int) {

    coroutineScope.get().launch {
      if (!userService.userExists(userId)) return@launch
      val user = userService.getUser(userId)
      if (user.isBanned) return@launch

      val userActivityDays = Duration.between(user.createdAt, timeService.now()).toDays()

      val eventList = buildList {

        if (userActivityDays <= DAYS_TO_FIRE_PLAYED_ANY_GAME_X_MIN_EVENTS && anyGameBannersShownThresholds.getValue(user.appPlatform).map { it.range }.any { range -> timeElapsedInSeconds in range }) {
          val gamePlayedOrderNumber = gamePersistenceService.getGamesOrderedByFirstTimeBannerShow(userId)
            .indexOfFirst { it.first == gameId } + 1
          if (gamePlayedOrderNumber in 1..5) {
            val eventName = buildGamePlayedForXMinEventName(gamePlayedOrderNumber, timeElapsedInSeconds, user.appPlatform)
            if (eventName != null) add(eventName)
          }
        }

        if (userActivityDays <= DAYS_TO_FIRE_PLAYED_PARTICULAR_GAME_X_MIN_EVENTS && user.appPlatform == AppPlatform.ANDROID) {
          val gameIdToEventNamePrefix = particularGamesBannerShownAppIdToEventNamePrefix.mapKeys { gamesService.getGameId(it.key, user.appPlatform) }
          val eventNamePrefix = gameIdToEventNamePrefix[gameId]
          if (eventNamePrefix != null) {
            particularGameBannersShownThresholds.getValue(user.appPlatform)
              .filter { (timeToPlay, _) -> timeToPlay.inWholeSeconds <= timeElapsedInSeconds }
              .forEach { (_, postfix) -> add("${eventNamePrefix}_${postfix}") }
          }
        }

      }

      if (eventList.isEmpty()) return@launch

      trackedEventsPersistenceService.addTrackedEvents(eventList.flatMap { buildTrackedEventsForAllPlatforms(userId = userId, eventName = it) })
      try {
        adMarketService.sendMarketEvents(userId, eventList)
      } catch (e: Exception) {
        logger().warn("Failed to send market events ${eventList.joinToString()} for the user = $userId: ", e)
      }
    }
  }

  fun onUserEarningsCalculated(metaId: Int) {
    coroutineScope.get().launch {
      val newTrackedEventsList = mutableListOf<TrackedEvent>()
      val marketsEventsList = mutableListOf<String>()
      val userEarnings = rewardingFacade.loadUserEarningsForMetaId(metaId) ?: return@launch
      val userId = userEarnings.userId

      if (!userService.userExists(userId)) return@launch
      val user = userService.getUser(userEarnings.userId)
      if (user.isBanned) return@launch

      val now = timeService.now()

      // https://app.asana.com/0/1155692811605665/1200337690960862/f
      val cashoutPeriodStartAtUserCreationDate = user.createdAt.minus(2, ChronoUnit.MINUTES) // Just to let day counter be tolerant to minor fluctuations

      val userActivityDays = Duration.between(cashoutPeriodStartAtUserCreationDate, now).toDays()

      createRevenueEvents(userActivityDays, userId, newTrackedEventsList, marketsEventsList)

      createEarningsEvents(user, userActivityDays, userId, marketsEventsList)

      newTrackedEventsList.chunked(MAX_TRACKED_EVENT_CHUNK_SIZE).forEach { chunkList -> trackedEventsPersistenceService.addTrackedEvents(chunkList) }

      try {
        adMarketService.sendMarketEvents(userId, marketsEventsList)
      } catch (e: Exception) {
        logger().warn("Failed to send market events for user(s) = $userId: $marketsEventsList", e)
      }
    }
  }

  private suspend fun createEarningsEvents(
    user: UserDto,
    userActivityDays: Long,
    userId: String,
    marketsEventsList: MutableList<String>
  ) {
    if (user.appPlatform == AppPlatform.ANDROID && userActivityDays <= 30) {
      val totalUserEarnings = rewardingFacade.getTotalUsdEarningsForUser(userId)
      earningsReachedMinXEventsThresholds
        .dropWhile { (earningsReached, _) -> earningsReached > totalUserEarnings }
        .forEach { (_, eventName) -> marketsEventsList.add(eventName) }
    }
  }

  private suspend fun createRevenueEvents(
    userActivityDays: Long,
    userId: String,
    newTrackedEventsList: MutableList<TrackedEvent>,
    marketsEventsList: MutableList<String>
  ) {
    // We don't have any logic down the road that works for users that are older than 8 days
    if (userActivityDays > REVENUE_TRACK_THRESHOLD_DAYS) return

      val (userRevenue, userOfferwallRevenue, userDay2Revenue) =
        rewardingFacade.getRevenueTotals(userId)
          ?: RevenueTotals(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO)

      val hasSuccessfulCashout = cashoutPersistenceService.hasSuccessCashouts(userId)
      val (_, _, activePeriodsCounter) = cashoutPeriodsPersistenceService.getCurrentCashoutPeriodCounters(userId)
        ?: CashoutPeriodCounters(0, 0, 0)

      if (userActivityDays <= 2) {
        // https://app.asana.com/0/1155692811605665/1204250405976541
        checkAndGenerateAmountReachedXEvent(userId, userRevenue, newTrackedEventsList, marketsEventsList)
        // https://app.asana.com/0/1155692811605665/1203548261448172/f
        listOf(0.1, 0.2, 0.5, 0.75).forEach { revenueThreshold ->
          if (userRevenue >= BigDecimal.valueOf(revenueThreshold)) {
            val eventName = buildEventName("$revenueThreshold", 2)
            newTrackedEventsList.add(TrackedEvent(userId = userId, eventName = eventName, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
            marketsEventsList.add(eventName)
          }
        }

        (1..4).forEach { revenueThreshold ->
          if (userRevenue >= BigDecimal(revenueThreshold)) {
            val eventName = buildEventName("$revenueThreshold", 2)
            newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
            marketsEventsList.add(eventName)
          }
        }

        if (userRevenue >= BigDecimal.ONE) {
          // https://app.asana.com/0/0/1200337690960862/1200478714912073/f
          val trackedEvent =
            TrackedEvent(userId = userId, eventName = "fb_mobile_level_achieved", platformsToTrack = TrackedEvent.EventTrackingPlatform.FACEBOOK)
          newTrackedEventsList.add(trackedEvent)
        }
      }

      if (userRevenue >= BigDecimal(8)) {
        val eventName = buildEventName("8", 8)
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userRevenue >= BigDecimal(13)) {
        val eventName = buildEventName("13", 8)
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      // new ua events 2023-12
      if (userDay2Revenue >= BigDecimal(3) && userActivityDays >= 4) {
        val eventName = "rev_on_day_2_reached_3_AND_day_4_retention"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userDay2Revenue >= BigDecimal(3) && userActivityDays >= 5) {
        val eventName = "rev_on_day_2_reached_3_AND_day_5_retention"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userDay2Revenue >= BigDecimal(4) && userActivityDays >= 6) {
        val eventName = "rev_on_day_2_reached_4_AND_day_6_retention"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userDay2Revenue >= BigDecimal(2) && userActivityDays >= 5 && hasSuccessfulCashout) {
        val eventName = "rev_on_day_2_reached_2_AND_day_5_retention_AND_successful_cashout"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userDay2Revenue >= BigDecimal(4) && userActivityDays >= 4 && hasSuccessfulCashout) {
        val eventName = "rev_on_day_2_reached_4_AND_day_4_retention_AND_successful_cashout"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userDay2Revenue >= BigDecimal(3) && userActivityDays >= 5 && hasSuccessfulCashout) {
        val eventName = "rev_on_day_2_reached_3_AND_day_5_retention_AND_successful_cashout"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userRevenue >= BigDecimal(6) && userActivityDays in 5..6 && hasSuccessfulCashout) {
        val eventName = "rev_on_day_6_reached_6_AND_day_5_retention_AND_successful_cashout"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userRevenue >= BigDecimal(6) && userActivityDays in 5..6) {
        val eventName = "rev_on_day_6_reached_6_AND_day_5_retention"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userRevenue >= BigDecimal(8) && userActivityDays in 5..6 && hasSuccessfulCashout) {
        val eventName = "rev_on_day_6_reached_8_AND_day_5_retention_AND_successful_cashout"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userRevenue >= BigDecimal(8) && userActivityDays in 5..6) {
        val eventName = "rev_on_day_6_reached_8_AND_day_5_retention"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userRevenue >= BigDecimal(4) && userActivityDays in 5..6 && hasSuccessfulCashout) {
        val eventName = "rev_on_day_6_reached_4_AND_day_5_retention_AND_successful_cashout"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (activePeriodsCounter >= 10) {
        val eventName = "10_active_CPs"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userActivityDays <= 2 && userRevenue >= BigDecimal(3) && activePeriodsCounter >= 10) {
        val eventName = "rev_on_day_2_reached_3_AND_10_active_CPs"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userActivityDays <= 2 && userRevenue >= BigDecimal(4) && activePeriodsCounter >= 9) {
        val eventName = "rev_on_day_2_reached_4_AND_9_active_CPs"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userActivityDays <= 6 && userRevenue >= BigDecimal(6) && activePeriodsCounter >= 9) {
        val eventName = "rev_on_day_6_reached_6_AND_9_active_CPs"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userActivityDays in 3..6 && userRevenue >= BigDecimal(4) && hasSuccessfulCashout && userOfferwallRevenue > BigDecimal.ZERO) {
        val eventName = "rev_on_day_6_reached_4_AND_offerwall_revenue_any_amount_AND_successful_cashout_AND_day_3_retention"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userActivityDays in 3..6 && userRevenue >= BigDecimal(6) && hasSuccessfulCashout && userOfferwallRevenue > BigDecimal.ZERO) {
        val eventName = "rev_on_day_6_reached_6_AND_offerwall_revenue_any_amount_AND_successful_cashout_AND_day_3_retention"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userActivityDays in 3..6 && userRevenue >= BigDecimal(8) && hasSuccessfulCashout && userOfferwallRevenue > BigDecimal.ZERO) {
        val eventName = "rev_on_day_6_reached_8_AND_offerwall_revenue_any_amount_AND_successful_cashout_AND_day_3_retention"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userActivityDays in 3..6 && userRevenue >= BigDecimal(4) && userOfferwallRevenue > BigDecimal.ZERO) {
        val eventName = "rev_on_day_6_reached_4_AND_offerwall_revenue_any_amount_AND_day_3_retention"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userActivityDays <= 6 && userOfferwallRevenue >= BigDecimal(4) && hasSuccessfulCashout) {
        val eventName = "offerwall_revenue_on_day_6_reached_4_AND_successful_cashout"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userActivityDays <= 6 && userOfferwallRevenue >= BigDecimal(6)) {
        val eventName = "offerwall_revenue_on_day_6_reached_6"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userOfferwallRevenue > BigDecimal.ZERO) {
        val eventName = "offerwall_revenue_any_amount"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
        molocoEventService.sendEvent(userId, userOfferwallRevenue, EventType.OW_WITH_REVENUE)
      }
      // new ua events 2024-02
      if (activePeriodsCounter >= 10 && hasSuccessfulCashout) {
        val eventName = "10_active_CPs_AND_successful_cashout"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (activePeriodsCounter >= 10 && userActivityDays >= 4 && hasSuccessfulCashout) {
        val eventName = "10_active_CPs_AND_day_4_retention_AND_successful_cashout"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userActivityDays >= 4 && hasSuccessfulCashout) {
        val eventName = "day_4_retention_AND_successful_cashout"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (hasSuccessfulCashout) {
        val eventName = "successful_cashout"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userActivityDays >= 1 && hasSuccessfulCashout) {
        val eventName = "day_1_retention_AND_successful_cashout"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userActivityDays >= 2 && hasSuccessfulCashout) {
        val eventName = "day_2_retention_AND_successful_cashout"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
      if (userActivityDays >= 3 && hasSuccessfulCashout) {
        val eventName = "day_3_retention_AND_successful_cashout"
        newTrackedEventsList.addAll(buildTrackedEventsForAllPlatforms(userId = userId, eventName = eventName))
        marketsEventsList.add(eventName)
      }
  }

  private suspend fun checkAndGenerateAmountReachedXEvent(
    userId: String,
    userRevenue: BigDecimal,
    newTrackedEventsList: MutableList<TrackedEvent>,
    marketsEventsList: MutableList<String>
  ) {
    cashout2ReachedXEventCountryToUSDThreshold[userService.getUserCountryCode(userId)]?.let {
      if (userRevenue >= BigDecimal.valueOf(it)) {
        val eventName = "cashout_2_reached_x"
        newTrackedEventsList.add(TrackedEvent(userId = userId, eventName = eventName, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
        marketsEventsList.add(eventName)
      }
    }
  }

  fun onSuccessfulCashout(cashoutTransaction: CashoutTransactionDto) = with(cashoutTransaction) {
    coroutineScope.get().launch {
      val totalCashouts = cashoutPersistenceService.calculateTotalUsdCashoutsForUser(userId)
      val totalRevenue = rewardingFacade.getRevenueTotals(userId)?.revenue
      if (totalRevenue != null && totalCashouts >= totalRevenue)
        userSuspicionService.createUserSuspicion(
          userId,
          UserSuspicions.CASHOUT_MORE_THAN_REVENUE,
          "User has cashed out more money than the revenue they generated"
        )

      val totalCashoutsForEmail = cashoutPersistenceService.calculateTotalCashoutsForEmail(emailHash)
      if (totalCashoutsForEmail >= totalCashoutLimit)
        userSuspicionService.createUserSuspicion(
          userId,
          UserSuspicions.CASHOUT_MORE_THAN_MAX_AMOUNT,
          "Users with hashed email $emailHash have cashed out more money (\$$totalCashoutsForEmail) than the maximum allowed amount (\$$totalCashoutLimit)"
        )

      val duplicateEmails = cashoutPersistenceService.countUsersWithSameEmail(userId, emailHash)
      if (duplicateEmails > MAX_DUPLICATE_CASHOUT_EMAIL_COUNT) {
        userSuspicionService.createUserSuspicion(
          userId,
          UserSuspicions.DUPLICATE_CASHOUT_EMAIL,
          "Found $duplicateEmails users with same hashed email '${emailHash}'"
        )
      }

      userIp.let {
        val cashoutAmount = cashoutPersistenceService.countCashoutSumWithSameIp(userIp)
        if (cashoutAmount > totalCashoutLimit)
          userSuspicionService.createUserSuspicion(
            userId,
            UserSuspicions.CASHOUT_MORE_THAN_MAX_AMOUNT_FOR_IP,
            "Users with ip $userIp have cashed out combined more money ($cashoutAmount) than the maximum allowed amount (\$$totalCashoutLimit)"
          )
      }

      fraudScoreService.onSuccessfulCashout(userId, provider)
      fraudScoreService.freezeFraudScore(userId, hasSuccessfulCashout = true)
    }
  }

  fun onUserFaceVerified(userId: String) {
    coroutineScope.get().launch {
      fraudScoreService.onUserFaceVerified(userId)
    }
  }

  fun onUserFaceUniquenessCheckFailed(userId: String) {
    coroutineScope.get().launch {
      fraudScoreService.onUserFaceUniquenessCheckFailed(userId)
    }
  }

  suspend fun onUserPassedStrongAttestationCheck(userId: String) {
    userAttestationPersistenceService.saveUserPassedStrongAttestation(userId)
    coroutineScope.get().launch {
      fraudScoreService.checkCountriesAndRemoveFS(userId)
    }
  }


  private fun buildEventName(revenueThreshold: String, userActivityDays: Long) =
    when (revenueThreshold) {
      "0.1" -> "cashout_${userActivityDays}_reached_01"
      "0.2" -> "cashout_${userActivityDays}_reached_02"
      "0.5" -> "cashout_${userActivityDays}_reached_05"
      "0.75" -> "cashout_${userActivityDays}_reached_075"
      "1" -> "cashout_${userActivityDays}_reached_1"
      "2" -> "cashout_${userActivityDays}_reached_2"
      "3" -> "cashout_${userActivityDays}_reached_3"
      "4" -> "cashout_${userActivityDays}_reached_4"
      "8" -> "cashout_${userActivityDays}_reached_8"
      "13" -> "cashout_${userActivityDays}_reached_13"
      else -> {
        logger().error("Unknown revenueThreshold : $revenueThreshold")
        "Unknown revenueThreshold : $revenueThreshold"
      }
    }

  private fun buildGamePlayedForXMinEventName(gamePlayedOrderNumber: Int, timeElapsedInSeconds: Int, appPlatform: AppPlatform): String? {
    val prefix = when (gamePlayedOrderNumber) {
      1 -> "1st"
      2 -> "2nd"
      3 -> "3rd"
      4 -> "4th"
      5 -> "5th"
      else -> {
        logger().error("Unexpected game played order number while generating ad market event name!")
        null
      }
    }
    return anyGameBannersShownThresholds.getValue(appPlatform).find { timeElapsedInSeconds in it.range }?.let {
      "${prefix}_game_played_${it.postfix}"
    }
  }

  data class Threshold(val range: LongRange, val postfix: String)
}
