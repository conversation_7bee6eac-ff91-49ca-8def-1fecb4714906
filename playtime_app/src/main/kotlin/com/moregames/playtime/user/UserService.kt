package com.moregames.playtime.user

import com.google.inject.Inject
import com.google.inject.Provider
import com.justplayapps.playtime.proto.userCreatedEvent
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.app.BuildVariant
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.MessageBus
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppPlatform.*
import com.moregames.base.dto.AppVersionDto
import com.moregames.base.dto.TrackingDataType
import com.moregames.base.encryption.EncryptionService
import com.moregames.base.exceptions.CountryIsNotAllowedException
import com.moregames.base.exceptions.UserRecordNotFoundException
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.ipregistry.IpData
import com.moregames.base.mail.MailAddress
import com.moregames.base.mail.MailService
import com.moregames.base.messaging.dto.*
import com.moregames.base.user.dto.CreateUserData
import com.moregames.base.user.dto.UserRequestMetadata
import com.moregames.base.util.ClientVersionsSupport.getHardUpdateVersion
import com.moregames.base.util.DefaultCoroutineScope
import com.moregames.base.util.TimeService
import com.moregames.base.util.logger
import com.moregames.playtime.administration.user.PersonalDataService
import com.moregames.playtime.app.androidAllowLazyApplovinInitialization
import com.moregames.playtime.buseffects.*
import com.moregames.playtime.buseffects.FraudEffect.*
import com.moregames.playtime.checks.IpService
import com.moregames.playtime.checks.dto.SanityValue
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.notifications.NotificationType
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.user.ActiveUsersService.Companion.MAX_INACTIVITY_DAYS
import com.moregames.playtime.user.PopupMessageReason.LIMITED_AD_TRACKING
import com.moregames.playtime.user.UserEcpmGroupsService.DefineUsersEcpmGroupEffect
import com.moregames.playtime.user.UserPersistenceService.GamePlayStatusDto
import com.moregames.playtime.user.cashout.CashoutPeriodsPersistenceService
import com.moregames.playtime.user.cashout.CashoutPersistenceService
import com.moregames.playtime.user.dto.*
import com.moregames.playtime.user.exception.IncompatibleVersionException
import com.moregames.playtime.user.tracking.TrackingData
import com.moregames.playtime.user.tracking.TrackingService
import com.moregames.playtime.webhook.adjust.AdjustEventPersistenceService
import kotlinx.coroutines.launch
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit
import java.util.*
import javax.inject.Singleton
import kotlin.time.Duration.Companion.days

@Singleton
class UserService @Inject constructor(
  private val abTestingService: AbTestingService,
  private val userPersistenceService: UserPersistenceService,
  private val coroutineScope: Provider<DefaultCoroutineScope>,
  private val marketService: MarketService,
  private val messageBus: MessageBus,
  private val bigQueryEventPublisher: BigQueryEventPublisher,
  private val ipService: IpService,
  private val rewardingFacade: RewardingFacade,
  private val timeService: TimeService,
  private val trackingService: TrackingService,
  private val cashoutPeriodsPersistenceService: CashoutPeriodsPersistenceService,
  private val adjustEventPersistenceService: AdjustEventPersistenceService,
  private val cashoutPersistenceService: CashoutPersistenceService,
  private val personalDataService: PersonalDataService,
  private val mailService: MailService,
  private val buildVariantProvider: Provider<BuildVariant>,
  private val applicationConfig: ApplicationConfig,
  private val encryptionService: EncryptionService,
  private val featureFlagsFacade: FeatureFlagsFacade,
  private val userDataCache: UserDataCache,
) {

  companion object {
    const val DEFAULT_RANDOMLY_COIN_REWARD = 100
    const val MINUTES_TO_RANDOMLY_COIN_REWARD = 15L

    // probably Firebase bans suspicious devices itself: https://justplayapps.slack.com/archives/C01MTD2KFM5/p1682074083123279
    // and if this "device_token" is not handled, Firebase would return errors on notifications sending try
    const val DEVICE_TOKEN_BLACKLISTED = "BLACKLISTED"
    const val DAYS_TO_CALCULATE_ECPM_GROUPS_THRESHOLDS = 7L
  }

  suspend fun onUserSpecificRequest(
    userId: String,
    userRequestMetadata: UserRequestMetadata,
    appVersion: AppVersionDto,
    ipData: IpData? = null,
    locale: Locale? = null,
    market: String?,
  ) {
    val userData = getUser(userId)

    checkForHardUpdate(appVersion)

    val calculatedIpData = ipData ?: ipService.extractIpData(userRequestMetadata)
    validateCountry(userId, appVersion.platform, calculatedIpData.countryCode)

    coroutineScope.get().launch {
      if (userData.lastActiveAtDay != LocalDate.now()) {
        if (userData.lastActiveAtDay != null && userData.lastActiveAtDay.isBefore(LocalDate.now().minusDays(MAX_INACTIVITY_DAYS))) {
          logger().info("User: $userId was reactivated! Last active at day was: ${userData.lastActiveAtDay}")
        }
        messageBus.publish(OnFirstLaunchOfAppTodayEffect(userData, locale))
      }

      val oldAppVersion = AppVersionDto(userData.appPlatform, userData.appVersion)
      if (oldAppVersion != appVersion) {
        userPersistenceService.updateUserAppVersion(userId, appVersion)
        userPersistenceService.logUserAppVersionChange(userId, appVersion)
        if (oldAppVersion.platform != appVersion.platform) {
          logger().warn("Application platform change detected. UserId:$userId, previousPlatform:${oldAppVersion.platform.name}, newPlatform:${appVersion.platform.name}")
          messageBus.publish(PlatformChanged(userId))
        } else if (oldAppVersion.version != appVersion.version) {
          messageBus.publish(AppVersionChanged(userId, appVersion, oldAppVersion.version))
        }
      }

      calculatedIpData.let {
        if (it.doNotTrackIp) return@let
        val newIp = userPersistenceService.saveUserIpReturnNew(userId, it.ip, it.countryCode)
        if (newIp) {
          messageBus.publish(NewUserIp(userId, it.ip, it.countryCode))
        }
      }

      if (market == null) {
        logger().warn("Header 'X-Playtime-Market' is null for userId = '$userId'")
      }
    }
  }

  fun onNewUserCreated(userId: String, createUserData: CreateUserData) {
    if (createUserData.userRequestDto == null) return

    listOf(
      NewUserFraudEffect(userId, createUserData),
      AfterXMinUserCreationEventEffect(userId),
      InstallGamesWhenNoCoinsEffect(userId)
    )
      .forEach { messageBus.publishAsync(it) }
  }

  suspend fun onUserSanityCheck(userId: String, sanityValue: SanityValue) {
    if (sanityValue == SanityValue.IS_USING_VPN) {
      userPersistenceService.markUserAsConnectedViaVpn(userId)
      messageBus.publishAsync(VpnUsage(userId))
    }
  }

  suspend fun updateValidGoogleAdId(userId: String, googleAdId: String, appVersion: Int?) {
    if (trackingService.isGoogleAdIdWhitelisted(googleAdId)) {
      whitelistUser(userId)
    }
    trackingService.updateGoogleAdId(userId, googleAdId)
    messageBus.publishAsync(NewGoogleAdId(userId, googleAdId, appVersion))
    messageBus.publish(
      TrackingDataUpdatedEventDto(
        userId = userId,
        trackingId = googleAdId,
        trackingType = TrackingDataType.IDFA.name,
        appPlatform = ANDROID.name,
        platform = ANDROID.name,
        market = applicationConfig.justplayMarket,
        createdAt = timeService.now()
      )
    )
  }

  suspend fun updateGoogleAdId(userId: String, googleAdId: String, appVersion: AppVersionDto) {
    //prevent unuseful duplicates in audit
    if (googleAdId == userPersistenceService.fetchGoogleAdId(userId)) return

    when {
      trackingService.isStubGoogleAdId(googleAdId) -> {
        onLimitedTrackingUser(userId, appVersion)
      }

      trackingService.isValidGoogleAdId(googleAdId) -> {
        updateValidGoogleAdId(userId = userId, googleAdId = googleAdId, appVersion = appVersion.version)
        messageBus.publishAsync(DeletePopupMessageEffect(userId, LIMITED_AD_TRACKING))
      }

      else -> {
        // something wrong with gaid
        logger().error("Invalid gaid '$googleAdId' for user '$userId'")
      }
    }
  }

  suspend fun updateTrackingData(userId: String, trackingData: TrackingData, appVersion: AppVersionDto) {
    //prevent unuseful duplicates in audit
    if (trackingData == userPersistenceService.fetchTrackingData(userId)) return

    when {
      trackingService.isStubTrackingId(trackingData.id) -> {
        onLimitedTrackingUser(userId, appVersion)
      }

      trackingService.isValidTrackingId(trackingData.id) -> {
        updateValidTrackingData(userId = userId, trackingData = trackingData)
        messageBus.publishAsync(DeletePopupMessageEffect(userId, LIMITED_AD_TRACKING))
      }

      else -> {
        logger().error("Invalid tracking data '$trackingData' for user '$userId'")
      }
    }
  }

  private suspend fun onLimitedTrackingUser(userId: String, appVersion: AppVersionDto) {
    userPersistenceService.setLimitedTrackingUser(userId)
    if (appVersion.platform == ANDROID) return
    messageBus.publishAsync(CreateLatPopupMessageEffect(userId, getUser(userId).locale))
  }

  suspend fun updateValidTrackingData(userId: String, trackingData: TrackingData) {
    if (trackingService.isTrackingDataWhitelisted(trackingData)) {
      whitelistUser(userId)
    }
    trackingService.changeUserCurrentTrackingData(userId, trackingData)
    messageBus.publishAsync(NewTrackingData(userId, trackingData))
    messageBus.publish(
      TrackingDataUpdatedEventDto(
        userId = userId,
        trackingId = trackingData.id,
        trackingType = trackingData.type.name,
        appPlatform = trackingData.platform.name,
        platform = trackingData.platform.name,
        market = applicationConfig.justplayMarket,
        createdAt = timeService.now()
      )
    )
  }

  suspend fun updateAdjustId(userId: String, adjustId: String) {
    userPersistenceService.updateAdjustId(userId, adjustId)
    // if we have an installation event saved in DB with the same adjustId but with different userId
    // means that this is re-installation, so we won't have new adjust install event and have to use
    // this old one from previous user to run related checks and fraud score adjustments
    adjustEventPersistenceService.loadLastAdjustInstallationByAdjustId(adjustId)
      ?.takeIf { it.userId != userId }
      ?.copy(userId = userId, googleAdId = null, idfv = null)
      ?.also { messageBus.publishAsync(AdjustDataReceived(it.userId, it)) }
    bigQueryEventPublisher.publish(AdjustIdTrackedEventDto(userId = userId, adjustId = adjustId, createdAt = timeService.now()))
  }

  suspend fun getAdjustId(userId: String): String? = userPersistenceService.getAdjustId(userId)
  
  suspend fun addOrUpdateFirebaseAppInstanceId(userId: String, firebaseAppInstanceId: String, platform: AppPlatform?) =
    userPersistenceService.addOrUpdateFirebaseAppInstanceId(userId, firebaseAppInstanceId, platform)

  suspend fun createUser(countryCode: String, data: CreateUserData): String {
    val userId = userPersistenceService.createUser(countryCode, data)
    messageBus.publish(
      userCreatedEvent {
        this.userId = userId
      }
    )
    val createdAt = timeService.now()
    messageBus.publish(
      UserCreatedEventDto(
        userId = userId,
        market = applicationConfig.justplayMarket,
        createdAt = createdAt,
        countryCode = countryCode,
        platform = data.appVersion.platform
      )
    )
    if (data.userRequestDto?.deviceSpecification != null) {
      bigQueryEventPublisher.publish(
        UserCreatedDeviceSpecsBqEventDto(
          userId = userId,
          createdAt = createdAt,
          osVersion = data.userRequestDto?.deviceSpecification?.osVersion,
          modelName = data.userRequestDto?.deviceSpecification?.modelName,
          ramSize = data.userRequestDto?.deviceSpecification?.ramSize,
          fontScale = data.userRequestDto?.deviceSpecification?.fontScale?.toBigDecimal(),
          density = data.userRequestDto?.deviceSpecification?.density,
          densityScaleFactor = data.userRequestDto?.deviceSpecification?.densityScaleFactor?.toBigDecimal(),
          appPlatform = data.appVersion.platform.name
        )
      )
    }
    return userId
  }

  suspend fun loadCoinGoalUser(userId: String) =
    userPersistenceService.loadCoinGoalUser(userId)

  suspend fun getUser(userId: String, includingDeleted: Boolean = false): UserDto =
    userDataCache.getUserData(userId, includingDeleted)

  suspend fun countUsersWithSameGoogleAdId(userId: String) =
    userPersistenceService.countUsersWithSameGoogleAdId(userId)

  suspend fun countUsersWithSameGoogleAdId(userId: String, googleAdId: String) =
    userPersistenceService.countUsersWithSameGoogleAdId(userId, googleAdId)

  suspend fun countUsersWithSameTrackingData(userId: String, trackingData: TrackingData) =
    userPersistenceService.countUsersWithSameTrackingData(userId, trackingData)

  suspend fun whitelistGoogleAdId(googleAdId: String) {
    trackingService.addGoogleAdIdToWhitelist(googleAdId)
    val userId = userPersistenceService.fetchUserId(trackingId = googleAdId)
    whitelistUser(userId)
  }

  suspend fun whitelistTrackingData(trackingData: TrackingData) {
    val userId = userPersistenceService.fetchUserId(trackingData)
    trackingService.addTrackingDataToWhitelist(trackingData)
    whitelistUser(userId)
  }

  suspend fun whitelistUser(userId: String) {
    userPersistenceService.whitelistUser(userId)
  }

  suspend fun userExists(userId: String): Boolean = userPersistenceService.userExists(userId)

  suspend fun updateDeviceToken(userId: String, deviceToken: String, appPlatform: AppPlatform) {
    val deviceTokenFiltered = if (deviceToken == DEVICE_TOKEN_BLACKLISTED) "" else deviceToken

    userPersistenceService.updateDeviceToken(userId, deviceTokenFiltered)
    messageBus.publish(DeviceTokenUpdatedEventDto(userId = userId, deviceToken = deviceTokenFiltered, appPlatform = appPlatform))
  }

  suspend fun fetchAllUserIds(googleAdId: String) = userPersistenceService.fetchAllUserIds(googleAdId)

  suspend fun markUserAsDeleted(userId: String) = userPersistenceService.markUserAsDeleted(userId)

  suspend fun obfuscateUserDataPersonals(userId: String): Int = userPersistenceService.obfuscateUserPersonals(userId)

  suspend fun obfuscateUserGaidTrackingPersonals(userId: String): Int =
    trackingService.obfuscateUserGaidTrackingPersonals(userId)

  suspend fun getLatestActivityTime(userId: String): Instant? =
    rewardingFacade.getLatestRevenueTime(userId)
      ?: (userPersistenceService.loadUserAppVersionAndLastActivityDay(userId).second
        ?.atStartOfDay()
        ?.toInstant(ZoneOffset.UTC)
        )

  suspend fun getLatestAppActivityTime(userId: String): Instant? =
    userPersistenceService.loadUserAppVersionAndLastActivityDay(userId).second
      ?.atStartOfDay()
      ?.toInstant(ZoneOffset.UTC)

  suspend fun getAppVersion(userId: String): AppVersionDto =
    userPersistenceService.loadUserAppVersionAndLastActivityDay(userId).first

  suspend fun noRecentNotifications(userId: String, type: NotificationType, forDate: Instant? = null) =
    userPersistenceService.getLastNotificationDateByType(userId, type)
      ?.isBefore((forDate ?: timeService.now()).minus(1, ChronoUnit.DAYS))
      ?: true

  suspend fun hasPreviousNotification(userId: String, type: NotificationType) =
    userPersistenceService.getLastNotificationDateByType(userId, type) != null

  suspend fun trackNotification(userId: String, type: NotificationType) =
    userPersistenceService.trackUserNotification(userId, type, timeService.now())

  suspend fun fetchExternalIds(userId: String): UserExternalIds? =
    userDataCache.fetchExternalIds(userId)

  suspend fun scheduleOnEmptyCPEndNotifications(userId: String, notificationsLeft: Int) {
    userPersistenceService.updateUserOnEmptyCPNotificationTable(userId, notificationsLeft)
  }

  suspend fun shouldNotifyOnEmptyCPNotification(userId: String): Boolean =
    userPersistenceService.reduceAmountOfNotificationsOnEmptyCPByOneReturnRowsUpdated(userId) == 1

  suspend fun getGdprState(userId: String): GdprStateDto {
    return userPersistenceService.getUserCountryCode(userId)
      .let { marketService.isGdprAppliesToCountry(it) }
      .let { GdprStateDto(it) }
  }

  // loads non-inflated coin goal
  suspend fun loadCoinGoal(userId: String): Int? =
    cashoutPeriodsPersistenceService
      .getCurrentCashoutPeriod(userId)
      ?.coinGoal

  suspend fun loadUserGameCoins(userId: String): Map<Int, GamePlayStatusDto> =
    if (abTestingService.isEm2Participant(userId)) {
      userPersistenceService.loadRoundedPerGameCoinsForEm2User(userId)
    } else {
      userPersistenceService.loadPerGameCoinsForUser(userId)
    }
      .associateBy { it.gameId }

  suspend fun addUserTrackingData(userId: String, trackingData: TrackingData) {
    trackingService.getLatestTrackingData(userId, trackingData.type, trackingData.platform).let {
      if (it == trackingData) return
    }
    trackingService.addTrackingData(userId, trackingData)
    messageBus.publish(
      TrackingDataUpdatedEventDto(
        userId = userId,
        trackingId = trackingData.id,
        trackingType = trackingData.type.name,
        appPlatform = trackingData.platform.name,
        platform = trackingData.platform.name,
        market = applicationConfig.justplayMarket,
        createdAt = timeService.now()
      )
    )
  }

  suspend fun updateGpsLocationCountry(userId: String, country: String) = userPersistenceService.updateGpsLocationCountry(userId, country)

  suspend fun fetchUserId(trackingId: String): String =
    userPersistenceService.fetchUserId(trackingId)

  suspend fun fetchUserIds(trackingIds: List<String>): List<UserPersistenceService.UserIdAndTrackingId> =
    userPersistenceService.fetchUserIds(trackingIds)

  suspend fun getCoinGoalUser(userId: String): User? =
    try {
      userPersistenceService.loadCoinGoalUser(userId)
    } catch (_: UserRecordNotFoundException) {
      null
    }

  suspend fun requestUserDeletion(userId: String) {
    val googleAdId = userPersistenceService.fetchGoogleAdId(userId)
    val cashoutTransactions = cashoutPersistenceService.loadTransactions(userId)
    val hasCashoutData = cashoutTransactions.isNotEmpty()
    userPersistenceService.requestUserDeletion(userId, hasCashoutData)
    if (hasCashoutData) { // ask support to delete user and face data after a period of time
      val userEmail = encryptionService.decryptOrEmpty(cashoutTransactions.last().encryptedEmail)
      val market = applicationConfig.justplayMarket
      mailService.sendMail(
        sender = MailAddress("<EMAIL>", "Reporting"),
        subject = "${if (buildVariantProvider.get() == BuildVariant.PRODUCTION) "" else "[TEST] "}Please delete account $userId",
        content = "Please delete my account and all its associated data\n" +
          "My user ID $userId\n" +
          "My market $market\n" +
          "My googleAdId $googleAdId\n" +
          "My email $userEmail\n",
        attachments = null,
        MailAddress("<EMAIL>")
      )
    } else { // delete automatically
      personalDataService.deletePersonalsForUser(userId, googleAdId)
    }
  }

  suspend fun updateUserConsent(userId: String, consentApiDto: ConsentApiDto) =
    userPersistenceService.updateUserConsent(userId, consentApiDto)

  suspend fun useAndroidLazyApplovinInitialization(userId: String): Boolean {
    if (!featureFlagsFacade.androidAllowLazyApplovinInitialization()) return false
    val deviceSpecs = userPersistenceService.loadUserDevice(userId)
    return deviceSpecs?.ramSize?.let { it < 3500 } ?: false
  }

  suspend fun loadUserDevice(userId: String) = userPersistenceService.loadUserDevice(userId)

  suspend fun loadUserCountries(userId: String, ipToExclude: String): Set<String> = userPersistenceService.loadUserCountries(userId, ipToExclude)

  suspend fun getUserLastGameCoinsDate(userId: String): Instant? = userPersistenceService.getUserLastGameCoinsDate(userId)

  suspend fun getUserLastGameEm2CoinsDate(userId: String): Instant? = userPersistenceService.getUserLastGameEm2CoinsDate(userId)

  suspend fun getUserCountryCode(userId: String): String = userPersistenceService.getUserCountryCode(userId)

  suspend fun isUserUnique(userId: String): Boolean = userPersistenceService.isUserUnique(userId)

  suspend fun storeVideoReward(userId: String, reward: VideoAdReward) {
    val added = userPersistenceService.storeFirstVideoReward(userId, reward)
    if (added) {
      bigQueryEventPublisher.publish(
        UserFirstVideoRewardBqEventDto(
          userId = userId,
          reward = reward.revenue.toBigDecimal(),
          createdAt = timeService.now(),
        )
      )
      messageBus.publishAsync(
        DefineUsersEcpmGroupEffect(
          userId = userId,
          firstVideoAdRewardAmount = reward.revenue.toBigDecimal()
        )
      )
    }
  }

  suspend fun calculateMinThresholdsForEcpmGroups() {
    val ecpmGroupsThresholds = userPersistenceService.getFirstRewardsForUsersCreatedDuringPreviousDaysOrdered(DAYS_TO_CALCULATE_ECPM_GROUPS_THRESHOLDS)
      .takeIf { it.size >= 20 }
      ?.let { rewardsList ->
        rewardsList
          .withIndex()
          .groupBy { 20 * it.index / rewardsList.size } // we have 20 ecpm groups by 5% each starting from 0-group which is top-5% by ecpm users
          .mapValues { it.value.minOfOrNull { rewardIndexed -> rewardIndexed.value } }
      } ?: throw IllegalStateException("Not enough rewards to calculate ecpm groups thresholds")
    (0..19).associateWith { ecpmGroupsThresholds[it]!! }
      .let { userPersistenceService.writeCurrentEcpmGroupsThresholds(it); println(it) }

  }

  suspend fun getUserAdditionalCountryInfo(userId: String): AdditionalCountryInfo? = userPersistenceService.getUserAdditionalCountryInfo(userId)

  suspend fun getUserCountryTierSettings(userId: String): CountryTierSettings? = userPersistenceService.getUserCountryTierSettings(userId)

  suspend fun wasUserEverConnectedViaVpn(userId: String): Boolean = userPersistenceService.wasUserEverConnectedViaVpn(userId)

  suspend fun getUserFirstAppVersion(userId: String): Int? = userPersistenceService.getUserFirstAppVersion(userId)

  suspend fun getUserEcpmGroup(userId: String): Int? = userPersistenceService.getUserEcpmGroup(userId)

  suspend fun getDay0RevenueForUser(userId: String): BigDecimal? {
    val userCreationDate = getUser(userId, includingDeleted = true).createdAt
    val thresholdTime = timeService.now().minusSeconds(1.days.inWholeSeconds)

    return if (userCreationDate.isBefore(thresholdTime)) {
      rewardingFacade.getRevenueTotals(userId)?.day0Revenue
    } else {
      null
    }
  }

  private suspend fun isCountryAllowedForUser(userId: String, countryCode: String): Boolean {
    if (userPersistenceService.isUserWhitelisted(userId)) {
      return true
    }
    val allowedCountries = marketService.getAllowedCountries()
    return countryCode.uppercase() in allowedCountries
      && (userPersistenceService.findGpsLocationCountry(userId)?.uppercase().let { gpsCountry ->
      gpsCountry == null || gpsCountry in allowedCountries
    })
  }

  private suspend fun validateCountry(userId: String, appPlatform: AppPlatform, countryCode: String) {
    if ((appPlatform == IOS || appPlatform == IOS_WEB) && !isCountryAllowedForUser(userId, countryCode)) {
      throw CountryIsNotAllowedException()
    }

    if (countryCode in marketService.getForbiddenCountries()) {
      coroutineScope.get().launch {
        bigQueryEventPublisher.publish(
          Tier3UserAccessPreventedDto(userId, countryCode, timeService.now())
        )
      }

      throw CountryIsNotAllowedException()
    }
  }

  private fun checkForHardUpdate(appVersion: AppVersionDto) {
    getHardUpdateVersion(appVersion.platform).let { hardUpdateVersion ->
      if (appVersion.version < hardUpdateVersion) throw IncompatibleVersionException(
        userVersion = appVersion.version,
        expectedVersion = hardUpdateVersion
      )
    }
  }
}
