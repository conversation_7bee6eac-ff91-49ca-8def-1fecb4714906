package com.moregames.playtime.user.cashout

import com.google.inject.Inject
import com.moregames.base.app.PaymentProviderType
import com.moregames.base.app.UserIdentifierType
import com.moregames.base.base.BasePersistenceService
import com.moregames.base.config.CfgCashoutStatusProvidersImagesTable
import com.moregames.base.table.PaymentProviderTable
import com.moregames.base.table.UserCashoutTransactionsTable
import com.moregames.base.table.UserCashoutTransactionsTable.Status.*
import com.moregames.base.table.UserCashoutTransactionsTable.id
import com.moregames.base.user.UserPersonals
import com.moregames.base.util.TimeService
import com.moregames.playtime.earnings.dto.UserCurrencyEarnings
import com.moregames.playtime.user.cashout.dto.*
import org.jetbrains.exposed.sql.*
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*
import javax.inject.Singleton

@Singleton
class CashoutPersistenceService @Inject constructor(
  database: Database,
  private val timeService: TimeService,
) : BasePersistenceService(database) {

  suspend fun updatePaymentProviders(
    paymentProviderType: PaymentProviderType,
    countryCode: String,
    minimumAmount: BigDecimal,
    maximumAmount: BigDecimal,
    disclaimer: String
  ) {
    dbQuery {
      PaymentProviderTable.update({
        (PaymentProviderTable.provider eq paymentProviderType.key) and (PaymentProviderTable.countryCode eq countryCode)
      }) {
        it[PaymentProviderTable.minimumAmount] = minimumAmount
        it[PaymentProviderTable.maximumAmount] = maximumAmount
        it[PaymentProviderTable.disclaimer] = disclaimer
      }
    }
  }

  suspend fun loadPaymentProviders(countryCode: String): List<CashoutProvider> {
    return dbQuery {
      PaymentProviderTable
        .select {
          (PaymentProviderTable.countryCode eq countryCode) and
            (PaymentProviderTable.enabled eq true) and
            (PaymentProviderTable.disabledUntil.isNull() or (PaymentProviderTable.disabledUntil less timeService.now()))
        }
        .map { toCashoutProvider(it) }
    }
  }

  suspend fun loadPaymentProvidersNonFiltered(countryCode: String): List<CashoutProvider> = dbQuery {
    PaymentProviderTable
      .select { PaymentProviderTable.countryCode eq countryCode }
      .map { toCashoutProvider(it) }
  }

  suspend fun loadPaymentProvider(provider: PaymentProviderType, countryCode: String): CashoutProvider? {
    return dbQuery {
      PaymentProviderTable
        .select {
          (PaymentProviderTable.provider eq provider.key) and
            (PaymentProviderTable.countryCode eq countryCode) and
            (PaymentProviderTable.enabled eq true) and
            (PaymentProviderTable.disabledUntil.isNull() or (PaymentProviderTable.disabledUntil less timeService.now()))
        }
        .map { toCashoutProvider(it) }
        .firstOrNull()
    }
  }

  private fun toCashoutProvider(it: ResultRow) = CashoutProvider(
    displayName = it[PaymentProviderTable.displayName],
    url = it[PaymentProviderTable.url],
    videoUrl = it[PaymentProviderTable.videoUrl],
    iconFilename = it[PaymentProviderTable.iconFilename],
    largeIconFilename = it[PaymentProviderTable.largeIconFilename],
    smallIconFilename = it[PaymentProviderTable.smallIconFilename],
    text = it[PaymentProviderTable.text],
    shortText = it[PaymentProviderTable.shortText],
    providerType = PaymentProviderType.byKey(it[PaymentProviderTable.provider]),
    disclaimer = it[PaymentProviderTable.disclaimer],
    emailHint = it[PaymentProviderTable.emailHint],
    minimumAmount = it[PaymentProviderTable.minimumAmount],
    maximumAmount = it[PaymentProviderTable.maximumAmount],
    orderKey = it[PaymentProviderTable.orderKey],
    identifierType = it[PaymentProviderTable.identifierType]?.let { identifier -> UserIdentifierType.fromKey(identifier) },
    identifierHint = it[PaymentProviderTable.identifierHint]
  )

  suspend fun calculateTotalUsdCashoutsForUser(userId: String): BigDecimal =
    dbQuery {
      UserCashoutTransactionsTable
        .slice(UserCashoutTransactionsTable.cashedOutAmountUsd.sum())
        .select { UserCashoutTransactionsTable.userId eq userId and (UserCashoutTransactionsTable.status eq SUCCESSFUL.key) }
        .firstOrNull()
        ?.get(UserCashoutTransactionsTable.cashedOutAmountUsd.sum())
        ?: BigDecimal.ZERO
    }

  suspend fun calculateTotalCashoutsByProviderForUser(userId: String): Map<PaymentProviderType, UserCurrencyEarnings> =
    dbQuery {
      UserCashoutTransactionsTable
        .slice(
          UserCashoutTransactionsTable.provider,
          UserCashoutTransactionsTable.cashedOutAmountUsd.sum(),
          UserCashoutTransactionsTable.userCurrencyCode,
          UserCashoutTransactionsTable.userCurrencyAmount.sum()
        )
        .select {
          UserCashoutTransactionsTable.userId eq userId and (UserCashoutTransactionsTable.status notInList listOf(REJECTED.key, FAILED.key)
            and (UserCashoutTransactionsTable.userCurrencyCode.isNotNull()))
        }
        .groupBy(UserCashoutTransactionsTable.provider, UserCashoutTransactionsTable.userCurrencyCode)
        .associate {
          PaymentProviderType.byKey(it[UserCashoutTransactionsTable.provider]) to
            UserCurrencyEarnings(
              amountUsd = it[UserCashoutTransactionsTable.cashedOutAmountUsd.sum()] ?: BigDecimal.ZERO,
              userCurrency = Currency.getInstance(it[UserCashoutTransactionsTable.userCurrencyCode]),
              userCurrencyAmount = it[UserCashoutTransactionsTable.userCurrencyAmount.sum()] ?: BigDecimal.ZERO
            )
        }
    }

  suspend fun calculateTotalCashoutsForEmail(emailHash: String): BigDecimal =
    dbQuery {
      UserCashoutTransactionsTable
        .slice(UserCashoutTransactionsTable.cashedOutAmountUsd.sum())
        .select { UserCashoutTransactionsTable.emailHash eq emailHash and (UserCashoutTransactionsTable.status eq SUCCESSFUL.key) }
        .firstOrNull()
        ?.get(UserCashoutTransactionsTable.cashedOutAmountUsd.sum())
        ?: BigDecimal.ZERO
    }

  suspend fun loadUserIdsForEmails(emailHashSet: Set<String>): List<Pair<String, String>> =
    dbQuery {
      UserCashoutTransactionsTable
        .slice(UserCashoutTransactionsTable.userId, UserCashoutTransactionsTable.emailHash)
        .select { UserCashoutTransactionsTable.emailHash inList emailHashSet }
        .map { it[UserCashoutTransactionsTable.userId] to it[UserCashoutTransactionsTable.emailHash].orEmpty() }
        .distinct()
    }

  suspend fun createTransaction(userId: String, transactionId: String, cashoutDemand: CashoutDemand): Unit =
    dbQuery {
      UserCashoutTransactionsTable.insert {
        it[id] = transactionId
        it[UserCashoutTransactionsTable.userId] = userId
        it[type] = UserCashoutTransactionsTable.Type.EARNINGS.key
        it[status] = INITIAL.key
        it[provider] = cashoutDemand.provider.key
        it[countryCode] = cashoutDemand.countryCode
        it[encryptedName] = cashoutDemand.encryptedName
        it[encryptedAddress] = cashoutDemand.encryptedAddress
        it[encryptedEmail] = cashoutDemand.encryptedEmail
        it[emailHash] = cashoutDemand.emailHash
        it[userIp] = cashoutDemand.userIp
        it[userHandle] = cashoutDemand.userHandle
        it[normalizedEmailHash] = cashoutDemand.normalizedEmailHash
        it[normalizedEncryptedEmail] = cashoutDemand.normalizedEncryptedEmail
      }
    }

  suspend fun updateCashoutTransactionCashoutAmount(
    transactionId: String,
    amountUsd: BigDecimal,
    operationalWithholdAmountUsd: BigDecimal = BigDecimal.ZERO,
    userCurrency: Currency,
    userCurrencyAmount: BigDecimal,
    operationalWithholdUserCurrencyAmount: BigDecimal = BigDecimal.ZERO
  ) {
    dbQuery {
      UserCashoutTransactionsTable.update({ id eq transactionId }) {
        it[cashedOutAmountUsd] = amountUsd
        it[UserCashoutTransactionsTable.operationalWithholdAmountUsd] = operationalWithholdAmountUsd
        it[userCurrencyCode] = userCurrency.currencyCode
        it[UserCashoutTransactionsTable.userCurrencyAmount] = userCurrencyAmount
        it[UserCashoutTransactionsTable.operationalWithholdUserCurrencyAmount] = operationalWithholdUserCurrencyAmount
      }
    }
  }

  suspend fun updateTransactionStatus(transactionId: String, status: UserCashoutTransactionsTable.Status) = dbQuery {
    UserCashoutTransactionsTable.update({ id eq transactionId }) {
      it[UserCashoutTransactionsTable.status] = status.key
      if (status == SUCCESSFUL) it[processedAt] = timeService.now()
    }
  }

  suspend fun loadLastEarningsCashoutForUser(userId: String) =
    dbQuery {
      lastSuccessfulTransaction(userId)?.get(UserCashoutTransactionsTable.createdAt)
    }

  suspend fun loadLastSuccessfulTransaction(userId: String) =
    dbQuery {
      lastSuccessfulTransaction(userId)?.let { toCashoutTransactionDto(it) }
    }

  private fun lastSuccessfulTransaction(userId: String): ResultRow? {
    return UserCashoutTransactionsTable
      .select {
        (UserCashoutTransactionsTable.userId eq userId) and
          (UserCashoutTransactionsTable.status eq SUCCESSFUL.key) and
          (UserCashoutTransactionsTable.type eq UserCashoutTransactionsTable.Type.EARNINGS.key)
      }
      .orderBy(UserCashoutTransactionsTable.createdAt, SortOrder.DESC)
      .limit(1)
      .firstOrNull()
  }

  suspend fun countUsersWithSameEmail(userIdToExclude: String, emailHash: String) =
    dbQuery {
      UserCashoutTransactionsTable
        .slice(UserCashoutTransactionsTable.userId.countDistinct())
        .select { (UserCashoutTransactionsTable.userId neq userIdToExclude) and (UserCashoutTransactionsTable.emailHash eq emailHash) }
        .firstOrNull()
        ?.get(UserCashoutTransactionsTable.userId.countDistinct())
        ?: 0
    }

  suspend fun getRecentUserIdsWithSameEmail(userIdToExclude: String, emailHash: String) =
    dbQuery {
      UserCashoutTransactionsTable
        .slice(UserCashoutTransactionsTable.userId)
        .select {
          (UserCashoutTransactionsTable.userId neq userIdToExclude) and
            (UserCashoutTransactionsTable.emailHash eq emailHash) and
            (UserCashoutTransactionsTable.createdAt greater timeService.now().minus(30, ChronoUnit.DAYS))
        }
        .withDistinct()
        .map { it[UserCashoutTransactionsTable.userId] }
    }

  suspend fun countCashoutSumWithSameIp(userIp: String): BigDecimal =
    dbQuery {
      UserCashoutTransactionsTable
        .slice(UserCashoutTransactionsTable.cashedOutAmountUsd.sum())
        .select { (UserCashoutTransactionsTable.userIp eq userIp) and (UserCashoutTransactionsTable.status eq SUCCESSFUL.key) }
        .firstOrNull()?.get(UserCashoutTransactionsTable.cashedOutAmountUsd.sum())
        ?: BigDecimal.ZERO
    }

  suspend fun successfulUserCashouts(userId: String) =
    dbQuery {
      UserCashoutTransactionsTable
        .select { (UserCashoutTransactionsTable.userId eq userId) and (UserCashoutTransactionsTable.status eq SUCCESSFUL.key) }
        .count()
    }

  suspend fun loadRecentCashoutsByUserId(userId: String): List<SimplifiedCashoutDto> = dbQuery {
    UserCashoutTransactionsTable
      .slice(id, UserCashoutTransactionsTable.status, UserCashoutTransactionsTable.provider)
      .select {
        (UserCashoutTransactionsTable.userId eq userId) and
          (UserCashoutTransactionsTable.createdAt greater timeService.now().minus(1, ChronoUnit.DAYS))
      }.map {
        SimplifiedCashoutDto(
          it[id].value,
          UserCashoutTransactionsTable.Status.byKey(it[UserCashoutTransactionsTable.status]),
          PaymentProviderType.byKey(it[UserCashoutTransactionsTable.provider])
        )
      }
  }

  suspend fun hasSuccessCashouts(userId: String): Boolean = dbQuery {
    UserCashoutTransactionsTable
      .select { (UserCashoutTransactionsTable.userId eq userId) and (UserCashoutTransactionsTable.status neq FAILED.key) }
      .limit(1)
      .firstOrNull()?.let { true } ?: false
  }

  suspend fun hasCashouts(userId: String): Boolean = dbQuery {
    UserCashoutTransactionsTable
      .select { (UserCashoutTransactionsTable.userId eq userId) }
      .limit(1)
      .firstOrNull()?.let { true } ?: false
  }

  suspend fun loadRecentCashoutsByEmail(emailHash: String): List<SimplifiedCashoutDto> = dbQuery {
    UserCashoutTransactionsTable
      .slice(id, UserCashoutTransactionsTable.status, UserCashoutTransactionsTable.provider)
      .select {
        (UserCashoutTransactionsTable.emailHash eq emailHash) and
          (UserCashoutTransactionsTable.createdAt greater timeService.now().minus(1, ChronoUnit.DAYS))
      }
      .map {
        SimplifiedCashoutDto(
          it[id].value,
          UserCashoutTransactionsTable.Status.byKey(it[UserCashoutTransactionsTable.status]),
          PaymentProviderType.byKey(it[UserCashoutTransactionsTable.provider])
        )
      }
  }

  suspend fun loadRecentTransactions(userId: String, until: Instant): List<CashoutTransactionDto> = dbQuery {
    UserCashoutTransactionsTable
      .select { (UserCashoutTransactionsTable.userId eq userId) and (UserCashoutTransactionsTable.createdAt greater until) }
      .map { toCashoutTransactionDto(it) }
  }

  suspend fun loadTransactions(userId: String): List<CashoutTransactionDto> = dbQuery {
    UserCashoutTransactionsTable
      .select { (UserCashoutTransactionsTable.userId eq userId) }
      .map { toCashoutTransactionDto(it) }
  }

  suspend fun loadTransaction(cashoutTransactionId: String): CashoutTransactionDto = dbQuery {
    UserCashoutTransactionsTable
      .select { id eq cashoutTransactionId }
      .firstOrNull()
      ?.let { toCashoutTransactionDto(it) }
      ?: throw IllegalArgumentException("Transaction '$cashoutTransactionId' doesn't exist")
  }

  suspend fun userHasSuccessfulCashout(userId: String): Boolean = dbQuery {
    UserCashoutTransactionsTable
      .slice(id)
      .select { (UserCashoutTransactionsTable.userId eq userId) and (UserCashoutTransactionsTable.status eq SUCCESSFUL.key) }
      .limit(1)
      .firstOrNull()?.let { true } ?: false
  }

  suspend fun getProviderImagesConfig(countryCode: String, cashoutEnabled: Boolean): ProviderImagesCfg = dbQuery {
    CfgCashoutStatusProvidersImagesTable
      .select {
        (CfgCashoutStatusProvidersImagesTable.countryCode inList (listOf(countryCode, "US"))) and
          (CfgCashoutStatusProvidersImagesTable.cashoutEnabled eq cashoutEnabled)
      }
      .map { it.toProviderImagesCfg() }
      .let { configs ->
        configs.firstOrNull { it.countryCode == countryCode }
          ?: configs.firstOrNull { it.countryCode == "US" }
          ?: ProviderImagesCfg.empty()
      }
  }

  suspend fun obfuscateUserCashoutTransactionPersonals(userId: String): Int =
    dbQuery {
      UserCashoutTransactionsTable
        .update({ UserCashoutTransactionsTable.userId eq userId }) {
          it[encryptedName] = "personals deleted"
          it[encryptedAddress] = "personals deleted"
          it[emailHash] = "personals deleted"
          it[encryptedEmail] = "personals deleted"
          it[normalizedEncryptedEmail] = "personals deleted"
          it[normalizedEmailHash] = "personals deleted"
        }
    }

  suspend fun getLastUserPersonals(userId: String): UserPersonals? =
    dbQuery {
      UserCashoutTransactionsTable
        .slice(UserCashoutTransactionsTable.encryptedEmail, UserCashoutTransactionsTable.encryptedName)
        .select { UserCashoutTransactionsTable.userId eq userId }
        .orderBy(Pair(UserCashoutTransactionsTable.createdAt, SortOrder.DESC))
        .limit(1)
        .map {
          UserPersonals(
            it[UserCashoutTransactionsTable.encryptedEmail].orEmpty(),
            it[UserCashoutTransactionsTable.encryptedName].orEmpty(),
          )
        }
        .firstOrNull()
    }

  private fun toCashoutTransactionDto(it: ResultRow) = CashoutTransactionDto(
    cashoutTransactionId = it[id].value,
    userId = it[UserCashoutTransactionsTable.userId],
    encryptedEmail = it[UserCashoutTransactionsTable.encryptedEmail].orEmpty(),
    emailHash = it[UserCashoutTransactionsTable.emailHash].orEmpty(),
    userIp = it[UserCashoutTransactionsTable.userIp] ?: "", // TODO: make ip mandatory
    provider = PaymentProviderType.byKey(it[UserCashoutTransactionsTable.provider]),
    amountUsd = it[UserCashoutTransactionsTable.cashedOutAmountUsd] ?: BigDecimal.ZERO,
    userCurrency = Currency.getInstance(it[UserCashoutTransactionsTable.userCurrencyCode] ?: "USD"),
    userCurrencyAmount = it[UserCashoutTransactionsTable.userCurrencyAmount] ?: BigDecimal.ZERO,
    status = UserCashoutTransactionsTable.Status.byKey(it[UserCashoutTransactionsTable.status]),
    encryptedUserName = it[UserCashoutTransactionsTable.encryptedName].orEmpty(),
    userHandle = it[UserCashoutTransactionsTable.userHandle],
    encryptedAddress = it[UserCashoutTransactionsTable.encryptedAddress].orEmpty(),
  )

}

private fun ResultRow.toProviderImagesCfg(): ProviderImagesCfg =
  ProviderImagesCfg(
    countryCode = this[CfgCashoutStatusProvidersImagesTable.countryCode].uppercase(),
    imageFileNames = listOfNotNull(
      this[CfgCashoutStatusProvidersImagesTable.imageFilename1],
      this[CfgCashoutStatusProvidersImagesTable.imageFilename2],
      this[CfgCashoutStatusProvidersImagesTable.imageFilename3],
      this[CfgCashoutStatusProvidersImagesTable.imageFilename4],
    ).filter { it.isNotBlank() }
  )