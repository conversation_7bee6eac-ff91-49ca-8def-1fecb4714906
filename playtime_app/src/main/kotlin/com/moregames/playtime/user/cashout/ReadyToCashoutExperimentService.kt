package com.moregames.playtime.user.cashout

import com.google.inject.Inject
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.variations.AndroidCashoutReadyNotificationVariation
import com.moregames.base.util.format
import com.moregames.playtime.earnings.dto.UserCurrencyEarnings
import com.moregames.playtime.util.roundDownToSecondDigit
import java.math.BigDecimal
import javax.inject.Singleton

@Singleton
class ReadyToCashoutExperimentService @Inject constructor(
  private val abTestingService: AbTestingService
) {
  suspend fun getReadyToCashoutExperimentConfig(userId: String, unclaimedUsdEarnings: UserCurrencyEarnings): ReadyToCashoutExperimentConfig? {
    val assignedVariationValue = abTestingService.assignedVariationValue(userId, ClientExperiment.ANDROID_CASHOUT_READY_NOTIFICATION)
    when (assignedVariationValue) {
      AndroidCashoutReadyNotificationVariation.CashoutReadyClaim -> {
        if (unclaimedUsdEarnings.amountUsd < BigDecimal.ONE) {
          return ReadyToCashoutExperimentConfig(
            title = "You've Got Money!",
            description = "Open JustPlay to claim your rewards"
          )
        } else {
          val amountString = unclaimedUsdEarnings.userCurrencyAmount.roundDownToSecondDigit().format(unclaimedUsdEarnings.userCurrency)
          return ReadyToCashoutExperimentConfig(
            title = "You've Made $amountString!",
            description = "Don't wait—claim your earnings now!"
          )
        }
      }

      AndroidCashoutReadyNotificationVariation.CashoutReadyWaiting -> {
        if (unclaimedUsdEarnings.amountUsd < BigDecimal.ONE) {
          return ReadyToCashoutExperimentConfig(
            title = "Your Cash is Ready!",
            description = "Your JustPlay payout is waiting for you"
          )
        } else {
          val amountString = unclaimedUsdEarnings.userCurrencyAmount.roundDownToSecondDigit().format(unclaimedUsdEarnings.userCurrency)
          return ReadyToCashoutExperimentConfig(
            title = "Cash Alert: $amountString Ready!",
            description = "Your rewards are waiting. Withdraw now!"
          )
        }
      }

      else -> return null
    }
  }
}

data class ReadyToCashoutExperimentConfig(
  val title: String? = null,
  val description: String? = null,
)