package com.justplayapps.service.rewarding.earnings

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.justplayapps.service.rewarding.earnings.dto.UserCurrencyEarnings
import com.justplayapps.service.rewarding.earnings.proto.EarningModel
import com.justplayapps.service.rewarding.earnings.proto.*
import com.justplayapps.service.rewarding.earnings.proto.EmApiGrpcKt.EmApiCoroutineStub
import com.justplayapps.service.rewarding.revenue.RevenuePersistenceService
import com.moregames.base.grpc.testGrpcClient
import com.moregames.base.grpc.withGrpcService
import com.moregames.base.util.mock
import com.moregames.base.util.toProto
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import java.math.BigDecimal
import java.time.LocalDate
import java.util.*

class EmApiImplTest {

  private val emExperimentBaseService: EmExperimentBaseService = mock()
  private val stashCoinsService: StashCoinsService = mock()
  private val userEarningsService: UserEarningsService = mock()
  private val userEarningsPersistenceService: UserEarningsPersistenceService = mock()
  private val revenuePersistenceService: RevenuePersistenceService = mock()
  private val userCurrentCoinsBalanceService: UserCurrentCoinsBalanceService = mock()

  private val underTest = EmApiImpl(
    emExperimentBaseService = emExperimentBaseService,
    stashCoinsService = stashCoinsService,
    userEarningsService = userEarningsService,
    userEarningsPersistenceService = userEarningsPersistenceService,
    revenuePersistenceService = revenuePersistenceService,
    userCurrentCoinsBalanceService = userCurrentCoinsBalanceService
  )

  private val client = testGrpcClient(::EmApiCoroutineStub)

  companion object {
    private const val USER_ID = "user-id"
    private val currencyCad = Currency.getInstance("CAD")
  }

  @Test
  fun `SHOULD return default LoadUnpaidUserCurrencyEarningsResponse ON loadUnpaidUserCurrencyEarnings WHEN no unpaid data for user`() =
    withGrpcService(underTest) {
      val expected = EarningModel.LoadUnpaidUserCurrencyEarningsResponse.getDefaultInstance()

      userEarningsPersistenceService.mock({ loadUnpaidUserCurrencyEarnings(USER_ID) }, null)

      val request = loadUnpaidUserCurrencyEarningsRequest { this.userId = USER_ID }

      val actual = client.loadUnpaidUserCurrencyEarnings(request)

      assertThat(actual).isEqualTo(expected)
    }

  @Test
  fun `SHOULD return LoadUnpaidUserCurrencyEarningsResponse ON loadUnpaidUserCurrencyEarnings`() = withGrpcService(underTest) {
    val expected = loadUnpaidUserCurrencyEarningsResponse {
      this.amountUsd = BigDecimal.ONE.toProto()
      this.userCurrencyAmount = BigDecimal.TWO.toProto()
      this.userCurrencyCode = "CAD"
    }

    val earnings = UserCurrencyEarnings(amountUsd = BigDecimal.ONE, userCurrency = currencyCad, userCurrencyAmount = BigDecimal.TWO)

    userEarningsPersistenceService.mock({ loadUnpaidUserCurrencyEarnings(USER_ID) }, earnings)

    val request = loadUnpaidUserCurrencyEarningsRequest { this.userId = USER_ID }

    val actual = client.loadUnpaidUserCurrencyEarnings(request)

    assertThat(actual).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return LoadUnpaidUserCurrencyEarningsResponse ON loadUnpaidUserCurrencyEarnings WHEN non boosted balanced defined`() =
    withGrpcService(underTest) {
      val expected = loadUnpaidUserCurrencyEarningsResponse {
        this.amountUsd = BigDecimal.ONE.toProto()
        this.userCurrencyAmount = BigDecimal.TWO.toProto()
        this.userCurrencyCode = "CAD"
        this.nonBoostedAmountUsd = BigDecimal("2.71").toProto()
        this.nonBoostedUserCurrencyAmount = BigDecimal("3.14").toProto()
      }

      val earnings = UserCurrencyEarnings(
        amountUsd = BigDecimal.ONE,
        userCurrency = currencyCad,
        userCurrencyAmount = BigDecimal.TWO,
        nonBoostedAmountUsd = BigDecimal("2.71"),
        nonBoostedUserCurrencyAmount = BigDecimal("3.14"),
      )

      userEarningsPersistenceService.mock({ loadUnpaidUserCurrencyEarnings(USER_ID) }, earnings)

      val request = loadUnpaidUserCurrencyEarningsRequest { this.userId = USER_ID }

      val actual = client.loadUnpaidUserCurrencyEarnings(request)

      assertThat(actual).isEqualTo(expected)
    }

  @Test
  fun `SHOULD return GetUserRevenueLast2DaysResponse ON getUserRevenueLast2Days WHEN user has revenue`() =
    withGrpcService(underTest) {
      val userId = "test-user-123"
      val revenueAmount = BigDecimal("150.75")
      val yesterday = LocalDate.now().minusDays(1)

      val expected = getUserRevenueLast2DaysResponse {
        this.revenue = revenueAmount.toProto()
      }

      revenuePersistenceService.mock({ getUserRevenueAfter(userId, yesterday) }, revenueAmount)

      val request = getUserRevenueLast2DaysRequest {
        this.userId = userId
      }

      val actual = client.getUserRevenueLast2Days(request)

      assertThat(actual).isEqualTo(expected)
    }

  @Test
  fun `SHOULD return zero revenue ON getUserRevenueLast2Days WHEN user has no revenue`() =
    withGrpcService(underTest) {
      val userId = "test-user-456"
      val yesterday = LocalDate.now().minusDays(1)

      val expected = getUserRevenueLast2DaysResponse {
        this.revenue = BigDecimal.ZERO.toProto()
      }

      revenuePersistenceService.mock({ getUserRevenueAfter(userId, yesterday) }, BigDecimal.ZERO)

      val request = getUserRevenueLast2DaysRequest {
        this.userId = userId
      }

      val actual = client.getUserRevenueLast2Days(request)

      assertThat(actual).isEqualTo(expected)
    }

}