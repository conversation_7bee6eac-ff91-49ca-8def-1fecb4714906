package com.moregames.playtime.buseffects

import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.variations.SpecialCashoutOffersVariation
import com.moregames.base.bus.MessageBus
import com.moregames.base.gameStub
import com.moregames.base.junit.TypedVariationSource
import com.moregames.base.util.mock
import com.moregames.base.util.mockExperimentVariation
import com.moregames.playtime.earnings.dto.UserCurrencyEarnings
import com.moregames.playtime.notifications.PushNotification
import com.moregames.playtime.user.cashout.CashoutService
import com.moregames.playtime.user.cashout.offers.CashoutOffersService
import com.moregames.playtime.user.cashout.offers.CashoutOffersService.CashoutOfferSet
import com.moregames.playtime.utils.USD
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.*
import java.time.Instant
import java.util.*

class CashoutOffersEffectsTest {
  private val cashoutOffersService: CashoutOffersService = mock()
  private val messageBus: MessageBus = mock()
  private val abTestingService: AbTestingService = mock()
  private val cashoutService: CashoutService = mock()

  private val underTest = CashoutOffersEffects(
    cashoutOffersService = cashoutOffersService,
    messageBus = messageBus,
    abTestingService = abTestingService,
    cashoutService = cashoutService,
  )

  @ParameterizedTest
  @TypedVariationSource(SpecialCashoutOffersVariation::class, keys = ["3specialCashoutOffers", "3specialCashoutOffersRandom"])
  fun `SHOULD create offers set IF variation is non-earning based`(variation: SpecialCashoutOffersVariation) = runTest {
    val userId = "userId"
    abTestingService.mockExperimentVariation(userId, ClientExperiment.SPECIAL_CASHOUT_OFFERS, variation)
    underTest.handleCreateCashoutOfferSetEffect(CreateCashoutOfferSetEffect(userId))

    verify(cashoutOffersService).createSetIfNeeded(userId)
  }

  @ParameterizedTest
  @MethodSource("earningBasedValidTestData")
  fun `SHOULD create offers set IF variation is earning based AND data is valid`(variation: SpecialCashoutOffersVariation, earningAmount: Double) = runTest {
    val userId = "userId"
    abTestingService.mockExperimentVariation(userId, ClientExperiment.SPECIAL_CASHOUT_OFFERS, variation)
    cashoutService.mock({ userHasSuccessfulCashout(userId) }, false)
    cashoutService.mock({ getNonCashedUserCurrencyEarnings(userId) }, UserCurrencyEarnings(earningAmount.toBigDecimal(), USD, earningAmount.toBigDecimal()))

    underTest.handleCreateCashoutOfferSetEffect(
      CreateCashoutOfferSetEffect(
        userId
      )
    )

    verify(cashoutOffersService).createSetIfNeeded(userId)
  }

  @ParameterizedTest
  @MethodSource("allEarningsVariants")
  fun `SHOULD create offers set IF variation is earning based AND user has cashouted, no matter earnings`(
    variation: SpecialCashoutOffersVariation,
    earningAmount: Double
  ) = runTest {
    val userId = "userId"
    abTestingService.mockExperimentVariation(userId, ClientExperiment.SPECIAL_CASHOUT_OFFERS, variation)
    cashoutService.mock({ userHasSuccessfulCashout(userId) }, true)
    cashoutService.mock({ getNonCashedUserCurrencyEarnings(userId) }, UserCurrencyEarnings(earningAmount.toBigDecimal(), USD, earningAmount.toBigDecimal()))

    underTest.handleCreateCashoutOfferSetEffect(
      CreateCashoutOfferSetEffect(
        userId
      )
    )

    verify(cashoutOffersService).createSetIfNeeded(userId)
  }

  @ParameterizedTest
  @MethodSource("earningBasedInvalidTestData")
  fun `SHOULD NOT create offers set IF variation is earning based AND earnings are too high`(variation: SpecialCashoutOffersVariation, earningAmount: Double) =
    runTest {
      val userId = "userId"
      abTestingService.mockExperimentVariation(userId, ClientExperiment.SPECIAL_CASHOUT_OFFERS, variation)
      cashoutService.mock({ userHasSuccessfulCashout(userId) }, false)
      cashoutService.mock({ getNonCashedUserCurrencyEarnings(userId) }, UserCurrencyEarnings(earningAmount.toBigDecimal(), USD, earningAmount.toBigDecimal()))

      underTest.handleCreateCashoutOfferSetEffect(CreateCashoutOfferSetEffect(userId))

      verify(cashoutOffersService, never()).createSetIfNeeded(userId)
    }

  @Test
  fun `SHOULD only send push IF some offers are unclaimed or active`() = runTest {
    cashoutOffersService.mock(
      { getOfferSet(USER_ID) }, CashoutOfferSet.Active(
        setId = 1,
        userId = USER_ID,
        offers = listOf(
          CashoutOffersService.CashoutOffer.Claimed(UUID.randomUUID().toString(), 1),
          CashoutOffersService.CashoutOffer.Active(UUID.randomUUID().toString(), 2, Instant.now()),
          CashoutOffersService.CashoutOffer.Unclaimed(UUID.randomUUID().toString(), 3),
        ),
      )
    )

    underTest.handleCashoutOfferActivatedEvent(CashoutOfferActivatedEvent(USER_ID, 2, gameStub.name, Instant.now()))

    verify(messageBus).publishAsync(PushNotificationEffect(PushNotification.AndroidPushNotification.CashoutOfferStarted(USER_ID, gameStub.name)))
    verifyNoMoreInteractions(messageBus)
  }

  @Test
  fun `SHOULD publish event IF all offers are active or claimed`() = runTest {
    val now = Instant.now()
    cashoutOffersService.mock(
      { getOfferSet(USER_ID) }, CashoutOfferSet.Active(
        setId = 1,
        userId = USER_ID,
        offers = listOf(
          CashoutOffersService.CashoutOffer.Claimed(UUID.randomUUID().toString(), 1),
          CashoutOffersService.CashoutOffer.Active(UUID.randomUUID().toString(), 2, now),
          CashoutOffersService.CashoutOffer.Claimed(UUID.randomUUID().toString(), 3),
        ),
      )
    )

    underTest.handleCashoutOfferActivatedEvent(CashoutOfferActivatedEvent(USER_ID, 2, gameStub.name, now))

    verifyBlocking(messageBus) {
      publish(CloseCashoutOfferSetCommand(USER_ID), now)
    }
    verifyBlocking(messageBus) {
      publishAsync(PushNotificationEffect(PushNotification.AndroidPushNotification.CashoutOfferStarted(USER_ID, gameStub.name)))
    }
  }

  @Test
  fun `SHOULD close offer set when handling close command`() = runTest {
    underTest.handleCloseCashoutOfferSetCommand(CloseCashoutOfferSetCommand(USER_ID))

    verifyBlocking(cashoutOffersService) {
      closeSetIfNeeded(USER_ID)
    }
  }

  private companion object {
    val USER_ID = "userId"

    @JvmStatic
    fun earningBasedInvalidTestData(): List<Arguments> {
      return listOf(
        Arguments.of(SpecialCashoutOffersVariation.ThreeRandom05EarningCashoutOffers, 0.6),
        Arguments.of(SpecialCashoutOffersVariation.ThreeRandom025EarningCashoutOffers, 0.3),
        Arguments.of(SpecialCashoutOffersVariation.ThreeRandom1EarningCashoutOffers, 1.1),
        Arguments.of(SpecialCashoutOffersVariation.ThreeRandom05EarningCashoutOffers, 0.0),
        Arguments.of(SpecialCashoutOffersVariation.ThreeRandom025EarningCashoutOffers, 0.0),
        Arguments.of(SpecialCashoutOffersVariation.ThreeRandom1EarningCashoutOffers, 0.0),
      )
    }

    @JvmStatic
    fun earningBasedValidTestData(): List<Arguments> {
      return listOf(
        Arguments.of(SpecialCashoutOffersVariation.ThreeRandom05EarningCashoutOffers, 0.4),
        Arguments.of(SpecialCashoutOffersVariation.ThreeRandom025EarningCashoutOffers, 0.2),
        Arguments.of(SpecialCashoutOffersVariation.ThreeRandom1EarningCashoutOffers, 0.9),
      )
    }

    @JvmStatic
    fun allEarningsVariants() = earningBasedValidTestData() + earningBasedInvalidTestData()
  }
}