package com.moregames.playtime.buseffects

import com.justplayapps.playtime.proto.unpaidUserEarningsAddedEvent
import com.justplayapps.playtime.proto.userCreatedEvent
import com.justplayapps.service.rewarding.earnings.EarningsHandlers
import com.justplayapps.service.rewarding.earnings.UserCurrentCoinsBalanceService
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService
import com.justplayapps.service.rewarding.earnings.UserEarningsService
import com.justplayapps.service.rewarding.earnings.dto.UserCurrencyEarnings
import com.justplayapps.service.rewarding.facade.toProto
import com.moregames.base.bus.MessageBus
import com.moregames.base.util.mock
import com.moregames.playtime.earnings.CashoutSettingsService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.utils.EN_LOCALE
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.kotlin.verifyBlocking
import java.math.BigDecimal
import java.util.*

class EarningsAddedHandlerTest {
  private val cashoutSettingsService: CashoutSettingsService = Mockito.mock()
  private val userService: UserService = Mockito.mock()
  private val messageBus: MessageBus = Mockito.mock()
  private val userEarningsService: UserEarningsService = Mockito.mock()
  private val userCurrentCoinsBalanceService: UserCurrentCoinsBalanceService = Mockito.mock()
  private val userEarningsPersistenceService: UserEarningsPersistenceService = Mockito.mock()

  private val handler = EarningsAddedEffectHandler(
    cashoutSettingsService = cashoutSettingsService,
    userService = userService,
    messageBus = messageBus,
  )

  private val earningsHandler = EarningsHandlers(
    userEarningsService = userEarningsService,
    userEarningsPersistenceService = userEarningsPersistenceService,
    userCurrentCoinsBalanceService = userCurrentCoinsBalanceService,
  )

  companion object {
    private const val USER_ID = "user-id"
    private val maxEarnings = BigDecimal("30.00")
    val earnings = UserCurrencyEarnings(
      // also checks scale issue when comparing BigDecimals
      amountUsd = maxEarnings,
      userCurrency = Currency.getInstance("CAD"),
      userCurrencyAmount = BigDecimal("999")
    )
    val effect = unpaidUserEarningsAddedEvent {
      this.userId = USER_ID
      this.earnings = Companion.earnings.toProto()
    }
  }

  @BeforeEach
  fun init() {
    cashoutSettingsService.mock({ getUserMaxEarningsAmount(USER_ID) }, maxEarnings)
    userService.mock({ userExists(USER_ID) }, true)
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(id = USER_ID, locale = EN_LOCALE))
  }

  @Test
  fun `SHOULD process earnings ON onEarningsAdded`() = runTest {
    earningsHandler.handleEarningsAdded(effect)

    verifyBlocking(userEarningsService) { trackUnpaidUserEarnings(USER_ID, earnings) }
  }

  @Test
  fun `SHOULD process earnings ON onUserCreated`() = runTest {
    earningsHandler.handleUserCreatedEvent(userCreatedEvent { this.userId = USER_ID })

    verifyBlocking(userEarningsService) { createZeroRevenue(USER_ID) }
    verifyBlocking(userCurrentCoinsBalanceService) { createZeroBalance(USER_ID) }
  }

  @Test
  fun `SHOULD create popup message ON onEarningsAdded WHEN earnings cap reached`() = runTest {
    handler.handleEarningsAdded(effect)

    verifyBlocking(messageBus) { publishAsync(CreateThresholdReachedPopupMessageEffect(USER_ID, EN_LOCALE)) }
  }
}