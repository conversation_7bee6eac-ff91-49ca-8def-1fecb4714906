package com.moregames.playtime.buseffects

import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.playtime.app.enableMolocoIntegration
import com.moregames.playtime.app.messaging.dto.EventType
import com.moregames.playtime.app.messaging.dto.MolocoInAppEvent
import com.moregames.playtime.tracking.MolocoApiClient
import com.moregames.playtime.utils.user
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoInteractions
import org.mockito.kotlin.whenever
import java.math.BigDecimal

class MolocoSendInAppEventEffectHandlerTest {
  private val molocoApiClient: MolocoApiClient = mock()
  private val featureFlagsFacade: FeatureFlagsFacade = mock()
  private val handler = MolocoSendInAppEventEffectHandler(molocoApiClient, featureFlagsFacade)

  @Test
  fun `SHOULD handle on handleMolocoSendInAppEventEffect`() {
    whenever(featureFlagsFacade.enableMolocoIntegration()).thenReturn(true)
    val molocoEvent = MolocoInAppEvent(
      ipAddress = "***********",
      userAgent = "Dalvik/2.1.0 (Linux; U; Android 11; U696CL Build/UMX_U696CL_V11.01.01.07)",
      idfa = "b19860d2-ccf9-472b-a0f6-c2e517bd841a",
      idfv = "b19860d2-ccf9-472b-a0f6-c2e517bd841a",
      platform = user.appPlatform,
      amount = BigDecimal("11.00"),
      timestamp = 123L,
      type = EventType.SC_WITH_REVENUE
    )
    runBlocking {
      handler.handleMolocoSendInAppEventEffect(SendMolocoInAppEventEffect(event = molocoEvent))
    }
    verifyBlocking(molocoApiClient) { sendInAppEvent(molocoEvent) }
  }

  @Test
  fun `SHOULD do nothing ON handleMolocoSendInAppEventEffect WHEN enableMolocoIntegration is false`() {
    val molocoEvent = MolocoInAppEvent(
      ipAddress = "***********",
      userAgent = "Dalvik/2.1.0 (Linux; U; Android 11; U696CL Build/UMX_U696CL_V11.01.01.07)",
      idfa = "b19860d2-ccf9-472b-a0f6-c2e517bd841a",
      idfv = "b19860d2-ccf9-472b-a0f6-c2e517bd841a",
      platform = user.appPlatform,
      amount = BigDecimal("11.00"),
      timestamp = 123L,
      type = EventType.SC_WITH_REVENUE
    )
    runBlocking {
      handler.handleMolocoSendInAppEventEffect(SendMolocoInAppEventEffect(event = molocoEvent))
    }
    verifyNoInteractions(molocoApiClient)
  }
}