package com.moregames.playtime.buseffects

import com.justplayapps.service.rewarding.earnings.StashCoinsEffectHandler
import com.justplayapps.service.rewarding.earnings.UserStashPersistenceService
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import java.math.BigDecimal

class StashCoinsEffectHandlerTest {
  private val userStashPersistenceService: UserStashPersistenceService = mock()

  private val underTest = StashCoinsEffectHandler(
    userStashPersistenceService = userStashPersistenceService,
  )

  companion object {
    private const val USER_ID = "user-id"
    private val stashCoins = StashCoinsEffectHandler.StashCoinsEffect(
      userId = USER_ID,
      usdToStash = BigDecimal("0.43"),
      coinsForOneDollar = BigDecimal("220")
    )
  }

  @Test
  fun `SHOULD stash coins ON handleStashCoins`() {
    runBlocking { underTest.handleStashCoins(stashCoins) }

    verifyBlocking(userStashPersistenceService) {
      stashCoins(USER_ID, BigDecimal("94.60"))
    }
  }
}