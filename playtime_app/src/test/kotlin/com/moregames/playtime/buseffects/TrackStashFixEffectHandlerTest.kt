package com.moregames.playtime.buseffects

import com.justplayapps.service.rewarding.earnings.StashFixService
import com.justplayapps.service.rewarding.earnings.TrackStashFixEffectHandler
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import java.math.BigDecimal

class TrackStashFixEffectHandlerTest {

  private val stashFixService: StashFixService = mock()

  private val underTest = TrackStashFixEffectHandler(
    stashFixService = stashFixService,
  )

  @Test
  fun `SHOULD handle effect ON handleTrackStashFixEffect`() {
    val effect = TrackStashFixEffectHandler.TrackStashFixEffect("user-id", BigDecimal("0.16"))

    runBlocking { underTest.handleTrackStashFixEffect(effect) }

    verifyBlocking(stashFixService) { trackStashFixGiven("user-id", BigDecimal("0.16")) }
  }
}