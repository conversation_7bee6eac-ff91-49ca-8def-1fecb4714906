package com.moregames.playtime.carousel

import assertk.all
import assertk.assertThat
import assertk.assertions.*
import com.moregames.base.exceptions.RecordNotFoundException
import com.moregames.base.junit.MockExtension
import com.moregames.base.table.DatabaseExtension
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.base.util.prepareGame
import com.moregames.base.util.prepareUser
import com.moregames.playtime.carousel.domain.UserCarouselTaskEntity
import com.moregames.playtime.user.challenge.progress.ObjectiveProgressCalculatorType
import com.moregames.playtime.user.challenge.progress.ObjectiveProgressCalculatorType.TREASURE_MASTER_20
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.deleteAll
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Instant
import java.util.*
import kotlin.test.assertFailsWith

@ExtendWith(DatabaseExtension::class, MockExtension::class)
class CarouselPersistenceServiceTest(
  private val database: Database,
  private val timeService: TimeService,
) {

  private lateinit var underTest: CarouselPersistenceService
  private val now = Instant.parse("2024-01-01T12:00:00Z")

  @BeforeEach
  fun setUp() {
    timeService.mock({ now() }, now)
    underTest = CarouselPersistenceService(database, timeService)

    transaction(database) {
      UserCarouselTaskTable.deleteAll()
      CarouselTaskTable.deleteAll()
    }
  }

  @Test
  fun `SHOULD return empty list ON findVisibleCarouselTasks WHEN no tasks exist`() {
    val userId = database.prepareUser()

    val result = runBlocking {
      underTest.findVisibleCarouselTasks(userId)
    }

    assertThat(result).isEmpty()
  }

  @Test
  fun `SHOULD return visible tasks ON findVisibleCarouselTasks WHEN tasks exist but not completed`() {
    val userId = database.prepareUser()
    val taskDefinitionId = "task-def-1"
    val gameId = database.prepareGame()

    // Prepare task definition
    prepareCarouselTaskDefinition(taskDefinitionId, gameId)

    // Prepare user tasks with different states
    val taskId1 = prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.NEW)
    val taskId2 = prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.IN_PROGRESS)
    val taskId3 = prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.UNCLAIMED)
    val taskId4 = prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.CLAIMED)
    // This one should not be returned
    prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.COMPLETED)

    val result = runBlocking {
      underTest.findVisibleCarouselTasks(userId)
    }

    assertThat(result).hasSize(4)
    val taskIds = result.map { it.taskId }
    assertThat(taskIds).isEqualTo(listOf(taskId1, taskId2, taskId3, taskId4))
  }

  @Test
  fun `SHOULD return empty list ON findActiveCarouselTaskPerGame WHEN no active tasks for game`() {
    val userId = database.prepareUser()
    val gameId = database.prepareGame()

    val result = runBlocking {
      underTest.findActiveCarouselTaskPerGame(userId, gameId)
    }

    assertThat(result).isNull()
  }

  @Test
  fun `SHOULD return active tasks ON findActiveCarouselTaskPerGame WHEN in progress tasks exist for game`() {
    val userId = database.prepareUser()
    val taskDefinitionId1 = "task-def-1"
    val taskDefinitionId2 = "task-def-2"
    val gameId = database.prepareGame()
    val otherGameId = database.prepareGame()

    // Prepare task definitions
    prepareCarouselTaskDefinition(taskDefinitionId1, gameId)
    prepareCarouselTaskDefinition(taskDefinitionId2, otherGameId)

    // Prepare tasks - only IN_PROGRESS for the specific game should be returned
    val activeTaskId = prepareUserCarouselTask(userId, taskDefinitionId1, UserCarouselTaskState.IN_PROGRESS)
    prepareUserCarouselTask(userId, taskDefinitionId1, UserCarouselTaskState.NEW) // Different state
    prepareUserCarouselTask(userId, taskDefinitionId2, UserCarouselTaskState.IN_PROGRESS) // Different game

    val result = runBlocking {
      underTest.findActiveCarouselTaskPerGame(userId, gameId)
    }

    assertThat(result).isNotNull().all {
      prop(UserCarouselTaskEntity::taskId).isEqualTo(activeTaskId)
      prop(UserCarouselTaskEntity::userId).isEqualTo(userId)
      prop(UserCarouselTaskEntity::state).isEqualTo(UserCarouselTaskState.IN_PROGRESS)
      prop(UserCarouselTaskEntity::gameId).isEqualTo(gameId)
    }
  }

  @Test
  fun `SHOULD throw exception ON getUserCarouselTask WHEN task does not exist`() {
    val nonExistentTaskId = UUID.randomUUID()

    assertFailsWith<RecordNotFoundException> {
      runBlocking {
        underTest.getUserCarouselTask(nonExistentTaskId)
      }
    }
  }

  @Test
  fun `SHOULD return task ON getUserCarouselTask WHEN task exists`() {
    val userId = database.prepareUser()
    val taskDefinitionId = "task-def-1"
    val gameId = database.prepareGame()

    prepareCarouselTaskDefinition(taskDefinitionId, gameId)
    val taskId = prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.NEW)

    val result = runBlocking {
      underTest.getUserCarouselTask(taskId)
    }

    assertThat(result).isNotNull()
    assertThat(result!!.taskId).isEqualTo(taskId)
    assertThat(result.userId).isEqualTo(userId)
    assertThat(result.state).isEqualTo(UserCarouselTaskState.NEW)
    assertThat(result.gameId).isEqualTo(gameId)
  }

  @Test
  fun `SHOULD update state to IN_PROGRESS ON markTaskAsStarted`() {
    val userId = database.prepareUser()
    val taskDefinitionId = "task-def-1"
    val gameId = database.prepareGame()

    prepareCarouselTaskDefinition(taskDefinitionId, gameId)
    val taskId = prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.NEW)

    runBlocking {
      underTest.markTaskAsStarted(taskId)
    }

    val updatedState = getTaskState(taskId)
    assertThat(updatedState).isEqualTo(UserCarouselTaskState.IN_PROGRESS)
  }

  @Test
  fun `SHOULD update state to UNCLAIMED ON markTaskAsFinished`() {
    val userId = database.prepareUser()
    val taskDefinitionId = "task-def-1"
    val gameId = database.prepareGame()

    prepareCarouselTaskDefinition(taskDefinitionId, gameId)
    val taskId = prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.IN_PROGRESS)

    runBlocking {
      underTest.markTaskAsFinished(taskId)
    }

    val updatedState = getTaskState(taskId)
    assertThat(updatedState).isEqualTo(UserCarouselTaskState.UNCLAIMED)
  }

  @Test
  fun `SHOULD update state to CLAIMED ON markTaskAsClaimed`() {
    val userId = database.prepareUser()
    val taskDefinitionId = "task-def-1"
    val gameId = database.prepareGame()

    prepareCarouselTaskDefinition(taskDefinitionId, gameId)
    val taskId = prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.UNCLAIMED)

    runBlocking {
      underTest.markTaskAsClaimed(taskId, now.plusSeconds(3600))
    }

    val updatedState = getTaskState(taskId)
    assertThat(updatedState).isEqualTo(UserCarouselTaskState.CLAIMED)
  }

  @Test
  fun `SHOULD update state to COMPLETED and set completedAt ON completeTask`() {
    val userId = database.prepareUser()
    val taskDefinitionId = "task-def-1"
    val gameId = database.prepareGame()

    prepareCarouselTaskDefinition(taskDefinitionId, gameId)
    val taskId = prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.CLAIMED)

    runBlocking {
      underTest.completeTask(taskId)
    }

    transaction(database) {
      UserCarouselTaskTable
        .select { UserCarouselTaskTable.id eq taskId }
        .first()
        .let { row ->
          assertThat(row[UserCarouselTaskTable.state]).isEqualTo(UserCarouselTaskState.COMPLETED)
          assertThat(row[UserCarouselTaskTable.completedAt]).isEqualTo(now)
        }
    }
  }

  @Test
  fun `SHOULD not affect other users tasks ON findVisibleCarouselTasks`() {
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()
    val taskDefinitionId = "task-def-1"
    val gameId = database.prepareGame()

    prepareCarouselTaskDefinition(taskDefinitionId, gameId)
    prepareUserCarouselTask(userId1, taskDefinitionId, UserCarouselTaskState.NEW)
    prepareUserCarouselTask(userId2, taskDefinitionId, UserCarouselTaskState.IN_PROGRESS)

    val result1 = runBlocking { underTest.findVisibleCarouselTasks(userId1) }
    val result2 = runBlocking { underTest.findVisibleCarouselTasks(userId2) }

    assertThat(result1).hasSize(1)
    assertThat(result1.first().userId).isEqualTo(userId1)
    assertThat(result1.first().state).isEqualTo(UserCarouselTaskState.NEW)

    assertThat(result2).hasSize(1)
    assertThat(result2.first().userId).isEqualTo(userId2)
    assertThat(result2.first().state).isEqualTo(UserCarouselTaskState.IN_PROGRESS)
  }

  @Test
  fun `SHOULD update progress only ON updateTaskProgress WHEN achievement is not provided`() {
    val userId = database.prepareUser()
    val taskDefinitionId = "task-def-1"
    val gameId = database.prepareGame()

    prepareCarouselTaskDefinition(taskDefinitionId, gameId)
    val taskId = prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.IN_PROGRESS, progress = 10)

    val result = runBlocking {
      underTest.updateTaskProgress(taskId, currentProgress = 10, progress = 50)
    }

    assertThat(result).isTrue() // optimistic lock succeeded
    val updatedTask = getTaskProgressAndAchievement(taskId)
    assertThat(updatedTask.first).isEqualTo(50) // progress updated
    assertThat(updatedTask.second).isNull() // achievement unchanged (was null)
  }

  @Test
  fun `SHOULD update progress and achievement ON updateTaskProgress WHEN both are provided`() {
    val userId = database.prepareUser()
    val taskDefinitionId = "task-def-1"
    val gameId = database.prepareGame()

    prepareCarouselTaskDefinition(taskDefinitionId, gameId)
    val taskId = prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.IN_PROGRESS, progress = 20)

    val newAchievement = """{"levels": ["level1", "level2"]}"""

    val result = runBlocking {
      underTest.updateTaskProgress(taskId, currentProgress = 20, progress = 75, achievement = newAchievement)
    }

    assertThat(result).isTrue() // optimistic lock succeeded
    val updatedTask = getTaskProgressAndAchievement(taskId)
    assertThat(updatedTask.first).isEqualTo(75) // progress updated
    assertThat(updatedTask.second).isEqualTo(newAchievement) // achievement updated
  }

  @Test
  fun `SHOULD preserve existing achievement ON updateTaskProgress WHEN achievement is not provided`() {
    val userId = database.prepareUser()
    val taskDefinitionId = "task-def-1"
    val gameId = database.prepareGame()

    prepareCarouselTaskDefinition(taskDefinitionId, gameId)
    val existingAchievement = """{"levels": ["existing_level"]}"""
    val taskId = prepareUserCarouselTask(
      userId, taskDefinitionId, UserCarouselTaskState.IN_PROGRESS,
      progress = 30, achievement = existingAchievement
    )

    val result = runBlocking {
      underTest.updateTaskProgress(taskId, currentProgress = 30, progress = 60) // no achievement parameter
    }

    assertThat(result).isTrue() // optimistic lock succeeded
    val updatedTask = getTaskProgressAndAchievement(taskId)
    assertThat(updatedTask.first).isEqualTo(60) // progress updated
    assertThat(updatedTask.second).isEqualTo(existingAchievement) // achievement preserved
  }

  @Test
  fun `SHOULD overwrite existing achievement ON updateTaskProgress WHEN new achievement is provided`() {
    val userId = database.prepareUser()
    val taskDefinitionId = "task-def-1"
    val gameId = database.prepareGame()

    prepareCarouselTaskDefinition(taskDefinitionId, gameId)
    val existingAchievement = """{"levels": ["old_level"]}"""
    val newAchievement = """{"levels": ["new_level1", "new_level2"]}"""
    val taskId = prepareUserCarouselTask(
      userId, taskDefinitionId, UserCarouselTaskState.IN_PROGRESS,
      progress = 40, achievement = existingAchievement
    )

    val result = runBlocking {
      underTest.updateTaskProgress(taskId, progress = 80, currentProgress = 40, achievement = newAchievement)
    }

    assertThat(result).isTrue() // optimistic lock succeeded
    val updatedTask = getTaskProgressAndAchievement(taskId)
    assertThat(updatedTask.first).isEqualTo(80) // progress updated
    assertThat(updatedTask.second).isEqualTo(newAchievement) // achievement overwritten
  }

  @Test
  fun `SHOULD handle zero progress ON updateTaskProgress`() {
    val userId = database.prepareUser()
    val taskDefinitionId = "task-def-1"
    val gameId = database.prepareGame()

    prepareCarouselTaskDefinition(taskDefinitionId, gameId)
    val taskId = prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.IN_PROGRESS, progress = 50)

    val result = runBlocking {
      underTest.updateTaskProgress(taskId, currentProgress = 50, progress = 0)
    }

    assertThat(result).isTrue() // optimistic lock succeeded
    val updatedTask = getTaskProgressAndAchievement(taskId)
    assertThat(updatedTask.first).isEqualTo(0) // progress set to zero
  }

  @Test
  fun `SHOULD handle maximum progress values ON updateTaskProgress`() {
    val userId = database.prepareUser()
    val taskDefinitionId = "task-def-1"
    val gameId = database.prepareGame()

    prepareCarouselTaskDefinition(taskDefinitionId, gameId, progressMax = 1000)
    val taskId = prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.IN_PROGRESS, progress = 500)

    val result = runBlocking {
      underTest.updateTaskProgress(taskId, currentProgress = 500, progress = 1000)
    }

    assertThat(result).isTrue() // optimistic lock succeeded
    val updatedTask = getTaskProgressAndAchievement(taskId)
    assertThat(updatedTask.first).isEqualTo(1000) // progress set to max value
  }

  @Test
  fun `SHOULD not affect other tasks ON updateTaskProgress`() {
    val userId = database.prepareUser()
    val taskDefinitionId = "task-def-1"
    val gameId = database.prepareGame()

    prepareCarouselTaskDefinition(taskDefinitionId, gameId)
    val taskId1 = prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.IN_PROGRESS, progress = 10)
    val taskId2 = prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.IN_PROGRESS, progress = 20)

    val result = runBlocking {
      underTest.updateTaskProgress(taskId1, currentProgress = 10, progress = 90)
    }

    assertThat(result).isTrue() // optimistic lock succeeded
    val task1 = getTaskProgressAndAchievement(taskId1)
    val task2 = getTaskProgressAndAchievement(taskId2)

    assertThat(task1.first).isEqualTo(90) // task1 updated
    assertThat(task2.first).isEqualTo(20) // task2 unchanged
  }

  @Test
  fun `SHOULD handle non-existent task ID ON updateTaskProgress`() {
    val nonExistentTaskId = UUID.randomUUID()

    val result = runBlocking {
      underTest.updateTaskProgress(nonExistentTaskId, currentProgress = 0, progress = 100)
    }

    assertThat(result).isFalse() // no rows updated
  }

  @Test
  fun `SHOULD handle empty achievement string ON updateTaskProgress`() {
    val userId = database.prepareUser()
    val taskDefinitionId = "task-def-1"
    val gameId = database.prepareGame()

    prepareCarouselTaskDefinition(taskDefinitionId, gameId)
    val taskId = prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.IN_PROGRESS, progress = 25)

    val result = runBlocking {
      underTest.updateTaskProgress(taskId, progress = 75, currentProgress = 25, achievement = "")
    }

    assertThat(result).isTrue() // optimistic lock succeeded
    val updatedTask = getTaskProgressAndAchievement(taskId)
    assertThat(updatedTask.first).isEqualTo(75) // progress updated
    assertThat(updatedTask.second).isEqualTo("") // achievement set to empty string
  }

  @Test
  fun `SHOULD return false ON updateTaskProgress WHEN current progress does not match database`() {
    val userId = database.prepareUser()
    val taskDefinitionId = "task-def-1"
    val gameId = database.prepareGame()

    prepareCarouselTaskDefinition(taskDefinitionId, gameId)
    val taskId = prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.IN_PROGRESS, progress = 50)

    // Try to update with wrong current progress (optimistic lock should fail)
    val result = runBlocking {
      underTest.updateTaskProgress(taskId, currentProgress = 30, progress = 100) // current progress is wrong
    }

    assertThat(result).isFalse() // optimistic lock failed
    val unchangedTask = getTaskProgressAndAchievement(taskId)
    assertThat(unchangedTask.first).isEqualTo(50) // progress unchanged
  }

  @Test
  fun `SHOULD return zero ON countUnclaimedTasks WHEN no unclaimed tasks exist`() {
    val userId = database.prepareUser()

    val result = runBlocking {
      underTest.countUnclaimedTasks(userId)
    }

    assertThat(result).isEqualTo(0L)
  }

  @Test
  fun `SHOULD return correct count ON countUnclaimedTasks WHEN unclaimed tasks exist`() {
    val userId = database.prepareUser()
    val taskDefinitionId = "task-def-1"
    val gameId = database.prepareGame()

    prepareCarouselTaskDefinition(taskDefinitionId, gameId)

    // Create tasks with different states
    prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.UNCLAIMED)
    prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.UNCLAIMED)
    prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.UNCLAIMED)
    prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.NEW) // should not be counted
    prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.IN_PROGRESS) // should not be counted
    prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.CLAIMED) // should not be counted
    prepareUserCarouselTask(userId, taskDefinitionId, UserCarouselTaskState.COMPLETED) // should not be counted

    val result = runBlocking {
      underTest.countUnclaimedTasks(userId)
    }

    assertThat(result).isEqualTo(3L) // only UNCLAIMED tasks counted
  }

  @Test
  fun `SHOULD not count other users tasks ON countUnclaimedTasks`() {
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()
    val taskDefinitionId = "task-def-1"
    val gameId = database.prepareGame()

    prepareCarouselTaskDefinition(taskDefinitionId, gameId)

    // Create unclaimed tasks for both users
    prepareUserCarouselTask(userId1, taskDefinitionId, UserCarouselTaskState.UNCLAIMED)
    prepareUserCarouselTask(userId1, taskDefinitionId, UserCarouselTaskState.UNCLAIMED)
    prepareUserCarouselTask(userId2, taskDefinitionId, UserCarouselTaskState.UNCLAIMED) // should not be counted for userId1

    val result1 = runBlocking { underTest.countUnclaimedTasks(userId1) }
    val result2 = runBlocking { underTest.countUnclaimedTasks(userId2) }

    assertThat(result1).isEqualTo(2L) // only userId1's tasks
    assertThat(result2).isEqualTo(1L) // only userId2's tasks
  }

  @Test
  fun `SHOULD return empty list ON findActiveTaskDefinitions WHEN no task definitions exist`() {
    val result = runBlocking {
      underTest.findActiveTaskDefinitions()
    }

    assertThat(result).isEmpty()
  }

  @Test
  fun `SHOULD return empty list ON findActiveTaskDefinitions WHEN all task definitions are disabled`() {
    val gameId1 = database.prepareGame()
    val gameId2 = database.prepareGame()

    // Create disabled task definitions
    prepareCarouselTaskDefinition("task-def-1", gameId1, enabled = false)
    prepareCarouselTaskDefinition("task-def-2", gameId2, enabled = false)

    val result = runBlocking {
      underTest.findActiveTaskDefinitions()
    }

    assertThat(result).isEmpty()
  }

  @Test
  fun `SHOULD return only enabled task definitions ON findActiveTaskDefinitions WHEN mix of enabled and disabled exist`() {
    val gameId1 = database.prepareGame()
    val gameId2 = database.prepareGame()
    val gameId3 = database.prepareGame()

    // Create mix of enabled and disabled task definitions
    prepareCarouselTaskDefinition("enabled-task-1", gameId1, enabled = true)
    prepareCarouselTaskDefinition("disabled-task", gameId2, enabled = false)
    prepareCarouselTaskDefinition("enabled-task-2", gameId3, enabled = true)

    val result = runBlocking {
      underTest.findActiveTaskDefinitions()
    }

    assertThat(result).hasSize(2)
    assertThat(result).containsExactlyInAnyOrder("enabled-task-1", "enabled-task-2")
  }

  @Test
  fun `SHOULD return all task definitions ON findActiveTaskDefinitions WHEN all are enabled`() {
    val gameId1 = database.prepareGame()
    val gameId2 = database.prepareGame()
    val gameId3 = database.prepareGame()

    // Create all enabled task definitions
    prepareCarouselTaskDefinition("task-def-1", gameId1, enabled = true)
    prepareCarouselTaskDefinition("task-def-2", gameId2, enabled = true)
    prepareCarouselTaskDefinition("task-def-3", gameId3, enabled = true)

    val result = runBlocking {
      underTest.findActiveTaskDefinitions()
    }

    assertThat(result).hasSize(3)
    assertThat(result).containsExactlyInAnyOrder("task-def-1", "task-def-2", "task-def-3")
  }

  @Test
  fun `SHOULD handle empty list ON createNewTasks WHEN no task definition IDs provided`() {
    val userId = database.prepareUser()

    runBlocking {
      underTest.createNewTasks(userId, emptyList())
    }

    // Verify no tasks were created
    val tasks = runBlocking { underTest.findVisibleCarouselTasks(userId) }
    assertThat(tasks).isEmpty()
  }

  @Test
  fun `SHOULD create tasks with correct default values ON createNewTasks`() {
    val userId = database.prepareUser()
    val gameId1 = database.prepareGame()
    val gameId2 = database.prepareGame()

    // Prepare task definitions
    prepareCarouselTaskDefinition("task-def-1", gameId1)
    prepareCarouselTaskDefinition("task-def-2", gameId2)

    val taskDefinitionIds = listOf("task-def-1", "task-def-2")

    runBlocking {
      underTest.createNewTasks(userId, taskDefinitionIds)
    }

    // Verify tasks were created with correct values
    val createdTasks = runBlocking { underTest.findVisibleCarouselTasks(userId) }
    assertThat(createdTasks).hasSize(2)

    createdTasks.forEach { task ->
      assertThat(task.userId).isEqualTo(userId)
      assertThat(task.state).isEqualTo(UserCarouselTaskState.NEW)
      assertThat(task.progress).isEqualTo(0)
      assertThat(task.achievement).isNull()
      assertThat(task.completedAt).isNull()
      assertThat(taskDefinitionIds).contains(task.taskDefinitionId)
    }

    // Verify both task definition IDs were used
    val createdTaskDefIds = createdTasks.map { it.taskDefinitionId }
    assertThat(createdTaskDefIds).containsExactlyInAnyOrder("task-def-1", "task-def-2")
  }

  @Test
  fun `SHOULD create multiple tasks for same task definition ON createNewTasks`() {
    val userId = database.prepareUser()
    val gameId = database.prepareGame()

    prepareCarouselTaskDefinition("task-def-1", gameId)

    // Create multiple tasks for the same task definition
    val taskDefinitionIds = listOf("task-def-1", "task-def-1", "task-def-1")

    runBlocking {
      underTest.createNewTasks(userId, taskDefinitionIds)
    }

    // Verify all tasks were created
    val createdTasks = runBlocking { underTest.findVisibleCarouselTasks(userId) }
    assertThat(createdTasks).hasSize(3)

    createdTasks.forEach { task ->
      assertThat(task.userId).isEqualTo(userId)
      assertThat(task.taskDefinitionId).isEqualTo("task-def-1")
      assertThat(task.state).isEqualTo(UserCarouselTaskState.NEW)
      assertThat(task.progress).isEqualTo(0)
    }
  }

  @Test
  fun `SHOULD not affect other users tasks ON createNewTasks`() {
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()
    val gameId = database.prepareGame()

    prepareCarouselTaskDefinition("task-def-1", gameId)

    // Create existing task for user2
    prepareUserCarouselTask(userId2, "task-def-1", UserCarouselTaskState.IN_PROGRESS)

    // Create new task for user1
    runBlocking {
      underTest.createNewTasks(userId1, listOf("task-def-1"))
    }

    // Verify user1 has new task
    val user1Tasks = runBlocking { underTest.findVisibleCarouselTasks(userId1) }
    assertThat(user1Tasks).hasSize(1)
    assertThat(user1Tasks.first().userId).isEqualTo(userId1)
    assertThat(user1Tasks.first().state).isEqualTo(UserCarouselTaskState.NEW)

    // Verify user2's existing task is unchanged
    val user2Tasks = runBlocking { underTest.findVisibleCarouselTasks(userId2) }
    assertThat(user2Tasks).hasSize(1)
    assertThat(user2Tasks.first().userId).isEqualTo(userId2)
    assertThat(user2Tasks.first().state).isEqualTo(UserCarouselTaskState.IN_PROGRESS)
  }

  // Helper functions
  private fun prepareCarouselTaskDefinition(
    taskDefinitionId: String,
    gameId: Int,
    title: String = "Test Task",
    icon: String = "test-icon.png",
    progressMax: Int = 100,
    order: Int = 1,
    calculator: ObjectiveProgressCalculatorType = TREASURE_MASTER_20,
    enabled: Boolean = true
  ) {
    transaction(database) {
      CarouselTaskTable.insert {
        it[CarouselTaskTable.id] = taskDefinitionId
        it[CarouselTaskTable.gameId] = gameId
        it[CarouselTaskTable.title] = title
        it[CarouselTaskTable.icon] = icon
        it[CarouselTaskTable.progressMax] = progressMax
        it[CarouselTaskTable.order] = order
        it[CarouselTaskTable.calculator] = calculator
        it[CarouselTaskTable.enabled] = enabled
      }
    }
  }

  private fun prepareUserCarouselTask(
    userId: String,
    taskDefinitionId: String,
    state: UserCarouselTaskState,
    progress: Int = 0,
    achievement: String? = null,
    completedAt: Instant? = null
  ): UUID {
    return transaction(database) {
      UserCarouselTaskTable.insert {
        it[UserCarouselTaskTable.userId] = userId
        it[UserCarouselTaskTable.taskDefinitionId] = taskDefinitionId
        it[UserCarouselTaskTable.state] = state
        it[UserCarouselTaskTable.progress] = progress
        it[UserCarouselTaskTable.achievement] = achievement
        it[UserCarouselTaskTable.completedAt] = completedAt
      }[UserCarouselTaskTable.id].value
    }
  }

  private fun getTaskState(taskId: UUID): UserCarouselTaskState {
    return transaction(database) {
      UserCarouselTaskTable
        .select { UserCarouselTaskTable.id eq taskId }
        .first()[UserCarouselTaskTable.state]
    }
  }

  private fun getTaskProgressAndAchievement(taskId: UUID): Pair<Int, String?> {
    return transaction(database) {
      UserCarouselTaskTable
        .select { UserCarouselTaskTable.id eq taskId }
        .first()
        .let { row ->
          Pair(row[UserCarouselTaskTable.progress], row[UserCarouselTaskTable.achievement])
        }
    }
  }
}