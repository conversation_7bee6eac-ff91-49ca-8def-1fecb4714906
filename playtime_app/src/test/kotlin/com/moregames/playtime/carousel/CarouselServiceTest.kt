package com.moregames.playtime.carousel

import assertk.all
import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isInstanceOf
import assertk.assertions.isNotNull
import assertk.assertions.isNull
import com.moregames.base.bus.MessageBus
import com.moregames.base.db.OptimisticLockException
import com.moregames.base.db.TransactorMock
import com.moregames.base.junit.MockExtension
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.carousel.UserCarouselTaskState.*
import com.moregames.playtime.carousel.domain.TaskDefinition
import com.moregames.playtime.carousel.domain.UserCarouselTask
import com.moregames.playtime.carousel.domain.UserCarouselTaskEntity
import com.moregames.playtime.user.challenge.progress.ObjectiveProgressCalculatorType
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoInteractions
import java.time.Instant
import java.util.*
import kotlin.test.assertFailsWith
import java.time.Duration

@ExtendWith(MockExtension::class)
class CarouselServiceTest(
  private val carouselPersistenceService: CarouselPersistenceService,
  private val timeService: TimeService,
  private val messageBus: MessageBus,
) {
  private val underTest = CarouselService(carouselPersistenceService, timeService, messageBus, TransactorMock)

  private companion object {
    const val USER_ID = "user123"
    const val GAME_ID = 1001
    val TASK_ID = UUID.randomUUID()
    val NOW = Instant.parse("2023-12-01T10:00:00Z")

    val taskDefinition = TaskDefinition(
      id = "task_def_123",
      gameId = GAME_ID,
      titleTranslation = "Complete 5 levels",
      icon = "level_icon",
      progressMax = 100,
      goal = 5,
      order = 1,
      calculator = ObjectiveProgressCalculatorType.MILESTONE,
      enabled = true,
    )
  }

  @Test
  fun `SHOULD return InProgress task ON findActiveUserTask WHEN active task exists and is InProgress`() = runBlocking {
    val taskEntity = createTaskEntity(state = IN_PROGRESS, progress = 50, achievement = "level_3")
    carouselPersistenceService.mock({ findActiveCarouselTaskPerGame(USER_ID, GAME_ID) }, taskEntity)

    val result = underTest.findActiveUserTask(USER_ID, GAME_ID)

    assertThat(result).isNotNull().isInstanceOf(UserCarouselTask.InProgress::class).all {
      transform { it.userId }.isEqualTo(USER_ID)
      transform { it.progress }.isEqualTo(50)
      transform { it.achievement }.isEqualTo("level_3")
      transform { it.definition }.isEqualTo(taskDefinition)
      transform { it.objective.progressMax }.isEqualTo(taskDefinition.progressMax)
      transform { it.objective.goal }.isEqualTo(taskDefinition.goal)
    }
  }

  @Test
  fun `SHOULD return null ON findActiveUserTask WHEN active task exists but is not InProgress`() = runBlocking {
    val taskEntity = createTaskEntity(state = NEW)
    carouselPersistenceService.mock({ findActiveCarouselTaskPerGame(USER_ID, GAME_ID) }, taskEntity)

    val result = underTest.findActiveUserTask(USER_ID, GAME_ID)

    assertThat(result).isNull()
  }

  @Test
  fun `SHOULD return null ON findActiveUserTask WHEN no active task exists`() = runBlocking {
    carouselPersistenceService.mock({ findActiveCarouselTaskPerGame(USER_ID, GAME_ID) }, null)

    val result = underTest.findActiveUserTask(USER_ID, GAME_ID)

    assertThat(result).isNull()
  }

  @Test
  fun `SHOULD return correct count ON countUnclaimedTasks`() = runBlocking {
    carouselPersistenceService.mock({ countUnclaimedTasks(USER_ID) }, 3L)

    val result = underTest.countUnclaimedTasks(USER_ID)

    assertThat(result).isEqualTo(3L)
  }

  @Test
  fun `SHOULD return zero ON countUnclaimedTasks WHEN no unclaimed tasks exist`() = runBlocking {
    carouselPersistenceService.mock({ countUnclaimedTasks(USER_ID) }, 0L)

    val result = underTest.countUnclaimedTasks(USER_ID)

    assertThat(result).isEqualTo(0L)
  }

  @Test
  fun `SHOULD update task progress and return updated task ON updateTaskProgress WHEN update succeeds`() = runBlocking {
    val inProgressTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 50, "level_3")
    val updatedTaskEntity = createTaskEntity(state = IN_PROGRESS, progress = 75, achievement = "level_4")

    carouselPersistenceService.mock({ updateTaskProgress(TASK_ID, 50, 75, "level_4") }, true)
    carouselPersistenceService.mock({ getUserCarouselTask(TASK_ID) }, updatedTaskEntity)

    val result = underTest.updateTaskProgress(inProgressTask, 75, "level_4")

    assertThat(result).isInstanceOf(UserCarouselTask.InProgress::class)
    val updatedTask = result as UserCarouselTask.InProgress
    assertThat(updatedTask.progress).isEqualTo(75)
    assertThat(updatedTask.achievement).isEqualTo("level_4")
  }

  @Test
  fun `SHOULD throw OptimisticLockException ON updateTaskProgress WHEN update fails`() = runBlocking {
    val inProgressTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 50, "level_3")

    carouselPersistenceService.mock({ updateTaskProgress(TASK_ID, 50, 75, "level_4") }, false)

    val exception = assertFailsWith<OptimisticLockException> {
      underTest.updateTaskProgress(inProgressTask, 75, "level_4")
    }

    assertThat(exception.message).isEqualTo("Carousel task $TASK_ID update failed due to optimistic locking")
  }

  @Test
  fun `SHOULD update task progress with null achievement ON updateTaskProgress`() = runBlocking {
    val inProgressTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 25, null)
    val updatedTaskEntity = createTaskEntity(state = IN_PROGRESS, progress = 50, achievement = null)

    carouselPersistenceService.mock({ updateTaskProgress(TASK_ID, 25, 50, null) }, true)
    carouselPersistenceService.mock({ getUserCarouselTask(TASK_ID) }, updatedTaskEntity)

    val result = underTest.updateTaskProgress(inProgressTask, 50, null)

    assertThat(result).isInstanceOf(UserCarouselTask.InProgress::class)
    val updatedTask = result as UserCarouselTask.InProgress
    assertThat(updatedTask.progress).isEqualTo(50)
    assertThat(updatedTask.achievement).isNull()
  }

  @Test
  fun `SHOULD call persistence service ON markTaskAsFinished`() = runBlocking {
    underTest.markTaskAsFinished(TASK_ID)

    verifyBlocking(carouselPersistenceService) { markTaskAsFinished(TASK_ID) }
  }

  @Test
  fun `SHOULD convert UNCLAIMED state task entity correctly`() = runBlocking {
    val taskEntity = createTaskEntity(state = UNCLAIMED)
    carouselPersistenceService.mock({ getUserCarouselTask(TASK_ID) }, taskEntity)

    // Create a mock InProgress task to trigger updateTaskProgress
    val inProgressTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 50, null)
    carouselPersistenceService.mock({ updateTaskProgress(TASK_ID, 50, 100, null) }, true)

    val result = underTest.updateTaskProgress(inProgressTask, 100, null)

    assertThat(result).isInstanceOf(UserCarouselTask.Unclaimed::class)
    val unclaimedTask = result as UserCarouselTask.Unclaimed
    assertThat(unclaimedTask.taskId).isEqualTo(TASK_ID)
    assertThat(unclaimedTask.userId).isEqualTo(USER_ID)
    assertThat(unclaimedTask.definition).isEqualTo(taskDefinition)
  }

  @Test
  fun `SHOULD call persistence service with active task definitions ON generateInitialTasksList WHEN active task definitions exist`() = runBlocking {
    val activeTaskDefinitions = listOf("task_def_1", "task_def_2", "task_def_3")
    carouselPersistenceService.mock({ findActiveTaskDefinitions() }, activeTaskDefinitions)

    underTest.generateInitialTasksList(USER_ID)

    verifyBlocking(carouselPersistenceService) { findActiveTaskDefinitions() }
    verifyBlocking(carouselPersistenceService) { createNewTasks(USER_ID, activeTaskDefinitions) }
  }

  @Test
  fun `SHOULD call persistence service with empty list ON generateInitialTasksList WHEN no active task definitions exist`() = runBlocking {
    val emptyTaskDefinitions = emptyList<String>()
    carouselPersistenceService.mock({ findActiveTaskDefinitions() }, emptyTaskDefinitions)

    underTest.generateInitialTasksList(USER_ID)

    verifyBlocking(carouselPersistenceService) { findActiveTaskDefinitions() }
    verifyBlocking(carouselPersistenceService) { createNewTasks(USER_ID, emptyTaskDefinitions) }
  }

  @Test
  fun `SHOULD call persistence service with single task definition ON createTask`() = runBlocking {
    val taskDefinitionId = "task_def_123"

    underTest.createTask(USER_ID, taskDefinitionId)

    verifyBlocking(carouselPersistenceService) { createNewTasks(USER_ID, listOf(taskDefinitionId)) }
  }

  @Test
  fun `SHOULD call persistence service with different user and task definition ON createTask`() = runBlocking {
    val differentUserId = "different_user_456"
    val differentTaskDefinitionId = "different_task_def_789"

    underTest.createTask(differentUserId, differentTaskDefinitionId)

    verifyBlocking(carouselPersistenceService) { createNewTasks(differentUserId, listOf(differentTaskDefinitionId)) }
  }

  @Test
  fun `SHOULD successfully claim task ON claimTask WHEN task is in UNCLAIMED state`() = runBlocking {
    val unclaimedTaskEntity = createTaskEntity(state = UNCLAIMED)
    val expectedBlockedUntil = NOW.plus(Duration.ofHours(1))
    val expectedMessageDelay = expectedBlockedUntil.minus(Duration.ofMinutes(1))

    timeService.mock({ now() }, NOW)
    carouselPersistenceService.mock({ getUserCarouselTask(TASK_ID) }, unclaimedTaskEntity)

    underTest.claimTask(TASK_ID)

    verifyBlocking(carouselPersistenceService) { getUserCarouselTask(TASK_ID) }
    verifyBlocking(carouselPersistenceService) { markTaskAsClaimed(TASK_ID, expectedBlockedUntil) }
    verifyBlocking(messageBus) { publish(CarouselRecreateTaskCommand(TASK_ID), expectedMessageDelay) }
  }

  @Test
  fun `SHOULD throw IllegalStateException ON claimTask WHEN task is in NEW state`() = runBlocking {
    val newTaskEntity = createTaskEntity(state = NEW)
    carouselPersistenceService.mock({ getUserCarouselTask(TASK_ID) }, newTaskEntity)

    val exception = assertFailsWith<IllegalStateException> {
      underTest.claimTask(TASK_ID)
    }

    assertThat(exception.message).isEqualTo("Task $TASK_ID is not in UNCLAIMED state")
    verifyBlocking(carouselPersistenceService) { getUserCarouselTask(TASK_ID) }
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD throw IllegalStateException ON claimTask WHEN task is in IN_PROGRESS state`() = runBlocking {
    val inProgressTaskEntity = createTaskEntity(state = IN_PROGRESS, progress = 50)
    carouselPersistenceService.mock({ getUserCarouselTask(TASK_ID) }, inProgressTaskEntity)

    val exception = assertFailsWith<IllegalStateException> {
      underTest.claimTask(TASK_ID)
    }

    assertThat(exception.message).isEqualTo("Task $TASK_ID is not in UNCLAIMED state")
    verifyBlocking(carouselPersistenceService) { getUserCarouselTask(TASK_ID) }
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD throw IllegalStateException ON claimTask WHEN task is in CLAIMED state`() = runBlocking {
    val claimedTaskEntity = createTaskEntity(state = CLAIMED, blockedUntil = NOW.plusSeconds(3600))
    carouselPersistenceService.mock({ getUserCarouselTask(TASK_ID) }, claimedTaskEntity)

    val exception = assertFailsWith<IllegalStateException> {
      underTest.claimTask(TASK_ID)
    }

    assertThat(exception.message).isEqualTo("Task $TASK_ID is not in UNCLAIMED state")
    verifyBlocking(carouselPersistenceService) { getUserCarouselTask(TASK_ID) }
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD throw IllegalStateException ON claimTask WHEN task is in COMPLETED state`() = runBlocking {
    val completedTaskEntity = createTaskEntity(state = COMPLETED)
    carouselPersistenceService.mock({ getUserCarouselTask(TASK_ID) }, completedTaskEntity)

    val exception = assertFailsWith<IllegalStateException> {
      underTest.claimTask(TASK_ID)
    }

    assertThat(exception.message).isEqualTo("Task $TASK_ID is not in UNCLAIMED state")
    verifyBlocking(carouselPersistenceService) { getUserCarouselTask(TASK_ID) }
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD calculate correct time values ON claimTask WHEN claiming unclaimed task`() = runBlocking {
    val unclaimedTaskEntity = createTaskEntity(state = UNCLAIMED)
    val customTime = Instant.parse("2023-06-15T14:30:45Z")
    val expectedBlockedUntil = customTime.plusSeconds(3600) // +1 hour
    val expectedMessageDelay = expectedBlockedUntil.minusSeconds(60) // -1 minute from blockedUntil

    timeService.mock({ now() }, customTime)
    carouselPersistenceService.mock({ getUserCarouselTask(TASK_ID) }, unclaimedTaskEntity)

    underTest.claimTask(TASK_ID)

    verifyBlocking(carouselPersistenceService) { markTaskAsClaimed(TASK_ID, expectedBlockedUntil) }
    verifyBlocking(messageBus) { publish(CarouselRecreateTaskCommand(TASK_ID), expectedMessageDelay) }
  }

  @Test
  fun `SHOULD handle different task ID ON claimTask WHEN claiming unclaimed task`() = runBlocking {
    val differentTaskId = UUID.randomUUID()
    val unclaimedTaskEntity = createTaskEntity(state = UNCLAIMED).copy(taskId = differentTaskId)
    val expectedBlockedUntil = NOW.plus(Duration.ofHours(1))
    val expectedMessageDelay = expectedBlockedUntil.minus(Duration.ofMinutes(1))

    timeService.mock({ now() }, NOW)
    carouselPersistenceService.mock({ getUserCarouselTask(differentTaskId) }, unclaimedTaskEntity)

    underTest.claimTask(differentTaskId)

    verifyBlocking(carouselPersistenceService) { getUserCarouselTask(differentTaskId) }
    verifyBlocking(carouselPersistenceService) { markTaskAsClaimed(differentTaskId, expectedBlockedUntil) }
    verifyBlocking(messageBus) { publish(CarouselRecreateTaskCommand(differentTaskId), expectedMessageDelay) }
  }

  private fun createTaskEntity(
    state: UserCarouselTaskState,
    progress: Int = 0,
    achievement: String? = null,
    blockedUntil: Instant? = null,
  ) = UserCarouselTaskEntity(
    taskId = TASK_ID,
    userId = USER_ID,
    state = state,
    progress = progress,
    achievement = achievement,
    completedAt = null,
    blockedUntil = blockedUntil,
    taskDefinitionId = "task_def_123",
    gameId = taskDefinition.gameId,
    titleTranslation = taskDefinition.titleTranslation,
    icon = taskDefinition.icon,
    progressMax = taskDefinition.progressMax,
    goal = taskDefinition.goal,
    order = taskDefinition.order,
    calculator = taskDefinition.calculator,
    enabled = taskDefinition.enabled,
  )
}