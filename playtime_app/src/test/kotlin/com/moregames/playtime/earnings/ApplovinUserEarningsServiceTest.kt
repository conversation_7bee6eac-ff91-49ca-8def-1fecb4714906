package com.moregames.playtime.earnings

import com.moregames.base.util.mock
import com.moregames.playtime.revenue.applovin.ApplovinRevenuePersistenceService
import com.moregames.playtime.revenue.applovin.ApplovinUserEarningsService
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoMoreInteractions
import java.math.BigDecimal

class ApplovinUserEarningsServiceTest {

  private val applovinRevenuePersistenceService: ApplovinRevenuePersistenceService = mock()

  private val service = ApplovinUserEarningsService(applovinRevenuePersistenceService)

  @Test
  fun `SHOULD NOT transfer revenue ON onCalculateEarningsAndNotifyTriggered WHEN no applovin revenue available`() {
    applovinRevenuePersistenceService.mock({ loadImportedApplovinApplicationIds() }, emptySet())

    runTest {
      service.processImportedApplovinRevenue()
    }

    verifyBlocking(applovinRevenuePersistenceService) { loadImportedApplovinApplicationIds() }
    verifyNoMoreInteractions(applovinRevenuePersistenceService)
  }

  @Test
  fun `SHOULD NOT transfer revenue ON onCalculateEarningsAndNotifyTriggered WHEN applovin revenue is less than threshold`() {
    applovinRevenuePersistenceService.mock({ loadImportedApplovinApplicationIds() }, setOf("app1"))
    applovinRevenuePersistenceService.mock({ loadApplovinRevenueByApplicationId(any()) }, mapOf("app1" to BigDecimal("19"), "app2" to BigDecimal("81")))

    runTest {
      service.processImportedApplovinRevenue()
    }

    verifyBlocking(applovinRevenuePersistenceService) { loadImportedApplovinApplicationIds() }
    verifyBlocking(applovinRevenuePersistenceService) { loadApplovinRevenueByApplicationId(any()) }
    verifyNoMoreInteractions(applovinRevenuePersistenceService)
  }

  @Test
  fun `SHOULD transfer revenue ON onCalculateEarningsAndNotifyTriggered WHEN NOT applovin revenue is less than threshold`() {
    applovinRevenuePersistenceService.mock({ loadImportedApplovinApplicationIds() }, setOf("app1"))
    applovinRevenuePersistenceService.mock({ loadApplovinRevenueByApplicationId(any()) }, mapOf("app1" to BigDecimal("81"), "app2" to BigDecimal("19")))

    runTest {
      service.processImportedApplovinRevenue()
    }

    verifyBlocking(applovinRevenuePersistenceService) { transferApplovinRevenuesFromExternal() }
  }

  @Test
  fun `SHOULD transfer revenue ON onCalculateEarningsAndNotifyTriggered WHEN applovin revenue of previous day is zero`() {
    applovinRevenuePersistenceService.mock({ loadImportedApplovinApplicationIds() }, setOf("app1"))
    applovinRevenuePersistenceService.mock({ loadApplovinRevenueByApplicationId(any()) }, mapOf("app1" to BigDecimal.ZERO))

    runTest {
      service.processImportedApplovinRevenue()
    }

    verifyBlocking(applovinRevenuePersistenceService) { transferApplovinRevenuesFromExternal() }
  }
}