package com.moregames.playtime.earnings

import assertk.assertThat
import assertk.assertions.isBetween
import assertk.assertions.isEqualByComparingTo
import assertk.assertions.isEqualTo
import com.google.protobuf.BoolValue
import com.justplayapps.base.Common
import com.justplayapps.playtime.rewarding.proto.*
import com.justplayapps.service.rewarding.earnings.EarningsCalculationsService
import com.justplayapps.service.rewarding.earnings.EarningsCalculationsService.Companion.DEFAULT_RANDOM_MULTIPLIERS
import com.justplayapps.service.rewarding.earnings.EarningsCalculationsService.Companion.cashoutPeriodEarningsMinLimit
import com.justplayapps.service.rewarding.earnings.EarningsCalculationsService.Companion.minimalPossibleRevenueToEarningsRatio
import com.justplayapps.service.rewarding.earnings.EarningsCalculationsService.Companion.offerwallRevenueSpecialCut
import com.justplayapps.service.rewarding.earnings.EarningsCalculationsService.Companion.quotasValues
import com.justplayapps.service.rewarding.earnings.EarningsCalculationsService.Companion.revenueCutFactor
import com.justplayapps.service.rewarding.earnings.EmExperimentBaseService
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService
import com.justplayapps.service.rewarding.earnings.dto.UserCurrencyEarnings
import com.justplayapps.service.rewarding.facade.AbTestingFacade
import com.moregames.base.abtesting.AbTestingService.QuotasRndRange
import com.moregames.base.abtesting.ClientExperiment.EARNINGS_MODEL_V2
import com.moregames.base.abtesting.Variations.EM2_REV_SHARE_50_DQ_HT
import com.moregames.base.dto.AppPlatform
import com.moregames.base.util.RandomGenerator
import com.moregames.base.util.mock
import com.moregames.base.util.toProto
import com.moregames.playtime.cashstreak.model.CashStreakRewardType
import com.moregames.playtime.earnings.dto.UserEarningsQuotasDto
import com.moregames.playtime.user.fraudscore.FraudScoreService.Companion.FRAUD_SCORE_BAN_THRESHOLD
import com.moregames.playtime.utils.USD
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.spy
import org.mockito.kotlin.verify
import java.math.BigDecimal
import java.time.Instant

class EarningsCalculationsServiceTest {
  private val userEarningsPersistenceService: UserEarningsPersistenceService = mock()

  private val randomGenerator: RandomGenerator = mock()
  private val abTestingFacade: AbTestingFacade = mock()
  private val emExperimentBaseService: EmExperimentBaseService = mock()

  private val service = spy(
    EarningsCalculationsService(
      userEarningsPersistenceService = userEarningsPersistenceService,
      randomGenerator = randomGenerator,
      abTestingFacade = abTestingFacade,
      emExperimentBaseService = emExperimentBaseService,
    )
  )

  private companion object {
    const val USER_ID = "userId"
    val maxEarnings = BigDecimal("30.00")

    val userData = getUserDataResponse {
      userMaxEarningsAmount = maxEarnings.toProto()
      fraudScore = 0.0
      isUserBlocked = false
      isHighlyTrustedUser = true
      isUserRestricted = false
      userCountryCode = "US"
      ctsProfileMatchStatus = BoolValue.of(true)
      iosEarningsIncreasePercentage = 30
      platform = Common.AppPlatformProto.ANDROID
    }
  }

  @BeforeEach
  fun before() {
    userEarningsPersistenceService.mock({ loadUnpaidUserCurrencyEarnings(USER_ID) }, UserCurrencyEarnings(BigDecimal.ZERO, USD, BigDecimal.ZERO))
    `when`(randomGenerator.nextDouble(any(), any())).thenCallRealMethod()
    emExperimentBaseService.mock({ isEm2DoubleQuotasNoOw(USER_ID) }, false)
  }

  @ParameterizedTest
  @ValueSource(doubles = [-150.0, -100.0, -50.0, -10.0, 0.0])
  fun `SHOULD cut 50 percents of revenue and keep max daily earnings WHEN FS score is zero or less`(fraudScore: Double) {
    val actual = runBlocking {
      service.calculateRevenueCut(fraudScore, revenueCutFactor, userData.isUserWhitelisted)
    }

    assertThat(actual).isEqualByComparingTo(BigDecimal("0.5"))
  }

  @ParameterizedTest
  @ValueSource(doubles = [FRAUD_SCORE_BAN_THRESHOLD.toDouble(), FRAUD_SCORE_BAN_THRESHOLD + 10.0, FRAUD_SCORE_BAN_THRESHOLD + 100.0])
  fun `SHOULD cut 95 percents of revenue and use 1 cent as max daily earnings WHEN FS score is at threshold or above`(fraudScore: Double) {
    val actual = runBlocking {
      service.calculateRevenueCut(fraudScore, revenueCutFactor, userData.isUserWhitelisted)
    }

    assertThat(actual).isEqualByComparingTo(BigDecimal("0.05"))
  }

  @Test
  fun `SHOULD cut 10 percents of revenue and use 20 percent of initial max daily earnings WHEN FS score is at 80 percents of threshold`() {
    val actual = runBlocking {
      service.calculateRevenueCut(FRAUD_SCORE_BAN_THRESHOLD * 0.8, revenueCutFactor, userData.isUserWhitelisted)
    }

    assertThat(actual).isEqualByComparingTo(BigDecimal("0.1"))
  }

  @Test
  fun `SHOULD cut 20 percents of revenue and use 40 percent of initial max daily earnings WHEN FS score is at 60 percents of threshold`() {
    val actual = runBlocking {
      service.calculateRevenueCut(FRAUD_SCORE_BAN_THRESHOLD * 0.6, revenueCutFactor, userData.isUserWhitelisted)
    }

    assertThat(actual).isEqualByComparingTo(BigDecimal("0.2"))
  }

  @Test
  fun `SHOULD cut 37,5 percents of revenue and use 75 percent of initial max daily earnings WHEN FS score is at 25 percents of threshold`() {
    val actual = runBlocking {
      service.calculateRevenueCut(FRAUD_SCORE_BAN_THRESHOLD * 0.25, revenueCutFactor, userData.isUserWhitelisted)
    }

    assertThat(actual).isEqualByComparingTo(BigDecimal("0.375"))
  }

  @Test
  fun `SHOULD convert revenue to earnings ON convertRevenueToEarnings`() {
    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("1.00"), BigDecimal.ZERO, userData)
    }

    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, BigDecimal("0.5"))
    assertThat(actual.quotasDto!!.quotas).isEqualTo("1,1,1,1,1,0,1,1".split(","))
  }

  @Test
  fun `SHOULD convert revenue to zero earnings ON convertRevenueToEarnings WHEN user was blocked`() {
    val userDataResponse = userData.copy { isUserBlocked = true }

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("1.00"), BigDecimal.ZERO, userDataResponse)
    }

    assertThat(actual.earningsSum).isEqualByComparingTo(BigDecimal.ZERO)
  }

  @Test
  fun `SHOULD limit user earnings ON convertRevenueToEarnings WHEN total active balance is almost reached`() {
    userEarningsPersistenceService.mock({ loadUnpaidUserCurrencyEarnings(USER_ID) }, UserCurrencyEarnings(BigDecimal("29.00"), USD, BigDecimal("29.00")))

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("10.00"), BigDecimal.ZERO, userData)
    }

    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, BigDecimal("1.0"))
  }

  @Test
  fun `SHOULD return empty earnings ON convertRevenueToEarnings WHEN total active balance is exceeded`() {
    userEarningsPersistenceService.mock({ loadUnpaidUserCurrencyEarnings(USER_ID) }, UserCurrencyEarnings(BigDecimal("30.00"), USD, BigDecimal("30.00")))

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("10.00"), BigDecimal.ZERO, userData)
    }

    assertThat(actual.earningsSum).isEqualByComparingTo(BigDecimal.ZERO)
  }

  @Test
  fun `SHOULD cut 95 percents of revenue WHEN FS score is over threshold`() {
    val userDataResponse = userData.copy { fraudScore = FRAUD_SCORE_BAN_THRESHOLD + 10.0 }

    val userEarningsAmount = BigDecimal("0.1")
    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, userEarningsAmount, BigDecimal.ZERO, userDataResponse)
    }

    assertThat(actual.earningsSum).isEqualByComparingTo(minimalPossibleRevenueToEarningsRatio * userEarningsAmount)
  }

  @Test
  fun `SHOULD cut revenue to minimal 0p01 WHEN FS score is over threshold`() {
    val userDataResponse = userData.copy { fraudScore = FRAUD_SCORE_BAN_THRESHOLD + 10.0 }

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("100500"), BigDecimal.ZERO, userDataResponse)
    }

    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, cashoutPeriodEarningsMinLimit)
  }

  @Test
  fun `SHOULD never make earnings less than 0p01 WHEN FS score is over threshold`() {
    val userDataResponse = userData.copy { fraudScore = FRAUD_SCORE_BAN_THRESHOLD + 10.0 }

    randomGenerator.mock({ nextDouble(0.0095, 0.0105) }, 0.0095)

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("100500"), BigDecimal.ZERO, userDataResponse)
    }

    verify(randomGenerator).nextDouble(0.0095, 0.0105)
    assertThat(actual.earningsSum).isEqualByComparingTo(cashoutPeriodEarningsMinLimit)
  }

  @Test
  fun `SHOULD cut revenue to zero WHEN FS score is over threshold and active balance is exceeded`() {
    val userDataResponse = userData.copy { fraudScore = FRAUD_SCORE_BAN_THRESHOLD + 10.0 }

    userEarningsPersistenceService.mock({ loadUnpaidUserCurrencyEarnings(USER_ID) }, UserCurrencyEarnings(BigDecimal("30.00"), USD, BigDecimal("30.00")))

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("10.00"), BigDecimal.ZERO, userDataResponse)
    }

    assertThat(actual.earningsSum).isEqualByComparingTo(BigDecimal.ZERO)
  }

  @Test
  fun `SHOULD calculate earnings as user have no FS ON convertRevenueToEarnings WHEN user is in the whitelist`() {
    val userDataResponse = userData.copy {
      fraudScore = FRAUD_SCORE_BAN_THRESHOLD + 100500.0
      isUserWhitelisted = true
    }

    val expectedEarnings = BigDecimal("4.00") * revenueCutFactor

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("4.00"), BigDecimal.ZERO, userDataResponse)
    }

    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, expectedEarnings)
  }

  @ParameterizedTest
  @ValueSource(strings = ["IOS", "IOS_WEB"])
  fun `SHOULD temporarily calculate increased earnings for IOS users ON convertRevenueToEarnings`(platform: AppPlatform) {
    val expectedEarnings = BigDecimal("3.00") * revenueCutFactor * BigDecimal("1.3")
    val userDataResponse = userData.copy { this.platform = platform.toProto() }

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("3.00"), BigDecimal.ZERO, userDataResponse)
    }

    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, expectedEarnings)
  }

  @Test
  fun `SHOULD calculate earnings using increased cut ratio for users with active booster ON convertRevenueToEarnings`() {
    val expectedEarnings = BigDecimal("3.00") * revenueCutFactor * BigDecimal("1.3")

    val userDataResponse = userData.copy {
      currentBoosters.addAll(
        listOf(
          cashStreakRewardProto {
            achievementDay = 1000
            type = CashStreakRewardType.EARNING_POWER.toProto()
            value = BigDecimal("30").toProto()
            bigReward = true
          }
        )
      )
    }

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("3.00"), BigDecimal.ZERO, userDataResponse)
    }

    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, expectedEarnings)
  }

  @Test
  fun `SHOULD calculate as usual ON convertRevenueToEarnings WHEN user is highly trusted but no offerwall revenue`() {
    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("15.00"), BigDecimal.ZERO, userData)
    }

    assertThat(actual.earningsSum).isBetween(BigDecimal("1.9"), BigDecimal("2.1"))
  }

  @Test
  fun `SHOULD calculate as usual ON convertRevenueToEarnings WHEN user is highly trusted but total revenue is low`() {
    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("3.00"), BigDecimal("0.50"), userData)
    }

    assertThat(actual.earningsSum).isEqualByComparingTo(BigDecimal("1.5"))
  }

  @Test
  fun `SHOULD calculate as usual ON convertRevenueToEarnings WHEN user is not highly trusted`() {
    val userDataResponse = userData.copy { this.isHighlyTrustedUser = false }

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("40.00"), BigDecimal("5.00"), userDataResponse)
    }

    assertThat(actual.earningsSum).isBetween(BigDecimal("1.9"), BigDecimal("2.1"))
  }

  @ParameterizedTest
  @CsvSource(delimiter = '$', value = ["6.0$6.0", "5.0$2.0", "15.0$1.0", "15.0$8.0", "15.0$12.0", "15.0$15.0", "40.0$5.0"])
  fun `SHOULD calculate using offerwallRevenueSpecialCut WHEN user is highly trusted AND revenue above 4`(totalRevenue: String, ofwRevenue: String) {
    val usualCutEarnings = BigDecimal("2.0").min(BigDecimal(totalRevenue) * revenueCutFactor)
    val specialCutEarnings = (BigDecimal(totalRevenue) - BigDecimal("4.0")).min(BigDecimal(ofwRevenue)) * offerwallRevenueSpecialCut
    val expectedEarnings = usualCutEarnings + specialCutEarnings

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal(totalRevenue), BigDecimal(ofwRevenue), userData)
    }

    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, expectedEarnings)
  }

  @Test
  fun `SHOULD convert revenue to earnings ON convertRevenueToEarnings WHEN user is on double quotas`() {
    emExperimentBaseService.mock({ isEm2DoubleQuotasNoOw(USER_ID) }, true)

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("1.00"), BigDecimal.ZERO, userData)
    }

    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, BigDecimal("0.5"))
    assertThat(actual.quotasDto!!.quotas).isEqualTo("1,1,1,1,0,1,1,1".split(","))
  }

  @Test
  fun `SHOULD convert revenue to earnings ON convertRevenueToEarnings WHEN user is on double quotas AND ow revenue`() {
    emExperimentBaseService.mock({ isEm2DoubleQuotasNoOw(USER_ID) }, true)

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("1.00"), BigDecimal("1.00"), userData)
    }

    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, BigDecimal("0.5"))
    assertThat(actual.quotasDto!!.quotas).isEqualTo("1,1,1,1,1,0,1,1".split(","))
  }

  @Test
  fun `SHOULD calculate as usual ON convertRevenueToEarnings WHEN user is on double quotas AND highly trusted AND NOT offerwall revenue`() {
    emExperimentBaseService.mock({ isEm2DoubleQuotasNoOw(USER_ID) }, true)

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("15.00"), BigDecimal.ZERO, userData)
    }

    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, BigDecimal("4.0"))
  }

  @Test
  fun `SHOULD calculate as usual ON convertRevenueToEarnings WHEN user is on double quotas AND user is highly trusted AND total revenue is low`() {
    emExperimentBaseService.mock({ isEm2DoubleQuotasNoOw(USER_ID) }, true)

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("3.00"), BigDecimal("0.50"), userData)
    }

    assertThat(actual.earningsSum).isEqualByComparingTo(BigDecimal("1.425"))
  }

  @Test
  fun `SHOULD calculate as usual ON convertRevenueToEarnings WHEN user is on double quotas AND NOT highly trusted`() {
    emExperimentBaseService.mock({ isEm2DoubleQuotasNoOw(USER_ID) }, true)

    val userDataResponse = userData.copy { this.isHighlyTrustedUser = false }

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("40.00"), BigDecimal("5.00"), userDataResponse)
    }

    assertThat(actual.earningsSum).isBetween(BigDecimal("1.9"), BigDecimal("2.1"))
  }

  @ParameterizedTest
  @CsvSource(
    delimiter = '$',
    value = [
      "3.0$1.0$1.35", "3.0$2.0$1.5", "3.0$3.0$1.5",
      "4.0$1.0$2.0", "4.0$2.0$2.0", "4.0$3.0$2.0", "4.0$4.0$2.0",
      "5.0$1.0$2.35", "5.0$2.0$2.35", "5.0$3.0$2.35", "5.0$4.0$2.35", "5.0$5.0$2.35",
      "6.0$1.0$3.0", "6.0$6.0$2.7",
      "7.0$1.0$3.5", "7.0$7.0$3.05",
      "8.0$1.0$4.0", "8.0$8.0$3.4",
      "9.0$1.0$4.35", "9.0$9.0$3.75",
      "10.0$1.0$4.35", "10.0$10.0$4.1",
      "15.0$1.0$4.35", "15.0$8.0$6.3", "15.0$12.0$5.85", "15.0$15.0$5.85",
      "40.0$5.0$5.75"
    ]
  )
  fun `SHOULD calculate using offerwallRevenueSpecialCut WHEN user is on double quotas AND highly trusted AND revenue is big`(
    totalRevenue: String,
    ofwRevenue: String,
    expectedEarningsString: String,
  ) {
    val expectedEarnings = BigDecimal(expectedEarningsString)

    emExperimentBaseService.mock({ isEm2DoubleQuotasNoOw(USER_ID) }, true)

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal(totalRevenue), BigDecimal(ofwRevenue), userData)
    }

    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, expectedEarnings)
  }

  @Test
  fun `SHOULD not exceed maxCashoutAmount with additional earnings WHEN user is highly trusted`() {
    val unpaidEarnings = BigDecimal("25.00")
    userEarningsPersistenceService.mock({ loadUnpaidUserCurrencyEarnings(USER_ID) }, UserCurrencyEarnings(unpaidEarnings, USD, unpaidEarnings))

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("35.00"), BigDecimal("25.00"), userData)
    }

    assertThat(actual.earningsSum).isEqualByComparingTo(maxEarnings - unpaidEarnings) // 30 - 25 = 5$
  }

  @ParameterizedTest
  @CsvSource(delimiter = '$', value = ["1,1,1,1,1,1,1,1$2.0", "1,1,1,1,1,1,1,0$1.0", "1,1,1,1,1,0,0,0$0.25", "1,1,0,0,0,0,0,0$0.04"])
  fun `SHOULD use max available quota for calculation WHEN user is an earnings quotas participant and earn big revenue`(
    quotasString: String,
    quotaValue: Double
  ) {
    setupForUsingEarningsQuotas(quotasString)

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("100500"), BigDecimal.ZERO, userData)
    }

    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, quotaValue)
  }

  @ParameterizedTest
  @CsvSource(
    delimiter = '$', value = [
      "1,1,1,1,1,1,1,1$5.0$1,1,1,1,1,1,1,0$2.0",
      "1,1,1,1,1,1,1,1$2.0$1,1,1,1,1,1,0,1$1.0",
      "1,1,1,1,1,0,0,0$2.0$1,1,1,1,0,0,0,0$0.25",
      "1,1,1,1,1,1,1,1$1.7$1,1,1,1,1,1,0,1$0.85",
      "1,1,1,1,1,1,1,1$0.7$1,1,1,1,1,0,1,1$0.35",
      "1,1,1,1,1,0,1,1$0.7$1,1,1,1,1,0,0,1$0.35",
      "1,0,0,0,0,0,0,0$0.7$0,0,0,0,0,0,0,0$0.02",
      "0,0,0,0,0,0,0,0$0.7$0,0,0,0,0,0,0,0$0.01",
    ]
  )
  fun `SHOULD use right available quota for calculation WHEN user is an earnings quotas participant and earn some revenue`(
    quotasString: String,
    revenue: Double,
    quotasAfter: String,
    earnings: Double
  ) {
    setupForUsingEarningsQuotas(quotasString)
    val userRevenue = revenue.toBigDecimal()

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, userRevenue, BigDecimal.ZERO, userData)
    }

    assertThat(actual.quotasDto!!.quotas).isEqualTo(quotasAfter.split(","))
    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, earnings)
  }

  @Test
  fun `SHOULD respect max user balance limit WHEN user is an earnings quotas participant`() {
    setupForUsingEarningsQuotas()
    userEarningsPersistenceService.mock({ loadUnpaidUserCurrencyEarnings(USER_ID) }, UserCurrencyEarnings(BigDecimal("29.6"), USD, BigDecimal("29.6")))

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal.TEN, BigDecimal.ZERO, userData)
    }

    assertThat(actual.quotasDto!!.quotas).isEqualTo("1,1,1,1,1,0,1,1".split(","))
    assertThat(actual.earningsSum).isEqualByComparingTo(BigDecimal("0.4"))
  }

  @Test
  fun `SHOULD calculate using FS quotas limiting WHEN user is an earnings quotas participant`() {
    setupForUsingEarningsQuotas()
    val userDataResponse = userData.copy { fraudScore = 152.0 } //0.1 revenue cut + 2$ * 0.2 = 0.4$ as any quota threshold

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal.TEN, BigDecimal.ZERO, userDataResponse)
    }

    assertThat(actual.quotasDto!!.quotas).isEqualTo("1,1,1,1,1,1,1,0".split(","))
    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, BigDecimal("0.4"))
  }

  @Test
  fun `SHOULD calculate using FS quotas reducing WHEN user is an earnings quotas participant`() {
    setupForUsingEarningsQuotas()
    val userDataResponse = userData.copy { fraudScore = 152.0 } //0.1 revenue cut + 2$ * 0.2 = 0.4$ as any quota threshold

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal.ONE, BigDecimal.ZERO, userDataResponse)
    }

    assertThat(actual.quotasDto!!.quotas).isEqualTo("1,1,1,0,1,1,1,1".split(","))
    assertThat(actual.earningsSum).isEqualByComparingTo(BigDecimal("0.1"))
  }

  @ParameterizedTest
  @CsvSource(delimiter = '$', value = ["1,1,1,1,1,1,1,1$6.2", "1,1,1,1,1,1,0,0$5.75", "1,1,1,1,1,0,0,0$5.5"])
  fun `SHOULD calculate additional earnings WHEN user is highly trusted (big revenue)`(quotasString: String, earnings: Double) {
    setupForUsingEarningsQuotas(quotasString)

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("16.00"), BigDecimal("15.00"), userData)
    }

    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, earnings)
  }

  @ParameterizedTest
  @CsvSource(delimiter = '$', value = ["1,1,1,1,1,1,1,1$2.7", "1,1,1,1,1,1,0,0$2.25", "1,1,1,1,1,0,0,0$2.175"])
  fun `SHOULD calculate additional earnings WHEN user is highly trusted (ofw only)`(quotasString: String, earnings: Double) {
    setupForUsingEarningsQuotas(quotasString)

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("6.00"), BigDecimal("6.00"), userData)
    }

    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, earnings)
  }

  @ParameterizedTest
  @CsvSource(delimiter = '$', value = ["1,1,1,1,1,1,1,1$1.5", "1,1,1,1,1,1,1,0$1.35", "1,1,1,1,1,0,0,0$0.95"])
  fun `SHOULD calculate additional earnings WHEN user is highly trusted (small revenue)`(quotasString: String, earnings: Double) {
    setupForUsingEarningsQuotas(quotasString)

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("3.00"), BigDecimal("2.00"), userData)
    }

    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, earnings)
  }

  private fun setupForUsingEarningsQuotas(quotasString: String = "1,1,1,1,1,1,1,1") {
    userEarningsPersistenceService.mock({ getUserQuotas(USER_ID) }, UserEarningsQuotasDto(USER_ID, quotasString.split(","), Instant.now().plusSeconds(100600L)))
  }


  @Test
  fun `SHOULD respect max user balance limit WHEN there are some limits And Quotas Per Country Tier`() {
    setupForUsingEarningsQuotas()

    val userDataResponse = userData.copy {
      userMaxEarningsAmount = (maxEarnings * BigDecimal("0.5")).toProto()
      countryTierSettings = countryTierSettingsProto { // 30 * 0.5 = 15$ max unpaid earnings + usual quotas
        maxCashoutAmountMultiplier = BigDecimal("0.50").toProto()
        dailyEarningsQuotas.addAll(quotasValues.map { it.toProto() })
      }
    }
    userEarningsPersistenceService.mock({ loadUnpaidUserCurrencyEarnings(USER_ID) }, UserCurrencyEarnings(BigDecimal("14.6"), USD, BigDecimal("14.6")))

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal.TEN, BigDecimal.ZERO, userDataResponse)
    }

    assertThat(actual.quotasDto!!.quotas).isEqualTo("1,1,1,1,1,0,1,1".split(","))
    assertThat(actual.earningsSum).isEqualByComparingTo(BigDecimal("0.4"))
  }

  @ParameterizedTest
  @CsvSource(
    delimiter = '$', value = [
      "1,1,1,1,1,1,1,1$5.0$1,1,1,1,1,1,1,0$1.0",
      "1,1,1,1,1,1,1,1$1.0$1,1,1,1,1,1,0,1$0.5",
      "1,1,1,1,1,0,0,0$2.0$1,1,1,1,0,0,0,0$0.13",
      "1,1,1,1,1,1,1,1$1.7$1,1,1,1,1,1,1,0$0.85",
      "1,1,1,1,1,1,1,1$0.7$1,1,1,1,1,1,0,1$0.35",
      "1,1,1,1,1,1,0,1$0.7$1,1,1,1,1,1,0,0$0.35",
      "1,0,0,0,0,0,0,0$0.7$0,0,0,0,0,0,0,0$0.01",
      "0,0,0,0,0,0,0,0$0.7$0,0,0,0,0,0,0,0$0.01",
    ]
  )
  fun `SHOULD use experimental quotas for calculation WHEN there are some limits And Quotas Per Country Tier`(
    quotasString: String,
    revenue: Double,
    quotasAfter: String,
    earnings: Double
  ) {
    val expQuotas = "0.01,0.02,0.03,0.06,0.13,0.25,0.50,1.00".split(",").map { it.toBigDecimal() }

    val userDataResponse = userData.copy {
      userMaxEarningsAmount = (maxEarnings * BigDecimal("0.5")).toProto()
      countryTierSettings = countryTierSettingsProto { //usual max unpaid earnings limit, but exp quotas values
        maxCashoutAmountMultiplier = BigDecimal("1.00").toProto()
        dailyEarningsQuotas.addAll(expQuotas.map { it.toProto() })
      }
    }
    setupForUsingEarningsQuotas(quotasString)
    val userRevenue = revenue.toBigDecimal()

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, userRevenue, BigDecimal.ZERO, userDataResponse)
    }

    assertThat(actual.quotasDto!!.quotas).isEqualTo(quotasAfter.split(","))
    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, earnings)
  }

  @Test
  fun `SHOULD reduce quotas values by 70 percent but not less minimal value WHEN the user has cts_profile_match false`() {
    setupForUsingEarningsQuotas()
    val userDataResponse = userData.copy { ctsProfileMatchStatus = BoolValue.of(false) }

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal.TEN, BigDecimal.ZERO, userDataResponse)
    }

    val expectedQuotas = quotasValues
      .map { value -> value.multiply(BigDecimal("0.15")) }
      .map { it.max(BigDecimal("0.01")) }

    assertThat(actual.quotasDto!!.quotas).isEqualTo("1,1,1,1,1,1,1,0".split(","))
    assertThat(actual.quotasDto!!.values).isEqualTo(expectedQuotas)
    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, BigDecimal("0.30"))
  }

  @Test
  fun `SHOULD not reduce quotas values by 70 percent WHEN the user has cts_profile_match null`() {
    setupForUsingEarningsQuotas()
    val userDataResponse = userData.toBuilder().clearCtsProfileMatchStatus().build().copy {
      isUserRestricted = false
    }

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal.TEN, BigDecimal.ZERO, userDataResponse)
    }
    assertThat(actual.quotasDto!!.quotas).isEqualTo("1,1,1,1,1,1,1,0".split(","))
    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, BigDecimal("2.00"))
  }

  @Test
  fun `SHOULD reduce quotas values by 70 percent but not less minimal WHEN the user is restricted`() {
    setupForUsingEarningsQuotas()
    val userDataResponse = userData.toBuilder().clearCtsProfileMatchStatus().build().copy {
      isUserRestricted = true
    }
    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal.TEN, BigDecimal.ZERO, userDataResponse)
    }

    val expectedQuotas = quotasValues
      .map { value -> value.multiply(BigDecimal("0.15")) }
      .map { it.max(BigDecimal("0.01")) }

    assertThat(actual.quotasDto!!.quotas).isEqualTo("1,1,1,1,1,1,1,0".split(","))
    assertThat(actual.quotasDto!!.values).isEqualTo(expectedQuotas)
    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, BigDecimal("0.30"))
  }

  @Test
  fun `SHOULD use experimental quotas limits ON convertRevenueToEarnings WHEN user is higher_daily_quotas participant`() {
    setupForUsingEarningsQuotas()
    val expRndRange = QuotasRndRange(3.0, 3.01)
    val userDataResponse = userData.copy {
      higherQuotasRndRange = higherQuotasRndRange {
        lowerMultiplier = expRndRange.lowerMultiplier
        upperMultiplier = expRndRange.upperMultiplier
      }
    }

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("30.0"), BigDecimal.ZERO, userDataResponse)
    }

    assertThat(actual.earningsSum).isBetween(BigDecimal("6.0"), BigDecimal("6.02")) // 6.02 = 2 (default quota) * 3.01 exp multiplier
  }

  @Test
  fun `SHOULD NOT use experimental quotas limits ON convertRevenueToEarnings WHEN user is higher_daily_quotas participant BUT is a restricted user`() {
    setupForUsingEarningsQuotas()
    val expRndRange = QuotasRndRange(3.0, 3.01)

    val userDataResponse = userData.copy {
      higherQuotasRndRange = higherQuotasRndRange {
        lowerMultiplier = expRndRange.lowerMultiplier
        upperMultiplier = expRndRange.upperMultiplier
      }
      isUserRestricted = true
    }

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("30.0"), BigDecimal.ZERO, userDataResponse)
    }

    //we don't use multiplied quota for restricted users but still use reduced quota, so experiment doesn't affect the penalty
    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, BigDecimal("0.3"))
  }

  @Test
  fun `SHOULD use higher quotas ON convertRevenueToEarnings WHEN user is HT AND em2_rev_share_50_dq_ht participant`() {
    setupForUsingEarningsQuotas()

    abTestingFacade.mock({ assignedVariation(USER_ID, EARNINGS_MODEL_V2) }, EM2_REV_SHARE_50_DQ_HT)
    val userDataResponse = userData.copy { isHighlyTrustedUser = true }

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("30.0"), BigDecimal.ZERO, userDataResponse)
    }

    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, BigDecimal("4.0")) // 4 = 2 (default quota) * 2 exp multiplier
  }

  @Test
  fun `SHOULD use higher quotas ON convertRevenueToEarnings WHEN user is HT AND em2_rev_share_50_dq_ht participant AND ow revenue`() {
    setupForUsingEarningsQuotas()

    abTestingFacade.mock({ assignedVariation(USER_ID, EARNINGS_MODEL_V2) }, EM2_REV_SHARE_50_DQ_HT)
    val userDataResponse = userData.copy { isHighlyTrustedUser = true }

    val actual = runBlocking {
      service.convertRevenueToEarnings(USER_ID, BigDecimal("30.0"), BigDecimal("30.0"), userDataResponse)
    }

    assertIsBetweenDefaultRandomMultiplier(actual.earningsSum, BigDecimal("11.7")) // 4 = 2 (default quota) * 2 exp multiplier + 0.35 * (30 - 8) ow
  }

  private fun assertIsBetweenDefaultRandomMultiplier(actual: BigDecimal, expected: BigDecimal) {
    assertThat(actual)
      .isBetween(
        expected * DEFAULT_RANDOM_MULTIPLIERS.lowerMultiplier.toBigDecimal(),
        expected * DEFAULT_RANDOM_MULTIPLIERS.upperMultiplier.toBigDecimal()
      )
  }

  private fun assertIsBetweenDefaultRandomMultiplier(actual: BigDecimal, expected: Double) {
    assertIsBetweenDefaultRandomMultiplier(actual, expected.toBigDecimal())
  }
}

