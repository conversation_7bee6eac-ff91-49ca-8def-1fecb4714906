package com.moregames.playtime.earnings

import assertk.assertThat
import assertk.assertions.isEqualByComparingTo
import assertk.assertions.isFalse
import assertk.assertions.isTrue
import com.justplayapps.service.rewarding.earnings.StashFixPersistenceService
import com.justplayapps.service.rewarding.earnings.table.UserStashFixGivenTable
import com.moregames.base.table.DatabaseExtension
import com.moregames.playtime.user.prepareUser
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.math.BigDecimal
import java.util.*

@ExperimentalCoroutinesApi
@ExtendWith(DatabaseExtension::class)
class StashFixPersistenceServiceTest(private val database: Database) {

  private val underTest = StashFixPersistenceService(
    database = database
  )

  @Test
  fun `SHOULD return false ON isStashFixGiven WHEN no entry`() {
    val userId = UUID.randomUUID().toString()

    runBlocking { underTest.isStashFixGiven(userId) }.let { actual ->
      assertThat(actual).isFalse()
    }
  }

  @Test
  fun `SHOULD return true ON isStashFixGiven WHEN fix given`() {
    val userId = database.prepareUser()

    transaction(database) {
      UserStashFixGivenTable.insert {
        it[UserStashFixGivenTable.userId] = userId
        it[fixAmount] = BigDecimal.ONE
      }
    }

    runBlocking { underTest.isStashFixGiven(userId) }.let { actual ->
      assertThat(actual).isTrue()
    }
  }

  @Test
  fun `SHOULD track fix ON trackStashFixGiven`() {
    val userId = database.prepareUser()

    runBlocking { underTest.trackStashFixGiven(userId, BigDecimal("3.14")) }

    transaction(database) {
      UserStashFixGivenTable
        .select { UserStashFixGivenTable.userId eq userId }
        .first()
        .let { row ->
          assertThat(row[UserStashFixGivenTable.fixAmount]).isEqualByComparingTo(BigDecimal("3.14"))
        }
    }
  }

  @Test
  fun `SHOULD track fix ON trackStashFixGiven WHEN already tracked`() {
    val userId = database.prepareUser()

    runBlocking {
      underTest.trackStashFixGiven(userId, BigDecimal("3.14"))
      underTest.trackStashFixGiven(userId, BigDecimal("2.71"))
    }

    transaction(database) {
      UserStashFixGivenTable
        .select { UserStashFixGivenTable.userId eq userId }
        .first()
        .let { row ->
          assertThat(row[UserStashFixGivenTable.fixAmount]).isEqualByComparingTo(BigDecimal("3.14"))
        }
    }
  }

}