package com.moregames.playtime.earnings

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.justplayapps.base.Common
import com.justplayapps.playtime.rewarding.proto.copy
import com.justplayapps.playtime.rewarding.proto.getUserDataResponse
import com.justplayapps.service.rewarding.earnings.*
import com.justplayapps.service.rewarding.earnings.dto.Earnings
import com.justplayapps.service.rewarding.earnings.dto.EarningsCalculationResult
import com.justplayapps.service.rewarding.facade.PlaytimeFacade
import com.moregames.base.bus.MessageBus
import com.moregames.base.dto.AppPlatform
import com.moregames.base.messaging.dto.RevenueReceivedEventDto
import com.moregames.base.util.mock
import com.moregames.base.util.toProto
import com.moregames.playtime.earnings.dto.GenericRevenueDto
import com.moregames.playtime.earnings.dto.UserEarningsQuotasDto
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Duration
import java.time.Instant
import java.util.*

class UserEarningsEm2ServiceTest {

  private val userEarningsPersistenceService: UserEarningsPersistenceService = mock()
  private val earningsCalculationsService: EarningsCalculationsService = mock()
  private val userEarningsService: UserEarningsService = mock()
  private val emExperimentBaseService: EmExperimentBaseService = mock()
  private val playtimeFacade: PlaytimeFacade = mock()
  private val messageBus: MessageBus = mock()
  private val stashCalculationService: StashCalculationService = mock()
  private val stashFixService: StashFixService = mock()
  private val userCurrentCoinsBalanceService: UserCurrentCoinsBalanceService = mock()

  private val service = UserEarningsEm2Service(
    userEarningsPersistenceService = userEarningsPersistenceService,
    earningsCalculationsService = earningsCalculationsService,
    userEarningsService = userEarningsService,
    emExperimentBaseService = emExperimentBaseService,
    playtimeFacade = playtimeFacade,
    messageBus = messageBus,
    stashCalculationService = stashCalculationService,
    stashFixService = stashFixService,
    userCurrentCoinsBalanceService = userCurrentCoinsBalanceService,
  )

  private val userId = "userId"
  private val metaId = 123
  private val cashOutPeriodStart = Instant.now().minus(Duration.ofHours(3))
  private val revenueGame = GenericRevenueDto(
    id = "eventId",
    userId = "userId",
    timestamp = Instant.now(),
    source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
    amount = BigDecimal("2.00"),
    amountExtra = null,
    gameId = null,
  )
  private val revenueOffer = GenericRevenueDto(
    id = "eventId",
    userId = "userId",
    timestamp = Instant.now(),
    source = RevenueReceivedEventDto.RevenueSource.IRON_SOURCE,
    amount = BigDecimal("2.00"),
    amountExtra = null,
    gameId = null,
  )
  private val revenueVideoAndroid = GenericRevenueDto(
    id = "eventId",
    userId = "userId",
    timestamp = Instant.now(),
    source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
    amount = BigDecimal("0.11"),
    amountExtra = null,
    gameId = 1000001,
  )
  private val revenueVideoIos = GenericRevenueDto(
    id = "eventId",
    userId = "userId",
    timestamp = Instant.now(),
    source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
    amount = BigDecimal("0.17"),
    amountExtra = null,
    gameId = 1000002,
  )
  private val earnings = Earnings(
    userId = userId,
    earningsSum = BigDecimal("3.10"),
    quotasDto = UserEarningsQuotasDto(
      userId = userId,
      quotas = listOf("quotas"),
      periodEnd = Instant.now(),
      values = listOf(BigDecimal.ONE)
    )
  )
  private val userCurrentCoinsBalance = UserCurrentCoinsBalance(
    gameCoins = BigDecimal("101.0"),
    offerCoins = BigDecimal("102.0"),
    bonusCoins = BigDecimal("303.0")
  )
  private val earningsConversionResultDto = EarningsCalculationResult.Simple(
    metaId = metaId,
    amount = BigDecimal("3.10"),
    amountNoRounding = BigDecimal("3.514")
  )
  private val calculationExpected = EarningsCalculationResult.Em2(
    simpleCalculationResult = earningsConversionResultDto,
    noEarnings = false,
    realRevenue = BigDecimal("4.28"),
    realGameRevenue = BigDecimal("2.00"),
    em2CoinsBalance = userCurrentCoinsBalance,
    coinsForOneDollar = BigDecimal("100.0"),
    em2Revenue = BigDecimal("5.060000"),
    em2GameRevenue = BigDecimal("1.010000"),
    earnings = earnings,
  )

  init {
    userEarningsService.mock({ getFirstCpTopUp(any(), any(), any()) }, BigDecimal.ZERO)
    emExperimentBaseService.mock({ getWelcomeBonusAmount(userId, AppPlatform.ANDROID) }, 10)
    emExperimentBaseService.mock({ getWelcomeBonusAmount(userId, AppPlatform.IOS) }, 5)
    stashFixService.mock({ calculateExtraEarnings(eq(userId), anyOrNull(), any()) }, null)
  }

  private val userData = getUserDataResponse {
    offerWallCoinsToUsdConversionRatio = BigDecimal("100.0").toProto()
    isWelcomeCoinsOfferCompleted = false
    userCurrency = "CAD"
    platform = Common.AppPlatformProto.ANDROID
    isHighlyTrustedUser = true
  }

  @Test
  fun `SHOULD convert revenue to earnings ON convertRevenueToEarnings`() {
    prepareBasicTest()

    runBlocking { service.convertRevenueToEarnings(userId, userData, cashOutPeriodStart) }.let { actual ->
      assertThat(actual).isEqualTo(calculationExpected)
    }
  }

  @Test
  fun `SHOULD return zero earnings ON convertRevenueToEarnings WHEN no coins`() {
    val coinsBalance = UserCurrentCoinsBalance(
      gameCoins = BigDecimal.ZERO,
      offerCoins = BigDecimal.ZERO,
      bonusCoins = BigDecimal.ZERO,
    )
    userEarningsPersistenceService.mock({ getUnconvertedRevenue(userId) }, emptyList())
    userCurrentCoinsBalanceService.mock(
      { loadCurrentCoinsBalanceEm2(userId) }, UserCurrentCoinsBalance(
        gameCoins = BigDecimal.ZERO,
        offerCoins = BigDecimal.ZERO,
        bonusCoins = BigDecimal.ZERO,
      )
    )

    val expected = EarningsCalculationResult.Em2(
      simpleCalculationResult = EarningsCalculationResult.Simple(null, BigDecimal.ZERO, BigDecimal.ZERO),
      noEarnings = true,
      realRevenue = BigDecimal.ZERO,
      realGameRevenue = BigDecimal.ZERO,
      em2CoinsBalance = coinsBalance,
      coinsForOneDollar = BigDecimal("100.0"),
      em2Revenue = BigDecimal.ZERO,
      em2GameRevenue = BigDecimal.ZERO,
      earnings = Earnings(userId, BigDecimal.ZERO),
    )

    runBlocking { service.convertRevenueToEarnings(userId, userData, cashOutPeriodStart) }.let { actual ->
      assertThat(actual).isEqualTo(expected)
    }

    verifyBlocking(userEarningsPersistenceService, never()) { bindRevenueWithEarnings(any(), any(), any(), anyOrNull(), any(), any(), anyOrNull()) }
  }

  @Test
  fun `SHOULD return zero earnings ON convertRevenueToEarnings WHEN no coins AND some real revenue`() {
    val revenue = GenericRevenueDto(
      id = "eventId",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("2.00"),
      amountExtra = null,
      gameId = null,
    )
    val coinsBalance = UserCurrentCoinsBalance(
      gameCoins = BigDecimal("0.00"),
      offerCoins = BigDecimal("0.000"),
      bonusCoins = BigDecimal("0.0000"),
    )
    userEarningsPersistenceService.mock({ getUnconvertedRevenue(userId) }, listOf(revenue))

    val userDataResponse = userData.copy {
      userCurrency = "CAD"
    }

    userCurrentCoinsBalanceService.mock(
      { loadCurrentCoinsBalanceEm2(userId) }, UserCurrentCoinsBalance(
        gameCoins = BigDecimal("0.00"),
        offerCoins = BigDecimal("0.000"),
        bonusCoins = BigDecimal("0.0000"),
      )
    )

    val expected = EarningsCalculationResult.Em2(
      simpleCalculationResult = EarningsCalculationResult.Simple(null, BigDecimal.ZERO, BigDecimal.ZERO),
      noEarnings = true,
      realRevenue = BigDecimal("2.00"),
      realGameRevenue = BigDecimal("2.00"),
      em2CoinsBalance = coinsBalance,
      coinsForOneDollar = BigDecimal("100.0"),
      em2Revenue = BigDecimal.ZERO,
      em2GameRevenue = BigDecimal.ZERO,
      earnings = Earnings(userId, BigDecimal.ZERO),
    )

    runBlocking { service.convertRevenueToEarnings(userId, userDataResponse, cashOutPeriodStart) }.let { actual ->
      assertThat(actual).isEqualTo(expected)
    }

    verifyBlocking(userEarningsPersistenceService) {
      bindRevenueWithEarnings(
        userId = userId,
        revenueEventIds = listOf("eventId"),
        calculatedUserEarningUsd = BigDecimal.ZERO,
        nonBoostedUserEarningUsd = null,
        userCurrency = Currency.getInstance("CAD"),
        userCurrencyEarningAmount = BigDecimal.ZERO,
        nonBoostedUserCurrencyEarningAmount = null,
      )
    }
  }

  @Test
  fun `SHOULD handle decimal division without exceptions ON convertRevenueToEarnings`() {
    val earnings = Earnings(
      userId = userId,
      earningsSum = BigDecimal("3.10"),
      quotasDto = UserEarningsQuotasDto(
        userId = userId,
        quotas = listOf("quotas"),
        periodEnd = Instant.now(),
        values = listOf(BigDecimal.ONE)
      )
    )
    val earningsConversionResultDto = EarningsCalculationResult.Simple(
      metaId,
      amount = BigDecimal("3.10"),
      amountNoRounding = BigDecimal("3.514")
    )
    userEarningsPersistenceService.mock({ getUnconvertedRevenue(userId) }, emptyList())
    val userDataResponse = userData.copy {
      offerWallCoinsToUsdConversionRatio = BigDecimal("220.0").toProto()
    }

    userCurrentCoinsBalanceService.mock(
      { loadCurrentCoinsBalanceEm2(userId) }, UserCurrentCoinsBalance(
        gameCoins = BigDecimal("0"),
        offerCoins = BigDecimal("0"),
        bonusCoins = BigDecimal("50"), // 50/220 = 0.2(27)
      )
    )

    earningsCalculationsService.mock(
      { convertRevenueToEarnings(userId, BigDecimal("0.227272"), BigDecimal("0.000000"), userDataResponse) },
      earnings
    )
    userEarningsService.mock(
      {
        giveEarningsToUser(
          userId = userId,
          revenueList = emptyList(),
          earningsFromRevenue = earnings,
          earningsWithAdditionsAmountUsd = BigDecimal("3.10"),
          nonBoostedEarningsWithAdditionsAmountUsd = null,
          Currency.getInstance("CAD")
        )
      },
      earningsConversionResultDto
    )
    playtimeFacade.mock(
      {
        calculateCappedGameRevenue(
          userId,
          gameCoins = BigDecimal.ZERO,
          coinsForOneDollar = BigDecimal("220.0"),
          gamesRealRevenues = emptyList(),
        )
      },
      BigDecimal.ZERO
    )

    runBlocking { service.convertRevenueToEarnings(userId, userDataResponse, cashOutPeriodStart) }
  }

  @Test
  fun `SHOULD top up ten cents user earnings if top up amount non-zero`() {
    val revenueGame = GenericRevenueDto(
      id = "eventId",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("2.00"),
      amountExtra = null,
      gameId = null,
    )
    val revenueOffer = GenericRevenueDto(
      id = "eventId",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.IRON_SOURCE,
      amount = BigDecimal("2.00"),
      amountExtra = null,
      gameId = null,
    )
    val earnings = Earnings(
      userId = userId,
      earningsSum = BigDecimal("3.10"),
      quotasDto = UserEarningsQuotasDto(
        userId = userId,
        quotas = listOf("quotas"),
        periodEnd = Instant.now(),
        values = listOf(BigDecimal.ONE)
      )
    )
    val userCurrentCoinsBalance = UserCurrentCoinsBalance(
      gameCoins = BigDecimal("101.0"),
      offerCoins = BigDecimal("102.0"),
      bonusCoins = BigDecimal("303.0")
    )
    val earningsConversionResultDto = EarningsCalculationResult.Simple(
      metaId = metaId,
      amount = BigDecimal("3.20"),
      amountNoRounding = BigDecimal("3.514")
    )
    userEarningsPersistenceService.mock({ getUnconvertedRevenue(userId) }, listOf(revenueGame, revenueOffer))
    userCurrentCoinsBalanceService.mock(
      { loadCurrentCoinsBalanceEm2(userId) }, UserCurrentCoinsBalance(
        gameCoins = BigDecimal("101.0"),
        offerCoins = BigDecimal("102.0"),
        bonusCoins = BigDecimal("303.0"),
      )
    )
    earningsCalculationsService.mock(
      { convertRevenueToEarnings(userId, BigDecimal("5.060000"), BigDecimal("1.020000"), userData) },
      earnings
    )
    userEarningsService.mock(
      {
        giveEarningsToUser(
          userId = userId,
          revenueList = listOf(revenueGame, revenueOffer),
          earningsFromRevenue = earnings,
          earningsWithAdditionsAmountUsd = BigDecimal("3.20"),
          nonBoostedEarningsWithAdditionsAmountUsd = null,
          userCurrency = Currency.getInstance("CAD"),
        )
      },
      earningsConversionResultDto
    )
    userEarningsService.mock({ getFirstCpTopUp(userId, BigDecimal("3.10"), userData) }, BigDecimal("0.1"))
    playtimeFacade.mock(
      {
        calculateCappedGameRevenue(
          userId,
          gameCoins = BigDecimal("101.0"),
          coinsForOneDollar = BigDecimal("100.0"),
          gamesRealRevenues = listOf(revenueGame),
        )
      },
      BigDecimal("1.010000")
    )

    val expected = EarningsCalculationResult.Em2(
      simpleCalculationResult = earningsConversionResultDto,
      noEarnings = false,
      realRevenue = BigDecimal("4.00"),
      realGameRevenue = BigDecimal("2.00"),
      em2CoinsBalance = userCurrentCoinsBalance,
      coinsForOneDollar = BigDecimal("100.0"),
      em2Revenue = BigDecimal("5.060000"),
      em2GameRevenue = BigDecimal("1.010000"),
      earnings = earnings,
    )

    runBlocking { service.convertRevenueToEarnings(userId, userData, cashOutPeriodStart) }.let { actual ->
      assertThat(actual).isEqualTo(expected)
    }
  }

  @Test
  fun `SHOULD limit offer coins by non-games revenue ON convertRevenueToEarnings`() {
    val revenueGame = GenericRevenueDto(
      id = "eventId",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("2.00"),
      amountExtra = null,
      gameId = null,
    )
    val revenueOffer = GenericRevenueDto(
      id = "eventId",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.IRON_SOURCE,
      amount = BigDecimal("2.00"),
      amountExtra = null,
      gameId = null,
    )
    val earnings = Earnings(
      userId = userId,
      earningsSum = BigDecimal("3.10"),
      quotasDto = UserEarningsQuotasDto(
        userId = userId,
        quotas = listOf("quotas"),
        periodEnd = Instant.now(),
        values = listOf(BigDecimal.ONE)
      )
    )
    val userCurrentCoinsBalance = UserCurrentCoinsBalance(
      gameCoins = BigDecimal("101.0"),
      offerCoins = BigDecimal("402.0"),
      bonusCoins = BigDecimal("303.0")
    )
    val earningsConversionResultDto = EarningsCalculationResult.Simple(
      metaId = metaId,
      amount = BigDecimal("3.10"),
      amountNoRounding = BigDecimal("3.514")
    )
    userEarningsPersistenceService.mock({ getUnconvertedRevenue(userId) }, listOf(revenueGame, revenueOffer))

    userCurrentCoinsBalanceService.mock(
      { loadCurrentCoinsBalanceEm2(userId) }, UserCurrentCoinsBalance(
        gameCoins = BigDecimal("101.0"),
        offerCoins = BigDecimal("402.0"),
        bonusCoins = BigDecimal("303.0"),
      )
    )
    earningsCalculationsService.mock(
      { convertRevenueToEarnings(userId, BigDecimal("6.040000"), BigDecimal("2.000000"), userData) },
      earnings
    )
    userEarningsService.mock(
      {
        giveEarningsToUser(
          userId = userId,
          revenueList = listOf(revenueGame, revenueOffer),
          earningsFromRevenue = earnings,
          earningsWithAdditionsAmountUsd = BigDecimal("3.10"),
          nonBoostedEarningsWithAdditionsAmountUsd = null,
          userCurrency = Currency.getInstance("CAD"),
        )
      },
      earningsConversionResultDto
    )
    playtimeFacade.mock(
      {
        calculateCappedGameRevenue(
          userId,
          gameCoins = BigDecimal("101.0"),
          coinsForOneDollar = BigDecimal("100.0"),
          gamesRealRevenues = listOf(revenueGame),
        )
      },
      BigDecimal("1.010000")
    )

    val expected = EarningsCalculationResult.Em2(
      simpleCalculationResult = earningsConversionResultDto,
      noEarnings = false,
      realRevenue = BigDecimal("4.00"),
      realGameRevenue = BigDecimal("2.00"),
      em2CoinsBalance = userCurrentCoinsBalance,
      coinsForOneDollar = BigDecimal("100.0"),
      em2Revenue = BigDecimal("6.040000"),
      em2GameRevenue = BigDecimal("1.010000"),
      earnings = earnings,
    )

    runBlocking { service.convertRevenueToEarnings(userId, userData, cashOutPeriodStart) }.let { actual ->
      assertThat(actual).isEqualTo(expected)
    }
  }

  @Test
  fun `SHOULD limit offer coins by non-games revenue taking amount extra into account ON convertRevenueToEarnings`() {
    val revenueGame = GenericRevenueDto(
      id = "eventId",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("2.00"),
      amountExtra = null,
      gameId = null,
    )
    val revenueOffer = GenericRevenueDto(
      id = "eventId",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.IRON_SOURCE,
      amount = BigDecimal("2.00"),
      amountExtra = BigDecimal("0.5"),
      gameId = null,
    )
    val earnings = Earnings(
      userId = userId,
      earningsSum = BigDecimal("3.10"),
      quotasDto = UserEarningsQuotasDto(
        userId = userId,
        quotas = listOf("quotas"),
        periodEnd = Instant.now(),
        values = listOf(BigDecimal.ONE)
      )
    )
    val userCurrentCoinsBalance = UserCurrentCoinsBalance(
      gameCoins = BigDecimal("101.0"),
      offerCoins = BigDecimal("402.0"),
      bonusCoins = BigDecimal("303.0")
    )
    val earningsConversionResultDto = EarningsCalculationResult.Simple(
      metaId = metaId,
      amount = BigDecimal("3.10"),
      amountNoRounding = BigDecimal("3.514")
    )
    userEarningsPersistenceService.mock({ getUnconvertedRevenue(userId) }, listOf(revenueGame, revenueOffer))
    userCurrentCoinsBalanceService.mock(
      { loadCurrentCoinsBalanceEm2(userId) }, UserCurrentCoinsBalance(
        gameCoins = BigDecimal("101.0"),
        offerCoins = BigDecimal("402.0"),
        bonusCoins = BigDecimal("303.0"),
      )
    )
    earningsCalculationsService.mock(
      { convertRevenueToEarnings(userId, BigDecimal("6.540000"), BigDecimal("2.500000"), userData) },
      earnings
    )
    userEarningsService.mock(
      {
        giveEarningsToUser(
          userId = userId,
          revenueList = listOf(revenueGame, revenueOffer),
          earningsFromRevenue = earnings,
          earningsWithAdditionsAmountUsd = BigDecimal("3.10"),
          nonBoostedEarningsWithAdditionsAmountUsd = null,
          userCurrency = Currency.getInstance("CAD"),
        )
      },
      earningsConversionResultDto
    )
    playtimeFacade.mock(
      {
        calculateCappedGameRevenue(
          userId,
          gameCoins = BigDecimal("101.0"),
          coinsForOneDollar = BigDecimal("100.0"),
          gamesRealRevenues = listOf(revenueGame),
        )
      },
      BigDecimal("1.010000")
    )

    val expected = EarningsCalculationResult.Em2(
      simpleCalculationResult = earningsConversionResultDto,
      noEarnings = false,
      realRevenue = BigDecimal("4.00"),
      realGameRevenue = BigDecimal("2.00"),
      em2CoinsBalance = userCurrentCoinsBalance,
      coinsForOneDollar = BigDecimal("100.0"),
      em2Revenue = BigDecimal("6.540000"),
      em2GameRevenue = BigDecimal("1.010000"),
      earnings = earnings,
    )

    runBlocking { service.convertRevenueToEarnings(userId, userData, cashOutPeriodStart) }.let { actual ->
      assertThat(actual).isEqualTo(expected)
    }
  }

  @Test
  fun `SHOULD not limit offer coins by non-games revenue ON convertRevenueToEarnings WHEN real revenue is just a little bit not enough`() {
    val revenueGame = GenericRevenueDto(
      id = "eventId",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("2.00"),
      amountExtra = null,
      gameId = null,
    )
    val revenueOffer = GenericRevenueDto(
      id = "eventId",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.IRON_SOURCE,
      amount = BigDecimal("2.00"),
      amountExtra = null,
      gameId = null,
    )
    val earnings = Earnings(
      userId = userId,
      earningsSum = BigDecimal("3.10"),
      quotasDto = UserEarningsQuotasDto(
        userId = userId,
        quotas = listOf("quotas"),
        periodEnd = Instant.now(),
        values = listOf(BigDecimal.ONE)
      )
    )
    val userCurrentCoinsBalance = UserCurrentCoinsBalance(
      gameCoins = BigDecimal("101.0"),
      offerCoins = BigDecimal("200.9"),
      bonusCoins = BigDecimal("303.0")
    )
    val earningsConversionResultDto = EarningsCalculationResult.Simple(
      metaId = metaId,
      amount = BigDecimal("3.10"),
      amountNoRounding = BigDecimal("3.514")
    )
    userEarningsPersistenceService.mock({ getUnconvertedRevenue(userId) }, listOf(revenueGame, revenueOffer))
    userCurrentCoinsBalanceService.mock(
      { loadCurrentCoinsBalanceEm2(userId) }, UserCurrentCoinsBalance(
        gameCoins = BigDecimal("101.0"),
        offerCoins = BigDecimal("200.9"),
        bonusCoins = BigDecimal("303.0"),
      )
    )
    earningsCalculationsService.mock(
      { convertRevenueToEarnings(userId, BigDecimal("6.049000"), BigDecimal("2.009000"), userData) },
      earnings
    )
    userEarningsService.mock(
      {
        giveEarningsToUser(
          userId = userId,
          revenueList = listOf(revenueGame, revenueOffer),
          earningsFromRevenue = earnings,
          earningsWithAdditionsAmountUsd = BigDecimal("3.10"),
          nonBoostedEarningsWithAdditionsAmountUsd = null,
          userCurrency = Currency.getInstance("CAD"),
        )
      },
      earningsConversionResultDto
    )
    playtimeFacade.mock(
      {
        calculateCappedGameRevenue(
          userId,
          gameCoins = BigDecimal("101.0"),
          coinsForOneDollar = BigDecimal("100.0"),
          gamesRealRevenues = listOf(revenueGame),
        )
      },
      BigDecimal("1.010000")
    )

    val expected = EarningsCalculationResult.Em2(
      simpleCalculationResult = earningsConversionResultDto,
      noEarnings = false,
      realRevenue = BigDecimal("4.00"),
      realGameRevenue = BigDecimal("2.00"),
      em2CoinsBalance = userCurrentCoinsBalance,
      coinsForOneDollar = BigDecimal("100.0"),
      em2Revenue = BigDecimal("6.049000"),
      em2GameRevenue = BigDecimal("1.010000"),
      earnings = earnings,
    )

    runBlocking { service.convertRevenueToEarnings(userId, userData, cashOutPeriodStart) }.let { actual ->
      assertThat(actual).isEqualTo(expected)
    }
  }

  @Test
  fun `SHOULD limit offer coins with extra welcome offer amount by non-games revenue ON convertRevenueToEarnings WHEN welcome coins offer finished in this CP`() {
    val revenueGame = GenericRevenueDto(
      id = "eventId",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("2.00"),
      amountExtra = null,
      gameId = null,
    )
    val revenueOffer = GenericRevenueDto(
      id = "eventId",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.IRON_SOURCE,
      amount = BigDecimal("2.00"),
      amountExtra = null,
      gameId = null,
    )
    val earnings = Earnings(
      userId = userId,
      earningsSum = BigDecimal("3.10"),
      quotasDto = UserEarningsQuotasDto(
        userId = userId,
        quotas = listOf("quotas"),
        periodEnd = Instant.now(),
        values = listOf(BigDecimal.ONE)
      )
    )
    val userCurrentCoinsBalance = UserCurrentCoinsBalance(
      gameCoins = BigDecimal("101.0"),
      offerCoins = BigDecimal("402.0"),
      bonusCoins = BigDecimal("303.0")
    )
    val earningsConversionResultDto = EarningsCalculationResult.Simple(
      metaId = metaId,
      amount = BigDecimal("3.10"),
      amountNoRounding = BigDecimal("3.514")
    )
    userEarningsPersistenceService.mock({ getUnconvertedRevenue(userId) }, listOf(revenueGame, revenueOffer))
    val userDataResponse = userData.copy {
      isWelcomeCoinsOfferCompleted = true
    }
    userCurrentCoinsBalanceService.mock(
      { loadCurrentCoinsBalanceEm2(userId) }, UserCurrentCoinsBalance(
        gameCoins = BigDecimal("101.0"),
        offerCoins = BigDecimal("402.0"),
        bonusCoins = BigDecimal("303.0"),
      )
    )
    earningsCalculationsService.mock(
      { convertRevenueToEarnings(userId, BigDecimal("6.140000"), BigDecimal("2.100000"), userDataResponse) },
      earnings
    )
    userEarningsService.mock(
      {
        giveEarningsToUser(
          userId = userId,
          revenueList = listOf(revenueGame, revenueOffer),
          earningsFromRevenue = earnings,
          earningsWithAdditionsAmountUsd = BigDecimal("3.10"),
          nonBoostedEarningsWithAdditionsAmountUsd = null,
          userCurrency = Currency.getInstance("CAD"),
        )
      },
      earningsConversionResultDto
    )
    emExperimentBaseService.mock({ getWelcomeBonusAmount(userId, AppPlatform.ANDROID) }, 10)
    playtimeFacade.mock(
      {
        calculateCappedGameRevenue(
          userId,
          gameCoins = BigDecimal("101.0"),
          coinsForOneDollar = BigDecimal("100.0"),
          gamesRealRevenues = listOf(revenueGame),
        )
      },
      BigDecimal("1.010000")
    )

    val expected = EarningsCalculationResult.Em2(
      simpleCalculationResult = earningsConversionResultDto,
      noEarnings = false,
      realRevenue = BigDecimal("4.00"),
      realGameRevenue = BigDecimal("2.00"),
      em2CoinsBalance = userCurrentCoinsBalance,
      coinsForOneDollar = BigDecimal("100.0"),
      em2Revenue = BigDecimal("6.140000"),
      em2GameRevenue = BigDecimal("1.010000"),
      earnings = earnings,
    )

    runBlocking { service.convertRevenueToEarnings(userId, userDataResponse, cashOutPeriodStart) }.let { actual ->
      assertThat(actual).isEqualTo(expected)
    }
  }

  @Test
  fun `SHOULD NOT call coins stashing effect ON convertRevenueToEarnings WHEN nothing to stash`() {
    prepareBasicTest()

    runBlocking { service.convertRevenueToEarnings(userId, userData, cashOutPeriodStart) }.let { actual ->
      assertThat(actual).isEqualTo(calculationExpected)
    }

    verifyBlocking(messageBus, never()) { publishAsync(any<StashCoinsEffectHandler.StashCoinsEffect>()) }
  }

  @Test
  fun `SHOULD call coins stashing effect ON convertRevenueToEarnings`() {
    prepareBasicTest()

    stashCalculationService.mock(
      { calculateUsdForStash(earnings, BigDecimal("4.28"), BigDecimal("2.00"), BigDecimal("1.010000"), true) },
      BigDecimal("3.14")
    )

    runBlocking { service.convertRevenueToEarnings(userId, userData, cashOutPeriodStart) }.let { actual ->
      assertThat(actual).isEqualTo(calculationExpected)
    }

    verifyBlocking(messageBus) {
      publishAsync(
        StashCoinsEffectHandler.StashCoinsEffect(userId, BigDecimal("3.14"), BigDecimal("100.0"))
      )
    }
  }

  @Test
  fun `SHOULD calculate stash fix and give extra earnings ON convertRevenueToEarnings`() {
    prepareBasicTest()

    val allRevenue = listOf(revenueGame, revenueOffer, revenueVideoAndroid, revenueVideoIos)

    stashCalculationService.mock(
      { calculateUsdForStash(earnings, BigDecimal("4.28"), BigDecimal("2.00"), BigDecimal("1.010000"), true) },
      BigDecimal("3.14")
    )
    stashFixService.mock({ calculateExtraEarnings(userId, BigDecimal("3.14"), BigDecimal("4.28")) }, BigDecimal("3.14"))
    userEarningsService.mock(
      {
        giveEarningsToUser(
          userId = userId,
          revenueList = allRevenue,
          earningsFromRevenue = earnings,
          earningsWithAdditionsAmountUsd = BigDecimal("6.24"),
          nonBoostedEarningsWithAdditionsAmountUsd = null,
          userCurrency = Currency.getInstance("CAD"),
        )
      },
      earningsConversionResultDto
    )

    runBlocking { service.convertRevenueToEarnings(userId, userData, cashOutPeriodStart) }.let { actual ->
      assertThat(actual).isEqualTo(calculationExpected)
    }

    verifyBlocking(messageBus) {
      publishAsync(
        TrackStashFixEffectHandler.TrackStashFixEffect(userId, BigDecimal("3.14"))
      )
    }
  }

  @Test
  fun `SHOULD calculated boosted earnings ON convertRevenueToEarnings WHEN boost defined`() {
    prepareBasicTest()

    val allRevenue = listOf(revenueGame, revenueOffer, revenueVideoAndroid, revenueVideoIos)

    userEarningsService.mock(
      { boostEarnings(userId, earnings.earningsSum, cashOutPeriodStart) },
      UserEarningsService.BoostedEarnings(
        earningsBefore = BigDecimal("3.87"),
        earningsAfter = BigDecimal("4.25"),
      )
    )

    userEarningsService.mock(
      {
        giveEarningsToUser(
          userId = userId,
          revenueList = allRevenue,
          earningsFromRevenue = earnings,
          earningsWithAdditionsAmountUsd = BigDecimal("4.25"),
          nonBoostedEarningsWithAdditionsAmountUsd = BigDecimal("3.87"),
          userCurrency = Currency.getInstance("CAD"),
        )
      },
      earningsConversionResultDto
    )

    runBlocking { service.convertRevenueToEarnings(userId, userData, cashOutPeriodStart) }.let { actual ->
      assertThat(actual).isEqualTo(calculationExpected)
    }
  }


  @Test
  fun `SHOULD calculated boosted earnings on revenue earnings only ON convertRevenueToEarnings WHEN boost defined AND stash earnings AND topup earnings`() {
    prepareBasicTest()

    val allRevenue = listOf(revenueGame, revenueOffer, revenueVideoAndroid, revenueVideoIos)

    userEarningsService.mock(
      { boostEarnings(userId, earnings.earningsSum, cashOutPeriodStart) },
      UserEarningsService.BoostedEarnings(
        earningsBefore = BigDecimal("3.87"),
        earningsAfter = BigDecimal("4.25"),
      )
    )
    stashCalculationService.mock(
      { calculateUsdForStash(earnings, BigDecimal("4.28"), BigDecimal("2.00"), BigDecimal("1.010000"), true) },
      BigDecimal("3.14")
    )
    stashFixService.mock({ calculateExtraEarnings(userId, BigDecimal("3.14"), BigDecimal("4.28")) }, BigDecimal("3.14"))
    userEarningsService.mock({ getFirstCpTopUp(userId, BigDecimal("3.10"), userData) }, BigDecimal("0.1"))

    userEarningsService.mock(
      {
        giveEarningsToUser(
          userId = userId,
          revenueList = allRevenue,
          earningsFromRevenue = earnings,
          earningsWithAdditionsAmountUsd = BigDecimal("7.49"),
          nonBoostedEarningsWithAdditionsAmountUsd = BigDecimal("7.11"),
          userCurrency = Currency.getInstance("CAD"),
        )
      },
      earningsConversionResultDto
    )

    runBlocking { service.convertRevenueToEarnings(userId, userData, cashOutPeriodStart) }.let { actual ->
      assertThat(actual).isEqualTo(calculationExpected)
    }
  }


  @Test
  fun `SHOULD return zero earnings ON processAndReturnZeroEarnings`() {
    val revenue = GenericRevenueDto(
      id = "eventId",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("2.00"),
      amountExtra = null,
      gameId = null,
    )
    val coinsBalance = UserCurrentCoinsBalance(
      gameCoins = BigDecimal("0.00"),
      offerCoins = BigDecimal("0.000"),
      bonusCoins = BigDecimal("0.0000"),
    )

    val expected = EarningsCalculationResult.Em2(
      simpleCalculationResult = EarningsCalculationResult.Simple(null, BigDecimal.ZERO, BigDecimal.ZERO),
      noEarnings = true,
      realRevenue = BigDecimal("2.00"),
      realGameRevenue = BigDecimal("3.00"),
      em2CoinsBalance = coinsBalance,
      coinsForOneDollar = BigDecimal("100.0"),
      em2Revenue = BigDecimal.ZERO,
      em2GameRevenue = BigDecimal.ZERO,
      earnings = Earnings(userId, BigDecimal.ZERO),
    )

    runBlocking {
      service.processAndReturnZeroEarnings(
        userId,
        revenueWithExtraList = listOf(revenue),
        totalRealRevenue = BigDecimal("2.00"),
        gamesRealRevenueSum = BigDecimal("3.00"),
        coinsCurrentBalance = coinsBalance,
        coinsForOneDollar = BigDecimal("100.0"),
        userCurrency = Currency.getInstance("CAD"),
      )
    }.let { actual ->
      assertThat(actual).isEqualTo(expected)
    }

    verifyBlocking(userEarningsPersistenceService) {
      bindRevenueWithEarnings(
        userId = userId,
        revenueEventIds = listOf("eventId"),
        calculatedUserEarningUsd = BigDecimal.ZERO,
        nonBoostedUserEarningUsd = null,
        userCurrency = Currency.getInstance("CAD"),
        userCurrencyEarningAmount = BigDecimal.ZERO,
        nonBoostedUserCurrencyEarningAmount = null,
      )
    }
  }

  private fun prepareBasicTest() {
    val allRevenue = listOf(revenueGame, revenueOffer, revenueVideoAndroid, revenueVideoIos)

    userEarningsPersistenceService.mock({ getUnconvertedRevenue(userId) }, allRevenue)
    userCurrentCoinsBalanceService.mock(
      { loadCurrentCoinsBalanceEm2(userId) }, UserCurrentCoinsBalance(
        gameCoins = BigDecimal("101.0"),
        offerCoins = BigDecimal("102.0"),
        bonusCoins = BigDecimal("303.0"),
      )
    )
    earningsCalculationsService.mock(
      { convertRevenueToEarnings(userId, BigDecimal("5.060000"), BigDecimal("1.020000"), userData) },
      earnings
    )
    userEarningsService.mock(
      {
        giveEarningsToUser(
          userId = userId,
          revenueList = allRevenue,
          earningsFromRevenue = earnings,
          earningsWithAdditionsAmountUsd = BigDecimal("3.10"),
          nonBoostedEarningsWithAdditionsAmountUsd = null,
          userCurrency = Currency.getInstance("CAD"),
        )
      },
      earningsConversionResultDto
    )
    playtimeFacade.mock(
      {
        calculateCappedGameRevenue(
          userId,
          gameCoins = BigDecimal("101.0"),
          coinsForOneDollar = BigDecimal("100.0"),
          gamesRealRevenues = listOf(revenueGame),
        )
      },
      BigDecimal("1.010000")
    )

  }
}