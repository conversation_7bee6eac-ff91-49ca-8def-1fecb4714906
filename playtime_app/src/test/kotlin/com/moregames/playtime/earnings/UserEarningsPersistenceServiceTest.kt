package com.moregames.playtime.earnings

import assertk.assertThat
import assertk.assertions.*
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService.Companion.CURRENT_GENERIC_REVENUE_HOURS
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService.Companion.CURRENT_GENERIC_UNCONVERTED_REVENUE_HOURS
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService.UserCoinsBasedEarningsData
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService.UserEarningsWithOfferwallAmount
import com.justplayapps.service.rewarding.earnings.dto.UserCurrencyEarnings
import com.justplayapps.service.rewarding.earnings.table.*
import com.justplayapps.service.rewarding.earnings.table.UserEarningsTable.calculatedUserEarningUsd
import com.justplayapps.service.rewarding.earnings.table.UserEarningsTable.nonBoostedCurrencyEarningAmount
import com.justplayapps.service.rewarding.earnings.table.UserEarningsTable.nonBoostedUserEarningUsd
import com.justplayapps.service.rewarding.earnings.table.UserEarningsTable.userCurrencyCode
import com.justplayapps.service.rewarding.earnings.table.UserEarningsTable.userCurrencyEarningAmount
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.VARIATION_KEY_DEFAULT
import com.moregames.base.abtesting.Variations
import com.moregames.base.gameStub
import com.moregames.base.messaging.dto.RevenueReceivedEventDto.RevenueSource
import com.moregames.base.table.*
import com.moregames.base.table.CurrentGenericRevenueTable.metaUserEarningsId
import com.moregames.base.table.CurrentGenericRevenueTable.networkId
import com.moregames.base.table.CurrentGenericRevenueTable.timestamp
import com.moregames.base.util.*
import com.moregames.playtime.earnings.dto.GenericRevenueDto
import com.moregames.playtime.earnings.dto.UserEarningsQuotasDto
import com.moregames.playtime.user.RevenueByPeriod
import com.moregames.playtime.user.addRevenueByPeriods
import com.moregames.playtime.user.cashout.prepareUserCashoutTransaction
import com.moregames.playtime.user.challenge.dto.ChallengeEventState
import com.moregames.playtime.user.challenge.dto.ChallengeEventType
import com.moregames.playtime.user.challenge.dto.SpecialChallengePotState
import com.moregames.playtime.user.challenge.table.ChallengeEventTable
import com.moregames.playtime.user.challenge.table.SpecialChallengePotTable
import com.moregames.playtime.user.challenge.table.UserChallengeEventTable
import com.moregames.playtime.user.challenge.table.UserSpecialChallengePotTable
import com.moregames.playtime.user.prepareUser
import com.moregames.playtime.user.trackLowEarningsCashoutPeriod
import com.moregames.playtime.utils.CAD
import com.moregames.playtime.utils.USD
import kotlinx.coroutines.*
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.mock
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*
import kotlin.test.assertFailsWith
import kotlin.test.assertNotNull
import kotlin.test.assertNull

@ExperimentalCoroutinesApi
@ExtendWith(DatabaseExtension::class)
class UserEarningsPersistenceServiceTest(private val database: Database) {

  private val timeService: TimeService = mock()

  private var service = UserEarningsPersistenceService(database, timeService)

  val now: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)
  val someEarningsStub = UserCurrencyEarnings(BigDecimal.ONE, USD, BigDecimal.ONE)
  val noEarningsStub = UserCurrencyEarnings(BigDecimal.ZERO, USD, BigDecimal.ZERO)

  private val userCoinsBasedEarningsData = UserCoinsBasedEarningsData(
    userId = "userId",
    coinsForOneDollar = BigDecimal("100.0"),
    periodStart = now.plusSeconds(10),
    periodEnd = now.plusSeconds(40),
    userQuality = BigDecimal("0.998"),
    currentToFirstEcpmRatio = BigDecimal("1.00001"),
    noEarnings = true,
    coinGoal = 42,
    earningsAmount = BigDecimal("7.7004"),
    em2Coins = BigDecimal("10.0004"),
    em2GameCoins = BigDecimal("5.0004"),
    em2BonusCoins = BigDecimal("4.0004"),
    em2OfferCoins = BigDecimal("3.0004"),
    realRevenue = BigDecimal("6.0004"),
    realGameRevenue = BigDecimal("2.0004"),
    em2Revenue = BigDecimal("3.5004"),
    em2Capped = BigDecimal("0.5004"),
    em2GameRevenue = BigDecimal("1.5004"),
    em2GameCapped = BigDecimal("0.4004"),
    em2Variation = Variations.EM2_BETA_01,
    usedQuota = BigDecimal("1.5000"),
  )

  @BeforeEach
  fun before() {
    service = UserEarningsPersistenceService(database, timeService)
    transaction(database) {
      UserSpecialChallengeRewardedTable.deleteAll()
      UserSpecialChallengePotTable.deleteAll()
      SpecialChallengePotTable.deleteAll()
    }
    Dispatchers.setMain(StandardTestDispatcher())
  }

  @Test
  fun `SHOULD return unpaid earnings ON loadUnpaidUserCurrencyEarnings`() = runTest {
    val userId = database.prepareUser()

    insertEarnings(userId = userId, usdEarnings = BigDecimal.ONE, userCurrencyCode = "CAD", userCurrencyEarnings = BigDecimal("1.10"))
    insertEarnings(userId = userId, usdEarnings = BigDecimal.TEN, userCurrencyCode = "CAD", userCurrencyEarnings = BigDecimal("11.00"))

    val actual = service.loadUnpaidUserCurrencyEarnings(userId)

    assertThat(actual).isEqualTo(
      UserCurrencyEarnings(
        amountUsd = BigDecimal("11.000000"),
        userCurrency = Currency.getInstance("CAD"),
        userCurrencyAmount = BigDecimal("12.100000")
      )
    )
  }

  @Test
  fun `SHOULD return unpaid earnings ON loadUnpaidUserCurrencyEarnings WHEN some entries have boosted coins`() = runTest {
    val userId = database.prepareUser()

    insertEarnings(userId = userId, usdEarnings = BigDecimal.ONE, userCurrencyCode = "CAD", userCurrencyEarnings = BigDecimal("1.10"))
    insertEarnings(
      userId = userId,
      usdEarnings = BigDecimal.TEN,
      nonBoostedUsdEarnings = BigDecimal("9.00"),
      userCurrencyCode = "CAD",
      userCurrencyEarnings = BigDecimal("11.00"),
      nonBoostedUserCurrencyEarnings = BigDecimal("10.00"),
    )

    val actual = service.loadUnpaidUserCurrencyEarnings(userId)

    assertThat(actual).isEqualTo(
      UserCurrencyEarnings(
        amountUsd = BigDecimal("11.000000"),
        nonBoostedAmountUsd = BigDecimal("10.000000"),
        userCurrency = Currency.getInstance("CAD"),
        userCurrencyAmount = BigDecimal("12.100000"),
        nonBoostedUserCurrencyAmount = BigDecimal("11.100000")
      )
    )
  }

  @Test
  fun `SHOULD return unpaid earnings ON loadUnpaidUserCurrencyEarnings WHEN all entries have boosted coins`() = runTest {
    val userId = database.prepareUser()

    insertEarnings(
      userId = userId,
      usdEarnings = BigDecimal.ONE,
      nonBoostedUsdEarnings = BigDecimal("0.9"),
      userCurrencyCode = "CAD",
      userCurrencyEarnings = BigDecimal("1.10"),
      nonBoostedUserCurrencyEarnings = BigDecimal("1.00"),
    )
    insertEarnings(
      userId = userId,
      usdEarnings = BigDecimal.TEN,
      nonBoostedUsdEarnings = BigDecimal("9.00"),
      userCurrencyCode = "CAD",
      userCurrencyEarnings = BigDecimal("11.00"),
      nonBoostedUserCurrencyEarnings = BigDecimal("10.00"),
    )

    val actual = service.loadUnpaidUserCurrencyEarnings(userId)

    assertThat(actual).isEqualTo(
      UserCurrencyEarnings(
        amountUsd = BigDecimal("11.000000"),
        nonBoostedAmountUsd = BigDecimal("9.900000"),
        userCurrency = Currency.getInstance("CAD"),
        userCurrencyAmount = BigDecimal("12.100000"),
        nonBoostedUserCurrencyAmount = BigDecimal("11.000000")
      )
    )
  }

  @Test
  fun `SHOULD return user earnings ON loadUserEarningsForMetaId WHEN earnings exist for meta id`() = runTest {
    val userId = database.prepareUser()
    val metaId = insertMetaEarnings()

    insertEarnings(userId = userId, usdEarnings = BigDecimal.ONE, metaId = metaId)
    insertEarnings(userId = userId, usdEarnings = BigDecimal.ONE, metaId = null)

    val actual = service.loadUserEarningsForMetaId(metaId)

    assertNotNull(actual)
    assertThat(actual.userId).isEqualTo(userId)
    assertThat(actual.calculatedUserEarningUsd).isEqualByComparingTo(BigDecimal.ONE)
    assertThat(actual.metaUserEarningsId).isEqualTo(metaId)
    assertThat(actual.cashoutTransactionId).isNull()
  }

  @Test
  fun `SHOULD return empty list ON loadUserEarningsForMetaId WHEN NOT earnings exist for meta id`() = runTest {
    val userId = database.prepareUser()
    val metaId = transaction(database) {
      MetaUserEarningsTable.insert { } get MetaUserEarningsTable.id
    }

    insertEarnings(userId = userId, usdEarnings = BigDecimal.ONE, metaId = null)

    val actual = service.loadUserEarningsForMetaId(metaId)

    assertNull(actual)
  }

  @Test
  fun `SHOULD return total earnings ON calculateTotalUsdEarningsForUser WHEN user has earnings`() = runTest {
    val userId = database.prepareUser()

    insertEarnings(userId = userId, usdEarnings = BigDecimal.ONE)
    insertEarnings(userId = userId, usdEarnings = BigDecimal.TEN)

    val actual = service.calculateTotalUsdEarningsForUser(userId)

    assertNotNull(actual)
    assertThat(actual).isEqualByComparingTo(BigDecimal("11"))
  }

  @Test
  fun `SHOULD return null ON calculateTotalUsdEarningsForUser WHEN NOT user has earnings`() = runTest {
    val userId = database.prepareUser()

    val actual = service.calculateTotalUsdEarningsForUser(userId)

    assertNull(actual)
  }

  @Test
  fun `SHOULD update unpaid earnings ON updateUnpaidUserEarningsForUser WHEN user has unpaid earnings`() = runTest {
    val userId = database.prepareUser()
    val existingTransactionId = insertTransaction(userId)
    insertEarnings(userId = userId, transactionId = existingTransactionId)
    val earningsId = 123
    insertEarnings(userId = userId, transactionId = null, id = earningsId)
    val newTransactionId = insertTransaction(userId)


    service.updateUnpaidUserEarningsForDemand(userId, newTransactionId)

    val actual = transaction(database) {
      UserEarningsTable.select { UserEarningsTable.id eq earningsId }.first()[UserEarningsTable.cashoutTransactionId]
    }

    assertThat(actual).isEqualTo(newTransactionId)
  }

  @Test
  fun `SHOULD clear transaction ids ON clearEarningsTransactionInfo WHEN transaction id is given`() {
    val userId = database.prepareUser()
    val transactionId = insertTransaction(userId)
    val earningsId = 555
    insertEarnings(userId = userId, transactionId = transactionId, id = earningsId)

    runTest {
      service.clearEarningsTransactionInfo(listOf(transactionId))
    }

    val actual = transaction(database) {
      UserEarningsTable.select { UserEarningsTable.id eq earningsId }.first()[UserEarningsTable.cashoutTransactionId]
    }

    assertThat(actual).isNull()
  }

  @Test
  fun `SHOULD do nothing ON clearEarningsTransactionInfo WHEN NOT transaction id is given`() {
    val userId = database.prepareUser()
    val transactionId = insertTransaction(userId)
    val earningsId = 7711
    insertEarnings(userId = userId, transactionId = transactionId, id = earningsId)

    runTest {
      service.clearEarningsTransactionInfo(listOf())
    }

    val actual = transaction(database) {
      UserEarningsTable.select { UserEarningsTable.id eq earningsId }.first()[UserEarningsTable.cashoutTransactionId]
    }

    assertThat(actual).isEqualTo(transactionId)
  }

  @Test
  fun `SHOULD save user revenue ON addUserRevenue`() = runTest {
    transaction(database) { CurrentGenericRevenueExtraTable.deleteAll() }
    transaction(database) { CurrentGenericRevenueTable.deleteAll() }

    val userId = database.prepareUser()
    val eventId = UUID.randomUUID().toString()
    val eventId2 = UUID.randomUUID().toString()
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val gameId = database.prepareGame(gameStub.copy(applicationId = "some.game"))


    service.addUserRevenue(
      eventId = eventId,
      userId = userId,
      source = RevenueSource.APPLOVIN,
      timestamp = now,
      amount = BigDecimal.TEN,
      networkId = -1,
      gameId = gameId,
      amountExtra = null,
    )
      .also { assertThat(it).isTrue() }
    service.addUserRevenue(
      eventId = eventId, // insertion with same eventId will be ignored
      userId = userId,
      source = RevenueSource.APPLOVIN,
      timestamp = now,
      amount = BigDecimal.ONE,
      networkId = -1,
      gameId = gameId,
      amountExtra = null,
    )
      .also { assertThat(it).isFalse() }
    service.addUserRevenue(
      eventId = eventId2, // different eventId
      userId = userId,
      source = RevenueSource.IRON_SOURCE,
      timestamp = now,
      amount = BigDecimal.ONE,
      networkId = -1,
      gameId = gameId,
      amountExtra = null,
    )
      .also { assertThat(it).isTrue() }


    transaction(database) {
      CurrentGenericRevenueTable.select { CurrentGenericRevenueTable.id eq eventId }.first().let {
        assertThat(it[CurrentGenericRevenueTable.userId]).isEqualTo(userId)
        assertThat(it[CurrentGenericRevenueTable.revenueSource]).isEqualTo(RevenueSource.APPLOVIN.name)
        assertThat(it[timestamp]).isEqualTo(now)
        assertThat(it[CurrentGenericRevenueTable.revenueAmount]).isEqualTo(BigDecimal("10.000000000000"))
        assertThat(it[metaUserEarningsId]).isNull()
        assertThat(it[networkId]).isEqualTo(-1)
        assertThat(it[CurrentGenericRevenueTable.gameId]).isEqualTo(gameId)
      }

      assertThat(CurrentGenericRevenueExtraTable.selectAll().count()).isEqualTo(0)
    }

  }


  @Test
  fun `SHOULD save user revenue ON addUserRevenue WHEN extra revenue defined`() = runTest {
    transaction(database) { CurrentGenericRevenueExtraTable.deleteAll() }
    transaction(database) { CurrentGenericRevenueTable.deleteAll() }

    val userId = database.prepareUser()
    val eventId = UUID.randomUUID().toString()
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val gameId = insertGames()

    service.addUserRevenue(
      eventId = eventId,
      userId = userId,
      source = RevenueSource.APPLOVIN,
      timestamp = now,
      amount = BigDecimal.TEN,
      networkId = -1,
      gameId = gameId,
      amountExtra = BigDecimal.ONE,
    )
      .also { assertThat(it).isTrue() }

    transaction(database) {
      CurrentGenericRevenueExtraTable.select { CurrentGenericRevenueExtraTable.id eq eventId }.first().let {
        assertThat(it[CurrentGenericRevenueExtraTable.revenueAmountExtra]).isEqualTo(BigDecimal("1.000000000000"))
      }
    }
  }

  @Test
  fun `SHOULD update generic totals ON addUserRevenue`() = runTest {
    val userId = database.prepareUser()
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    transaction(database) {
      GenericRevenueTotalsTable.insert {
        it[GenericRevenueTotalsTable.userId] = userId
        it[revenueAmount] = BigDecimal.ONE
        it[day2Revenue] = BigDecimal.ONE
        it[day0Revenue] = BigDecimal.ONE
        it[createdAt] = now.minus(49, ChronoUnit.HOURS)
      }
    }

    service.trackGenericRevenueTotals(
      userId = userId,
      source = RevenueSource.APPLOVIN,
      timestamp = now.minus(26, ChronoUnit.HOURS),
      amount = BigDecimal.TEN,
    )

    service.trackGenericRevenueTotals(
      userId = userId,
      source = RevenueSource.APPLOVIN,
      timestamp = now.minus(2, ChronoUnit.HOURS),
      amount = BigDecimal.TEN,
    )
    service.trackGenericRevenueTotals(
      userId = userId,
      source = RevenueSource.FYBER,
      timestamp = now.minus(1, ChronoUnit.HOURS),
      amount = BigDecimal.ONE,
    )
    service.trackGenericRevenueTotals(
      userId = userId,
      source = RevenueSource.TAPJOY,
      timestamp = now.minus(1, ChronoUnit.HOURS),
      amount = BigDecimal.ONE,
    )
    service.trackGenericRevenueTotals(
      userId = userId,
      source = RevenueSource.ADJOE,
      timestamp = now.minus(1, ChronoUnit.HOURS),
      amount = BigDecimal.ONE,
    )
    service.trackGenericRevenueTotals(
      userId = userId,
      source = RevenueSource.TAPJOY,
      timestamp = now, // some fresh revenue, more than 2 days have passed
      amount = BigDecimal.ONE,
    )

    transaction(database) {
      GenericRevenueTotalsTable
        .select { GenericRevenueTotalsTable.userId eq userId }
        .first()
        .let {
          assertThat(it[GenericRevenueTotalsTable.revenueAmount]).isEqualTo(BigDecimal("25.000000000000"))
          assertThat(it[GenericRevenueTotalsTable.offerwallRevenue]).isEqualTo(BigDecimal("4.000000000000"))
          assertThat(it[GenericRevenueTotalsTable.day2Revenue]).isEqualTo(BigDecimal("24.000000000000"))
          assertThat(it[GenericRevenueTotalsTable.day0Revenue]).isEqualTo(BigDecimal("11.000000000000"))
        }
    }
  }

  @Test
  fun `SHOULD extract revenue that is not associated with earnings meta ON getUnconvertedRevenue`() = runTest {
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val revenueEventId1 = UUID.randomUUID().toString() // already associated with meta
    val revenueEventId2 = UUID.randomUUID().toString()
    val revenueEventId3 = UUID.randomUUID().toString() // another user
    val gameId1 = database.prepareGame()
    transaction(database) {
      val previousMetaId = MetaUserEarningsTable.insert { } get MetaUserEarningsTable.id

      CurrentGenericRevenueTable.insert {
        it[id] = revenueEventId1
        it[userId] = userId1
        it[revenueSource] = RevenueSource.APPLOVIN.name
        it[timestamp] = now
        it[revenueAmount] = BigDecimal("1.00")
        it[metaUserEarningsId] = previousMetaId
        it[networkId] = -1
      }

      CurrentGenericRevenueTable.insert {
        it[id] = revenueEventId2
        it[userId] = userId1
        it[revenueSource] = RevenueSource.IRON_SOURCE.name
        it[timestamp] = now
        it[revenueAmount] = BigDecimal("2.00")
        it[networkId] = -1
        it[gameId] = gameId1
      }

      CurrentGenericRevenueTable.insert {
        it[id] = revenueEventId3
        it[userId] = userId2
        it[revenueSource] = RevenueSource.APPLOVIN.name
        it[timestamp] = now
        it[revenueAmount] = BigDecimal("3.00")
        it[networkId] = -1
      }
    }

    val actual = service.getUnconvertedRevenue(userId1)

    assertThat(actual).isEqualTo(
      listOf(
        GenericRevenueDto(
          id = revenueEventId2,
          userId = userId1,
          source = RevenueSource.IRON_SOURCE,
          timestamp = now,
          amount = BigDecimal("2.000000000000"),
          amountExtra = null,
          gameId = gameId1,
        )
      )
    )
  }


  @Test
  fun `SHOULD extract revenue ON getUnconvertedRevenue WHEN extra amount exists`() = runTest {
    transaction(database) {
      CurrentGenericRevenueTable.deleteAll()
      CurrentGenericRevenueExtraTable.deleteAll()
    }
    transaction(database) { }

    val userId1 = database.prepareUser()
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val revenueEventId1 = UUID.randomUUID().toString()
    val revenueEventId2 = UUID.randomUUID().toString()
    transaction(database) {

      CurrentGenericRevenueTable.insert {
        it[id] = revenueEventId1
        it[userId] = userId1
        it[revenueSource] = RevenueSource.APPLOVIN.name
        it[timestamp] = now
        it[revenueAmount] = BigDecimal("1.00")
        it[networkId] = -1
      }

      CurrentGenericRevenueTable.insert {
        it[id] = revenueEventId2
        it[userId] = userId1
        it[revenueSource] = RevenueSource.IRON_SOURCE.name
        it[timestamp] = now
        it[revenueAmount] = BigDecimal("2.00")
        it[networkId] = -1
      }

      CurrentGenericRevenueExtraTable.insert {
        it[id] = revenueEventId2
        it[revenueAmountExtra] = BigDecimal("1.00")
      }
    }

    val actual = service.getUnconvertedRevenue(userId1)

    assertThat(actual).containsOnly(
      GenericRevenueDto(
        id = revenueEventId1,
        userId = userId1,
        source = RevenueSource.APPLOVIN,
        timestamp = now,
        amount = BigDecimal("1.000000000000"),
        amountExtra = null,
        gameId = null,
      ),
      GenericRevenueDto(
        id = revenueEventId2,
        userId = userId1,
        source = RevenueSource.IRON_SOURCE,
        timestamp = now,
        amount = BigDecimal("2.000000000000"),
        amountExtra = BigDecimal("1.000000000000"),
        gameId = null,
      )
    )
  }

  @Test
  fun `SHOULD track metaId and associate revenue with earnings ON bindRevenueWithEarnings`() = runTest {
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()

    insertCurrentCoinsBalance(userId1, 1200)
    insertCurrentCoinsBalanceEm2(userId1, 1200)

    val revenueEventId1 = UUID.randomUUID().toString() // already associated with meta
    val revenueEventId2 = UUID.randomUUID().toString()
    val revenueEventId3 = UUID.randomUUID().toString() // another user
    var previousMetaId = 0
    transaction(database) {
      previousMetaId = MetaUserEarningsTable.insert { } get MetaUserEarningsTable.id

      CurrentGenericRevenueTable.insert {
        it[id] = revenueEventId1
        it[userId] = userId1
        it[revenueSource] = RevenueSource.APPLOVIN.name
        it[timestamp] = Instant.now()
        it[revenueAmount] = BigDecimal("1.00")
        it[metaUserEarningsId] = previousMetaId
        it[networkId] = -1
      }

      CurrentGenericRevenueTable.insert {
        it[id] = revenueEventId2
        it[userId] = userId1
        it[revenueSource] = RevenueSource.IRON_SOURCE.name
        it[timestamp] = Instant.now()
        it[revenueAmount] = BigDecimal("2.00")
        it[networkId] = -1
      }

      CurrentGenericRevenueTable.insert {
        it[id] = revenueEventId3
        it[userId] = userId2
        it[revenueSource] = RevenueSource.APPLOVIN.name
        it[timestamp] = Instant.now()
        it[revenueAmount] = BigDecimal("3.00")
        it[networkId] = -1
      }
    }

    val metaId = service.bindRevenueWithEarnings(
      userId1,
      listOf(revenueEventId2),
      calculatedUserEarningUsd = BigDecimal("0.90"),
      nonBoostedUserEarningUsd = null,
      userCurrency = Currency.getInstance("CAD"),
      userCurrencyEarningAmount = BigDecimal("1.11"),
      nonBoostedUserCurrencyEarningAmount = null,
    )

    transaction(database) {
      CurrentGenericRevenueTable.select { CurrentGenericRevenueTable.id eq revenueEventId1 }.first().let {
        assertThat(it[metaUserEarningsId]).isEqualTo(previousMetaId)
      }
      CurrentGenericRevenueTable.select { CurrentGenericRevenueTable.id eq revenueEventId2 }.first().let {
        assertThat(it[metaUserEarningsId]).isEqualTo(metaId)
      }
      UserEarningsTable.select { UserEarningsTable.metaUserEarningsId eq metaId }.first().let {
        assertThat(it[UserEarningsTable.userId]).isEqualTo(userId1)
        assertThat(it[calculatedUserEarningUsd]).isEqualTo(BigDecimal("0.900000"))
      }
    }
    // check all coins are (re)set
    transaction(database) {
      UserCoinGoalCurrentCoinsBalanceTable
        .select { UserCoinGoalCurrentCoinsBalanceTable.userId eq userId1 }
        .first()
        .let {
          assertThat(it[UserCoinGoalCurrentCoinsBalanceTable.offerCoins]).isEqualTo(0)
          assertThat(it[UserCoinGoalCurrentCoinsBalanceTable.bonusCoins]).isEqualTo(0)
          assertThat(it[UserCoinGoalCurrentCoinsBalanceTable.coins]).isEqualTo(0)
          assertThat(it[UserCoinGoalCurrentCoinsBalanceTable.goalCoins]).isEqualTo(0)
        }
      UserCoinGoalCurrentCoinsBalanceEm2Table
        .select { UserCoinGoalCurrentCoinsBalanceEm2Table.userId eq userId1 }
        .first()
        .let {
          assertThat(it[UserCoinGoalCurrentCoinsBalanceEm2Table.offerCoins]).isEqualByComparingTo(BigDecimal.ZERO)
          assertThat(it[UserCoinGoalCurrentCoinsBalanceEm2Table.bonusCoins]).isEqualByComparingTo(BigDecimal.ZERO)
          assertThat(it[UserCoinGoalCurrentCoinsBalanceEm2Table.gameCoins]).isEqualByComparingTo(BigDecimal.ZERO)
          assertThat(it[UserCoinGoalCurrentCoinsBalanceEm2Table.goalCoins]).isNotNull().isEqualByComparingTo(BigDecimal.ZERO)
        }
    }
  }

  @Test
  fun `SHOULD track metaId but not create earnings ON bindRevenueWithEarnings WHEN earnings amount is zero`() = runTest {
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()

    val revenueEventId1 = UUID.randomUUID().toString() // already associated with meta
    val revenueEventId2 = UUID.randomUUID().toString()
    val revenueEventId3 = UUID.randomUUID().toString() // another user
    var previousMetaId = 0
    transaction(database) {
      previousMetaId = MetaUserEarningsTable.insert { } get MetaUserEarningsTable.id

      CurrentGenericRevenueTable.insert {
        it[id] = revenueEventId1
        it[userId] = userId1
        it[revenueSource] = RevenueSource.APPLOVIN.name
        it[timestamp] = Instant.now()
        it[revenueAmount] = BigDecimal("1.00")
        it[metaUserEarningsId] = previousMetaId
        it[networkId] = -1
      }

      CurrentGenericRevenueTable.insert {
        it[id] = revenueEventId2
        it[userId] = userId1
        it[revenueSource] = RevenueSource.IRON_SOURCE.name
        it[timestamp] = Instant.now()
        it[revenueAmount] = BigDecimal("2.00")
        it[networkId] = -1
      }

      CurrentGenericRevenueTable.insert {
        it[id] = revenueEventId3
        it[userId] = userId2
        it[revenueSource] = RevenueSource.APPLOVIN.name
        it[timestamp] = Instant.now()
        it[revenueAmount] = BigDecimal("3.00")
        it[networkId] = -1
      }
    }

    val metaId = service.bindRevenueWithEarnings(
      userId1,
      listOf(revenueEventId2),
      calculatedUserEarningUsd = BigDecimal("0.00"),
      nonBoostedUserEarningUsd = null,
      userCurrency = Currency.getInstance("CAD"),
      userCurrencyEarningAmount = BigDecimal("0.00"),
      nonBoostedUserCurrencyEarningAmount = null,
    )

    transaction(database) {
      CurrentGenericRevenueTable.select { CurrentGenericRevenueTable.id eq revenueEventId1 }.first().let {
        assertThat(it[metaUserEarningsId]).isEqualTo(previousMetaId)
      }
      CurrentGenericRevenueTable.select { CurrentGenericRevenueTable.id eq revenueEventId2 }.first().let {
        assertThat(it[metaUserEarningsId]).isEqualTo(metaId)
      }
      UserEarningsTable.select { UserEarningsTable.metaUserEarningsId eq metaId }.firstOrNull().let {
        assertThat(it).isNull()
      }
    }
  }

  @Test
  fun `SHOULD fail ON bindRevenueWithEarnings WHEN earnings meta is already added to revenue`() = runTest {
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()

    val revenueEventId1 = UUID.randomUUID().toString() // already associated with meta
    val revenueEventId2 = UUID.randomUUID().toString()
    val revenueEventId3 = UUID.randomUUID().toString() // another user
    var previousMetaId = 0
    transaction(database) {
      previousMetaId = MetaUserEarningsTable.insert { } get MetaUserEarningsTable.id

      CurrentGenericRevenueTable.insert {
        it[id] = revenueEventId1
        it[userId] = userId1
        it[revenueSource] = RevenueSource.APPLOVIN.name
        it[timestamp] = Instant.now()
        it[revenueAmount] = BigDecimal("1.00")
        it[metaUserEarningsId] = previousMetaId
        it[networkId] = -1
      }

      CurrentGenericRevenueTable.insert {
        it[id] = revenueEventId2
        it[userId] = userId1
        it[revenueSource] = RevenueSource.IRON_SOURCE.name
        it[timestamp] = Instant.now()
        it[revenueAmount] = BigDecimal("2.00")
        it[networkId] = -1
      }

      CurrentGenericRevenueTable.insert {
        it[id] = revenueEventId3
        it[userId] = userId2
        it[revenueSource] = RevenueSource.APPLOVIN.name
        it[timestamp] = Instant.now()
        it[revenueAmount] = BigDecimal("3.00")
        it[networkId] = -1
      }
    }

    assertFailsWith(IllegalStateException::class) {
      service.bindRevenueWithEarnings(
        userId1,
        listOf(revenueEventId1, revenueEventId2),
        calculatedUserEarningUsd = BigDecimal("0.90"),
        nonBoostedUserEarningUsd = null,
        userCurrency = Currency.getInstance("CAD"),
        userCurrencyEarningAmount = BigDecimal("1.11"),
        nonBoostedUserCurrencyEarningAmount = null,
      )
    }.let {
      assertThat(it.message).isEqualTo("Can't convert revenue '${listOf(revenueEventId1, revenueEventId2)}' to earnings. Part of revenue is already converted.")
    }

    transaction(database) {
      CurrentGenericRevenueTable.select { CurrentGenericRevenueTable.id eq revenueEventId1 }.first().let {
        assertThat(it[metaUserEarningsId]).isEqualTo(previousMetaId)
      }
      CurrentGenericRevenueTable.select { CurrentGenericRevenueTable.id eq revenueEventId2 }.first().let {
        assertThat(it[metaUserEarningsId]).isNull()
      }
    }
  }


  @Test
  fun `SHOULD store nonBoosted earnings ON bindRevenueWithEarnings`() = runTest {
    val userId1 = database.prepareUser()

    val revenueEventId2 = UUID.randomUUID().toString()

    transaction(database) {
      CurrentGenericRevenueTable.insert {
        it[id] = revenueEventId2
        it[userId] = userId1
        it[revenueSource] = RevenueSource.IRON_SOURCE.name
        it[timestamp] = Instant.now()
        it[revenueAmount] = BigDecimal("2.00")
        it[networkId] = -1
      }
    }

    val metaId = service.bindRevenueWithEarnings(
      userId1,
      listOf(revenueEventId2),
      calculatedUserEarningUsd = BigDecimal("0.90"),
      nonBoostedUserEarningUsd = BigDecimal("0.80"),
      userCurrency = Currency.getInstance("CAD"),
      userCurrencyEarningAmount = BigDecimal("1.11"),
      nonBoostedUserCurrencyEarningAmount = BigDecimal("1.01"),
    )

    transaction(database) {
      UserEarningsTable.select { UserEarningsTable.metaUserEarningsId eq metaId }.first().let {
        assertThat(it[UserEarningsTable.userId]).isEqualTo(userId1)
        assertThat(it[nonBoostedUserEarningUsd]).isEqualTo(BigDecimal("0.800000"))
        assertThat(it[nonBoostedCurrencyEarningAmount]).isEqualTo(BigDecimal("1.010000"))
      }
    }
  }

  @Test
  fun `SHOULD update generic totals right ON addUserRevenue WHEN there are hell lot of updating threads and user has total row`() = runTest {
    val userId = database.prepareUser()
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val testScope = TestScope()

    transaction(database) {
      GenericRevenueTotalsTable.insert {
        it[GenericRevenueTotalsTable.userId] = userId
        it[revenueAmount] = BigDecimal.ONE
      }
    }

    val results = (1..20).map {
      testScope.async {
        service.trackGenericRevenueTotals(
          userId = userId,
          source = RevenueSource.APPLOVIN,
          timestamp = now,
          amount = BigDecimal.TEN,
        )
      }
    }


    results.awaitAll()


    transaction(database) {
      GenericRevenueTotalsTable
        .select { GenericRevenueTotalsTable.userId eq userId }
        .first()
        .let { assertThat(it[GenericRevenueTotalsTable.revenueAmount]).isEqualTo(BigDecimal("201.000000000000")) }
    }
  }

  @Test
  fun `SHOULD store in UserApplovinRevenueByPeriodsTable ON trackApplovinNonBannerRevenueBy5min`() = runTest {
    val userId = database.prepareUser()
    val gameId = database.prepareGame()
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)

    repeat(2) {
      service.trackApplovinNonBannerRevenueBy5min(
        userId = userId,
        timestamp = now,
        amount = BigDecimal.TEN,
        gameId = gameId
      )
    }

    transaction(database) {
      UserApplovinRevenueByPeriodsTable
        .slice(UserApplovinRevenueByPeriodsTable.revenue, UserApplovinRevenueByPeriodsTable.revenueTransactionsCount)
        .select {
          (UserApplovinRevenueByPeriodsTable.userId eq userId) and
            (UserApplovinRevenueByPeriodsTable.gameId eq gameId) and
            (UserApplovinRevenueByPeriodsTable.periodStart eq now.toBeginningOf5MinInterval())
        }
        .first()
        .let {
          assertThat(it[UserApplovinRevenueByPeriodsTable.revenue]).isEqualByComparingTo(BigDecimal("20.0"))
          assertThat(it[UserApplovinRevenueByPeriodsTable.revenueTransactionsCount]).isEqualTo(2)
        }
    }
  }


  @Test
  fun `SHOULD remove old data from current generic revenue ON removeOldDataFromCurrentGenericRevenue`() = runTest {
    transaction(database) { CurrentGenericRevenueExtraTable.deleteAll() }

    val now = Instant.now()
    val longTimeAgo = now.minus(CURRENT_GENERIC_REVENUE_HOURS + 1, ChronoUnit.HOURS)
    val userId = database.prepareUser()
    val userId2 = database.prepareUser()

    val metaId = insertMetaEarnings()
    transaction(database) {
      // new, non-calculated, keep
      (1..5).forEach { i ->
        val pk = "new-non-calculated-keep-$i"

        CurrentGenericRevenueTable.insert {
          it[id] = pk
          it[CurrentGenericRevenueTable.userId] = userId
          it[revenueSource] = "APPLOVIN"
          it[timestamp] = now
          it[revenueAmount] = BigDecimal.TEN
          it[networkId] = -1
          it[metaUserEarningsId] = null
        }

        CurrentGenericRevenueExtraTable.insert {
          it[id] = pk
          it[revenueAmountExtra] = BigDecimal.ONE
        }
      }

      CurrentGenericRevenueTable
        .batchInsert((1..5), ignore = true, shouldReturnGeneratedValues = false) // new calculated, user2, keep
        {
          this[CurrentGenericRevenueTable.userId] = userId2
          this[CurrentGenericRevenueTable.revenueSource] = "APPLOVIN"
          this[timestamp] = now
          this[CurrentGenericRevenueTable.revenueAmount] = BigDecimal.TEN
          this[networkId] = -1
          this[CurrentGenericRevenueTable.id] = UUID.randomUUID().toString()
          this[metaUserEarningsId] = metaId
        }
      CurrentGenericRevenueTable
        .batchInsert((1..5), ignore = true, shouldReturnGeneratedValues = false) // old, non-calculated, keep
        {
          this[CurrentGenericRevenueTable.userId] = userId
          this[CurrentGenericRevenueTable.revenueSource] = "APPLOVIN"
          this[timestamp] = longTimeAgo
          this[CurrentGenericRevenueTable.revenueAmount] = BigDecimal.TEN
          this[networkId] = -1
          this[CurrentGenericRevenueTable.id] = UUID.randomUUID().toString()
          this[metaUserEarningsId] = null
        }
      CurrentGenericRevenueTable
        .batchInsert((1..5), ignore = true, shouldReturnGeneratedValues = false) // old calculated, user2, remove
        {
          this[CurrentGenericRevenueTable.userId] = userId2
          this[CurrentGenericRevenueTable.revenueSource] = "APPLOVIN"
          this[timestamp] = longTimeAgo
          this[CurrentGenericRevenueTable.revenueAmount] = BigDecimal.TEN
          this[networkId] = -1
          this[CurrentGenericRevenueTable.id] = UUID.randomUUID().toString()
          this[metaUserEarningsId] = metaId
        }

      CurrentGenericRevenueTable
        .batchInsert((1..5), ignore = true, shouldReturnGeneratedValues = false) // old, calculated, remove
        {
          this[CurrentGenericRevenueTable.userId] = userId
          this[CurrentGenericRevenueTable.revenueSource] = "APPLOVIN"
          this[timestamp] = longTimeAgo
          this[CurrentGenericRevenueTable.revenueAmount] = BigDecimal.TEN
          this[networkId] = -1
          this[CurrentGenericRevenueTable.id] = UUID.randomUUID().toString()
          this[metaUserEarningsId] = metaId
        }

      // old, calculated, and already removed from CurrentGenericRevenueTable
      (1..5).forEach { i ->
        val pk = "old-calculated-remove-$i"

        CurrentGenericRevenueExtraTable.insert {
          it[id] = pk
          it[revenueAmountExtra] = BigDecimal.ONE
        }
      }
    }


    service.removeOldDataFromCurrentGenericRevenue(15, 2)
      .let { rowsDeleted -> assertThat(rowsDeleted).isEqualTo(10) }

    transaction(database) {
      CurrentGenericRevenueTable
        .select { CurrentGenericRevenueTable.userId inList listOf(userId, userId2) }
        .count()
        .let { assertThat(it).isEqualTo(15) }

      CurrentGenericRevenueExtraTable
        .selectAll()
        .count()
        .let { assertThat(it).isEqualTo(8) }
    }
  }

  @Test
  fun `SHOULD remove old unconverted revenue by deleted users from current generic revenue ON removeUnconvertedOldRevenueForDeletedUsers`() = runTest {
    transaction(database) { CurrentGenericRevenueExtraTable.deleteAll() }

    val now = Instant.now()
    val longTimeAgo = now.minus(CURRENT_GENERIC_UNCONVERTED_REVENUE_HOURS + 1, ChronoUnit.HOURS)
    val userId = database.prepareUser()
    val userId2 = database.prepareUser(isDeleted = true)
    val userId3 = database.prepareUser(isDeleted = true)

    val metaId = insertMetaEarnings()
    transaction(database) {
      //fresh unconverted
      CurrentGenericRevenueTable.insert {
        it[id] = UUID.randomUUID().toString()
        it[CurrentGenericRevenueTable.userId] = userId
        it[revenueSource] = "APPLOVIN"
        it[timestamp] = now
        it[revenueAmount] = BigDecimal.TEN
        it[networkId] = -1
        it[metaUserEarningsId] = null
      }
      CurrentGenericRevenueTable.insert {
        it[id] = UUID.randomUUID().toString()
        it[CurrentGenericRevenueTable.userId] = userId2
        it[revenueSource] = "APPLOVIN"
        it[timestamp] = now
        it[revenueAmount] = BigDecimal.TEN
        it[networkId] = -1
        it[metaUserEarningsId] = null
      }

      //old converted
      CurrentGenericRevenueTable.insert {
        it[id] = UUID.randomUUID().toString()
        it[CurrentGenericRevenueTable.userId] = userId
        it[revenueSource] = "APPLOVIN"
        it[timestamp] = longTimeAgo
        it[revenueAmount] = BigDecimal.TEN
        it[networkId] = -1
        it[metaUserEarningsId] = metaId
      }
      CurrentGenericRevenueTable.insert {
        it[id] = UUID.randomUUID().toString()
        it[CurrentGenericRevenueTable.userId] = userId2
        it[revenueSource] = "APPLOVIN"
        it[timestamp] = longTimeAgo
        it[revenueAmount] = BigDecimal.TEN
        it[networkId] = -1
        it[metaUserEarningsId] = metaId
      }

      //old unconverted
      CurrentGenericRevenueTable.insert {
        it[id] = UUID.randomUUID().toString()
        it[CurrentGenericRevenueTable.userId] = userId
        it[revenueSource] = "APPLOVIN"
        it[timestamp] = longTimeAgo
        it[revenueAmount] = BigDecimal.TEN
        it[networkId] = -1
        it[metaUserEarningsId] = null
      }
      //these two should be removed
      CurrentGenericRevenueTable.insert {
        it[id] = UUID.randomUUID().toString()
        it[CurrentGenericRevenueTable.userId] = userId2
        it[revenueSource] = "APPLOVIN"
        it[timestamp] = longTimeAgo
        it[revenueAmount] = BigDecimal.TEN
        it[networkId] = -1
        it[metaUserEarningsId] = null
      }
      CurrentGenericRevenueTable.insert {
        it[id] = UUID.randomUUID().toString()
        it[CurrentGenericRevenueTable.userId] = userId3
        it[revenueSource] = "APPLOVIN"
        it[timestamp] = longTimeAgo
        it[revenueAmount] = BigDecimal("15.22")
        it[networkId] = -1
        it[metaUserEarningsId] = null
      }
    }

    service.removeUnconvertedOldRevenueForDeletedUsers()
      .let { rowsDeleted -> assertThat(rowsDeleted).isEqualTo(2 to BigDecimal("25.22").setScale(12)) }

    transaction(database) {
      CurrentGenericRevenueTable
        .select { CurrentGenericRevenueTable.userId inList listOf(userId, userId2, userId3) }
        .count()
        .let { assertThat(it).isEqualTo(5) }
    }
  }

  @Test
  fun `SHOULD return transaction amount ON getEarningsSumForTransaction`() = runTest {
    val userId = database.prepareUser()
    val cashoutTransactionId1 = database.prepareUserCashoutTransaction(userId = userId)
    val cashoutTransactionId2 = database.prepareUserCashoutTransaction(userId = userId)
    insertEarnings(
      userId = userId, usdEarnings = BigDecimal.ONE, transactionId = cashoutTransactionId1,
      userCurrencyCode = "CAD", userCurrencyEarnings = BigDecimal("1.10")
    )
    insertEarnings(
      userId = userId, usdEarnings = BigDecimal.TEN, transactionId = cashoutTransactionId1,
      userCurrencyCode = "CAD", userCurrencyEarnings = BigDecimal("11.00")
    )
    insertEarnings(
      userId = userId, usdEarnings = BigDecimal.TEN, transactionId = cashoutTransactionId2,
      userCurrencyCode = "CAD", userCurrencyEarnings = BigDecimal("11.00")
    )

    val actual = service.getEarningsSumForTransaction(cashoutTransactionId1)

    assertThat(actual).isEqualTo(
      UserCurrencyEarnings(
        amountUsd = BigDecimal("11.000000"),
        userCurrency = Currency.getInstance("CAD"),
        userCurrencyAmount = BigDecimal("12.100000")
      )
    )
  }

  @Test
  fun `SHOULD return true if user has no earnings after some date ON userHasNoEarningsAfter`() = runTest {
    val now = Instant.now()
    val userId = database.prepareUser()
    insertEarnings(userId = userId, createdAt = now.minusSeconds(10))
    insertEarnings(userId = userId, createdAt = now.minusSeconds(100))
    insertEarnings(userId = userId, createdAt = now.minusSeconds(1000))


    service.noEarningsAfter(userId, now)
      .let { assertThat(it).isTrue() }
  }

  @Test
  fun `SHOULD return false if user has some earnings after the date ON userHasNoEarningsAfter`() = runTest {
    val now = Instant.now()
    val userId = database.prepareUser()
    insertEarnings(userId = userId, createdAt = now.minusSeconds(10))
    insertEarnings(userId = userId, createdAt = now.minusSeconds(100))
    insertEarnings(userId = userId, createdAt = now.minusSeconds(1000))
    insertEarnings(userId = userId, createdAt = now.plusSeconds(1))


    service.noEarningsAfter(userId, now)
      .let { assertThat(it).isFalse() }
  }

  @Test
  fun `SHOULD return null ON getUserQuotas when there are no quotas for this user`() = runTest {
    val userId = database.prepareUser()

    val actual = service.getUserQuotas(userId)

    assertThat(actual).isEqualTo(null)
  }

  @Test
  fun `SHOULD return user quotas ON getUserQuotas when there are some quotas for this user`() = runTest {
    val userId = database.prepareUser()
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    transaction(database) {
      UserEarningsQuotasTable.insert {
        it[UserEarningsQuotasTable.userId] = userId
        it[quotas] = "1,1,1,1,1,1,0,1"
        it[periodEnd] = now
      }
    }

    val actual = service.getUserQuotas(userId)

    assertThat(actual).isEqualTo(UserEarningsQuotasDto(userId, "1,1,1,1,1,1,0,1".split(","), now))
  }

  @Test
  fun `SHOULD save user quotas ON saveUserQuotas`() {
    val userId = database.prepareUser()
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val userQuotasDto = UserEarningsQuotasDto(userId, "1,1,1,1,1,1,0,1".split(","), now)

    runTest {
      service.saveUserQuotas(userQuotasDto)
    }

    transaction(database) {
      UserEarningsQuotasTable
        .select { UserEarningsQuotasTable.userId eq userId }
        .map { assertThat(it[UserEarningsQuotasTable.quotas]).isEqualTo(userQuotasDto.quotas.joinToString(",")) }
        .count()
        .let { assertThat(it).isEqualTo(1) }
    }
  }

  @Test
  fun `SHOULD update user quotas ON saveUserQuotas WHEN there are some quotas`() {
    val userId = database.prepareUser()
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val userQuotasDto = UserEarningsQuotasDto(userId, "1,0,0,0,0,0,0,1".split(","), now)
    transaction(database) {
      UserEarningsQuotasTable.insert {
        it[UserEarningsQuotasTable.userId] = userId
        it[quotas] = "1,1,1,1,1,1,1,1"
        it[periodEnd] = now
      }
    }

    runTest {
      service.saveUserQuotas(userQuotasDto)
    }

    transaction(database) {
      UserEarningsQuotasTable
        .select { UserEarningsQuotasTable.userId eq userId }
        .map { assertThat(it[UserEarningsQuotasTable.quotas]).isEqualTo(userQuotasDto.quotas.joinToString(",")) }
        .count()
        .let { assertThat(it).isEqualTo(1) }
    }
  }

  @Test
  fun `SHOULD return false ON userEverHadEarnings WHEN user had no earnings`() = runTest {
    val userId = database.prepareUser()

    service.userEverHadEarnings(userId)
      .let { assertThat(it).isFalse() }
  }

  @Test
  fun `SHOULD return true ON userEverHadEarnings WHEN user had some earnings`() = runTest {
    val userId = database.prepareUser()
    insertEarnings(userId)

    service.userEverHadEarnings(userId)
      .let { assertThat(it).isTrue() }
  }

  @Test
  fun `SHOULD write coins based earnings ON trackEm2EarningsCalculationData`() = runTest {
    val userId = database.prepareUser()
    val earnings = userCoinsBasedEarningsData.copy(userId = userId)


    service.trackEm2EarningsCalculationData(earnings)


    transaction(database) {
      UserCoinsBasedEarningsTable
        .select { UserCoinsBasedEarningsTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserCoinsBasedEarningsTable.userId]).isEqualTo(earnings.userId)
          assertThat(it[UserCoinsBasedEarningsTable.coinsForOneDollar]).isEqualByComparingTo(earnings.coinsForOneDollar)
          assertThat(it[UserCoinsBasedEarningsTable.userQuality]!!).isEqualByComparingTo(earnings.userQuality!!)
          assertThat(it[UserCoinsBasedEarningsTable.currentToFirstEcpmRatio]!!).isEqualByComparingTo(earnings.currentToFirstEcpmRatio)
          assertThat(it[UserCoinsBasedEarningsTable.periodStart]).isEqualByComparingTo(earnings.periodStart)
          assertThat(it[UserCoinsBasedEarningsTable.periodEnd]).isEqualByComparingTo(earnings.periodEnd)
          assertThat(it[UserCoinsBasedEarningsTable.noEarnings]).isEqualByComparingTo(earnings.noEarnings)
          assertThat(it[UserCoinsBasedEarningsTable.coinGoal]).isEqualByComparingTo(earnings.coinGoal)
          assertThat(it[UserCoinsBasedEarningsTable.em2Coins]).isEqualByComparingTo(earnings.em2Coins)
          assertThat(it[UserCoinsBasedEarningsTable.earningsAmount]).isEqualByComparingTo(earnings.earningsAmount)
          assertThat(it[UserCoinsBasedEarningsTable.em2GameCoins]).isEqualByComparingTo(earnings.em2GameCoins)
          assertThat(it[UserCoinsBasedEarningsTable.em2BonusCoins]).isEqualByComparingTo(earnings.em2BonusCoins)
          assertThat(it[UserCoinsBasedEarningsTable.em2OfferCoins]).isEqualByComparingTo(earnings.em2OfferCoins)
          assertThat(it[UserCoinsBasedEarningsTable.realRevenue]).isEqualByComparingTo(earnings.realRevenue)
          assertThat(it[UserCoinsBasedEarningsTable.realGameRevenue]).isEqualByComparingTo(earnings.realGameRevenue)
          assertThat(it[UserCoinsBasedEarningsTable.em2Revenue]).isEqualByComparingTo(earnings.em2Revenue)
          assertThat(it[UserCoinsBasedEarningsTable.em2Capped]).isEqualByComparingTo(earnings.em2Capped)
          assertThat(it[UserCoinsBasedEarningsTable.em2GameRevenue]).isEqualByComparingTo(earnings.em2GameRevenue)
          assertThat(it[UserCoinsBasedEarningsTable.em2GameCapped]).isEqualByComparingTo(earnings.em2GameCapped)
          assertThat(it[UserCoinsBasedEarningsTable.em2Variation]).isEqualByComparingTo((earnings.em2Variation as Variations).variationKey)
          assertThat(it[UserCoinsBasedEarningsTable.usedQuota]).isEqualByComparingTo(earnings.usedQuota)
        }
    }
  }

  @Test
  fun `SHOULD write coins based earnings for default variation ON trackEm2EarningsCalculationData`() {
    val userId = database.prepareUser()
    val earnings = userCoinsBasedEarningsData.copy(userId = userId, em2Variation = DEFAULT)

    runTest {
      service.trackEm2EarningsCalculationData(earnings)
    }

    transaction(database) {
      UserCoinsBasedEarningsTable
        .select { UserCoinsBasedEarningsTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserCoinsBasedEarningsTable.em2Variation]).isEqualTo(VARIATION_KEY_DEFAULT)
        }
    }
  }

  @Test
  fun `SHOULD track low earnings cashout period end ON trackLastLowEarningsCashoutPeriod WHEN we have no previous data`() {
    val userId = database.prepareUser()
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    runTest {
      service.trackLastLowEarningsCashoutPeriod(userId, now)
    }

    transaction(database) {
      UserLastLowEarningsCashoutPeriodTable
        .select { UserLastLowEarningsCashoutPeriodTable.userId eq userId }
        .first()
        .let { assertThat(it[UserLastLowEarningsCashoutPeriodTable.periodEnd]).isEqualTo(now) }
    }
  }

  @Test
  fun `SHOULD overwrite low earnings cashout period end ON trackLastLowEarningsCashoutPeriod WHEN we have previous data`() = runTest {
    val userId = database.prepareUser()
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    service.trackLastLowEarningsCashoutPeriod(userId, now.minusSeconds(10500))
    service.trackLastLowEarningsCashoutPeriod(userId, now)


    transaction(database) {
      UserLastLowEarningsCashoutPeriodTable
        .select { UserLastLowEarningsCashoutPeriodTable.userId eq userId }
        .first()
        .let { assertThat(it[UserLastLowEarningsCashoutPeriodTable.periodEnd]).isEqualTo(now) }
    }
  }

  @Test
  fun `SHOULD add earnings with a special meta id ON qaAddEarnings`() {
    val userId = database.prepareUser()
    transaction(database) { MetaUserEarningsTable.insertIgnore { it[id] = 1 } }

    runTest {
      service.qaAddEarnings(userId, amountUsd = BigDecimal.ONE, currencyCode = "CAD", amount = BigDecimal.TEN)
    }

    transaction(database) {
      UserEarningsTable.select { UserEarningsTable.userId eq userId }
        .first()
        .let {
          assertThat(it[userCurrencyEarningAmount]).isEqualByComparingTo(BigDecimal.TEN)
          assertThat(it[calculatedUserEarningUsd]).isEqualByComparingTo(BigDecimal.ONE)
          assertThat(it[userCurrencyCode]).isEqualTo("CAD")
          assertThat(it[UserEarningsTable.metaUserEarningsId]).isEqualTo(1)
        }
    }
  }

  @Test
  fun `SHOULD track revenue ON trackEarningsCalculationData`() {
    val metaId = insertMetaEarnings()

    runTest {
      service.trackEarningsCalculationData(metaId, BigDecimal.TEN, BigDecimal.ONE, BigDecimal.TEN)
    }

    transaction(database) {
      EarningsCalculationDataTable.select { EarningsCalculationDataTable.metaUserEarningsId eq metaId }
        .first()
        .let {
          assertThat(it[EarningsCalculationDataTable.revenue]).isEqualByComparingTo(BigDecimal.TEN)
          assertThat(it[EarningsCalculationDataTable.offerwallRevenue]).isEqualByComparingTo(BigDecimal.ONE)
          assertThat(it[EarningsCalculationDataTable.extraRevenue]).isEqualByComparingTo(BigDecimal.TEN)
          assertThat(it[EarningsCalculationDataTable.metaUserEarningsId]).isEqualTo(metaId)
        }
    }
  }

  @Test
  fun `SHOULD get revenue ON getCashoutRelatedRevenueSum`() {
    val userId = database.prepareUser()
    val metaId = insertMetaEarnings()
    val metaId2 = insertMetaEarnings()
    val transactionId = insertTransaction(userId)
    insertEarnings(userId = userId, usdEarnings = BigDecimal.TEN, metaId = metaId, transactionId = transactionId)
    insertEarnings(userId = userId, usdEarnings = BigDecimal.TEN, metaId = metaId2, transactionId = transactionId)

    transaction(database) {
      EarningsCalculationDataTable.insert {
        it[metaUserEarningsId] = metaId
        it[revenue] = BigDecimal("11.0")
        it[this.offerwallRevenue] = BigDecimal.ONE
      }

      EarningsCalculationDataTable.insert {
        it[metaUserEarningsId] = metaId2
        it[revenue] = BigDecimal("11.00")
        it[this.offerwallRevenue] = BigDecimal.TEN
      }
    }

    runTest {
      val result = service.getCashoutRelatedRevenueSum(transactionId)
      assertThat(result).isEqualByComparingTo(BigDecimal("22.0"))
    }

  }

  @Test
  fun `SHOULD load revenue with offerwall amount ON checkUnpaidUserEarningsOnlyFromOfferwall when user has earnings`() {
    val userId = database.prepareUser()
    val metaId = insertMetaEarnings()
    val metaId2 = insertMetaEarnings()
    insertEarnings(userId = userId, usdEarnings = BigDecimal("11.0"), metaId = metaId)
    insertEarnings(userId = userId, usdEarnings = BigDecimal("12.00"), metaId = metaId2)

    transaction(database) {
      EarningsCalculationDataTable.insert {
        it[metaUserEarningsId] = metaId
        it[revenue] = BigDecimal("11.0")
        it[this.offerwallRevenue] = BigDecimal("5.0")
      }

      EarningsCalculationDataTable.insert {
        it[metaUserEarningsId] = metaId2
        it[revenue] = BigDecimal("12.00")
        it[this.offerwallRevenue] = BigDecimal("0.0")
      }
    }

    runTest {
      val result = service.loadUnpaidEarningsWithOfferwallAmount(userId)
      assertNotNull(result)
      assertThat(result.totalRevenue).isEqualByComparingTo(BigDecimal("23.0"))
      assertThat(result.offerwallRevenue).isEqualByComparingTo(BigDecimal("5.0"))
    }
  }

  @Test
  fun `SHOULD not load revenue with offerwall amount ON checkUnpaidUserEarningsOnlyFromOfferwall when user has no earnings`() {
    val userId = database.prepareUser()

    runTest {
      val result = service.loadUnpaidEarningsWithOfferwallAmount(userId)
      assertThat(result).isEqualTo(UserEarningsWithOfferwallAmount(BigDecimal.ZERO, BigDecimal.ZERO))
    }
  }

  @Test
  fun `SHOULD return ZERO ON getRevenueSumForUserForTimeInterval WHEN user has no revenue`() {
    val userId = database.prepareUser()

    runTest {
      val result = service.getRevenueSumForUserForTimeInterval(userId, now.minus(24, ChronoUnit.HOURS), now)
      assertThat(result).isEqualTo(BigDecimal.ZERO)
    }
  }

  @Test
  fun `SHOULD sum only revenue from given interval ON getRevenueSumForUserForTimeInterval WHEN user has revenue`() = runTest {
    val userId1 = database.prepareUser()
    val gameId1 = database.prepareGame()
    val revenueEventId1 = UUID.randomUUID().toString()
    val revenueEventId2 = UUID.randomUUID().toString()
    val revenueEventId3 = UUID.randomUUID().toString()

    transaction(database) {
      val previousMetaId = MetaUserEarningsTable.insert { } get MetaUserEarningsTable.id

      CurrentGenericRevenueTable.insert {
        it[id] = revenueEventId1
        it[userId] = userId1
        it[revenueSource] = RevenueSource.APPLOVIN.name
        it[timestamp] = now.minus(3, ChronoUnit.HOURS)
        it[revenueAmount] = BigDecimal("1.00")
        it[metaUserEarningsId] = previousMetaId
        it[networkId] = -1
      }

      CurrentGenericRevenueTable.insert {
        it[id] = revenueEventId2
        it[userId] = userId1
        it[revenueSource] = RevenueSource.IRON_SOURCE.name
        it[timestamp] = now.minus(2, ChronoUnit.HOURS)
        it[revenueAmount] = BigDecimal("2.00")
        it[networkId] = -1
        it[gameId] = gameId1
      }

      CurrentGenericRevenueTable.insert {
        it[id] = revenueEventId3
        it[userId] = userId1
        it[revenueSource] = RevenueSource.APPLOVIN.name
        it[timestamp] = now.minus(26, ChronoUnit.HOURS)
        it[revenueAmount] = BigDecimal("3.00")
        it[networkId] = -1
      }
    }

    val result = service.getRevenueSumForUserForTimeInterval(userId1, now.minus(24, ChronoUnit.HOURS), now)
    assertThat(result.stripTrailingZeros()).isEqualTo(BigDecimal("3.00").stripTrailingZeros())
  }

  @ParameterizedTest
  @ValueSource(strings = ["insert", "update"])
  fun `SHOULD track fresh unpaid earnings ON trackUnpaidUserEarnings`(option: String) {
    val userId = database.prepareUser()

    runBlocking {
      if (option == "update")
        service.trackUnpaidUserEarnings(userId = userId, someEarningsStub.copy(amountUsd = BigDecimal.TEN, userCurrency = CAD))
      service.trackUnpaidUserEarnings(userId = userId, someEarningsStub)
    }

    transaction(database) {
      UserUnpaidEarningsTable
        .select { UserUnpaidEarningsTable.userId eq userId }
        .first()
        .let {
          assertThat(it[UserUnpaidEarningsTable.amount]).isEqualByComparingTo(BigDecimal.ONE)
          assertThat(it[UserUnpaidEarningsTable.amountUsd]).isEqualByComparingTo(BigDecimal.ONE)
          assertThat(it[UserUnpaidEarningsTable.currencyCode]).isEqualByComparingTo("USD")
        }
    }
  }

  @Test
  fun `SHOULD return 0 ON getLastUnpaidUserEarningsUSD WHEN there is no tracked values yet`() {
    val userId = database.prepareUser()

    runBlocking {
      service.getUnpaidUserEarningsUSD(userId)
    }.let { assertThat(it).isEqualTo(BigDecimal.ZERO) }
  }

  @Test
  fun `SHOULD return some usd amount ON getLastUnpaidUserEarningsUSD WHEN we have tracked something`() {
    val userId = database.prepareUser()

    runBlocking {
      service.trackUnpaidUserEarnings(userId = userId, someEarningsStub.copy(amountUsd = BigDecimal("12.134")))
      service.getUnpaidUserEarningsUSD(userId)
    }.let { assertThat(it).isEqualByComparingTo(BigDecimal("12.134")) }
  }

  @ParameterizedTest
  @ValueSource(strings = ["APPLOVIN", "FYBER"])
  fun `SHOULD insert daily revenue totals ON trackGenericRevenueDailyTotals WHEN it is the first revenue of that type`(source: RevenueSource) {
    val userId = database.prepareUser()

    runBlocking {
      service.trackGenericRevenueDailyTotals(userId, source, now, BigDecimal("0.0037"))
    }

    transaction(database) {
      GenericRevenueDailyTotalsMk2Table
        .select { GenericRevenueDailyTotalsMk2Table.userId eq userId }
        .map {
          assertThat(it[GenericRevenueDailyTotalsMk2Table.day]).isEqualTo(now.localUtcDate())
          assertThat(it[GenericRevenueDailyTotalsMk2Table.revenueSource]).isEqualTo(source.name)
          assertThat(it[GenericRevenueDailyTotalsMk2Table.revenueAmount]).isEqualByComparingTo(BigDecimal("0.0037"))
        }.size.let { assertThat(it).isEqualTo(1) }
    }
  }

  @Test
  fun `SHOULD incrementally update daily revenue totals ON trackGenericRevenueDailyTotals`() {
    val userId = database.prepareUser()

    runBlocking {
      service.trackGenericRevenueDailyTotals(userId, RevenueSource.APPLOVIN, now, BigDecimal("0.0037"))
      service.trackGenericRevenueDailyTotals(userId, RevenueSource.APPLOVIN, now, BigDecimal("0.0037"))
      service.trackGenericRevenueDailyTotals(userId, RevenueSource.APPLOVIN, now, BigDecimal("0.0037"))
      service.trackGenericRevenueDailyTotals(userId, RevenueSource.APPLOVIN, now, BigDecimal("0.0037"))
    }

    transaction(database) {
      GenericRevenueDailyTotalsMk2Table
        .select { GenericRevenueDailyTotalsMk2Table.userId eq userId }
        .map {
          assertThat(it[GenericRevenueDailyTotalsMk2Table.day]).isEqualTo(now.localUtcDate())
          assertThat(it[GenericRevenueDailyTotalsMk2Table.revenueSource]).isEqualTo("APPLOVIN")
          assertThat(it[GenericRevenueDailyTotalsMk2Table.revenueAmount]).isEqualByComparingTo(BigDecimal("0.0148"))
        }.size.let { assertThat(it).isEqualTo(1) }
    }
  }

  @Test
  fun `SHOULD add reward ON addChallengeEventReward`() {
    val userId = database.prepareUser()
    val eventId = UUID.randomUUID().toString()
    val currencyCad = Currency.getInstance("CAD")

    insertChallengeEvent(id = eventId)
    insertUserChallengeEvent(userId, eventId)

    runBlocking { service.addChallengeEventReward(userId, eventId, BigDecimal("2.741"), currencyCad, BigDecimal("3.1415")) }

    transaction(database) {
      UserChallengeEventRewardedTable
        .select { (UserChallengeEventRewardedTable.userId eq userId) and (UserChallengeEventRewardedTable.eventId eq eventId) }
        .count()
        .let { result ->
          assertThat(result).isEqualTo(1)
        }

      UserEarningsTable
        .select { UserEarningsTable.userId eq userId }
        .single()
        .let {
          assertThat(it[UserEarningsTable.metaUserEarningsId]).isEqualTo(-2)
          assertThat(it[calculatedUserEarningUsd]).isEqualByComparingTo(BigDecimal("2.741000"))
          assertThat(it[userCurrencyCode]).isEqualTo("CAD")
          assertThat(it[userCurrencyEarningAmount]).isEqualByComparingTo(BigDecimal("3.141500"))
        }
    }
  }

  @Test
  fun `SHOULD NOT add reward ON addChallengeEventReward WHEN reward is already tracked`() {
    val userId = database.prepareUser()
    val eventId = UUID.randomUUID().toString()
    val currencyCad = Currency.getInstance("CAD")

    insertChallengeEvent(id = eventId)
    insertUserChallengeEvent(userId, eventId)

    transaction(database) {
      UserChallengeEventRewardedTable.insert {
        it[UserChallengeEventRewardedTable.userId] = userId
        it[UserChallengeEventRewardedTable.eventId] = eventId
      }
    }

    runBlocking { service.addChallengeEventReward(userId, eventId, BigDecimal("3.1415"), currencyCad, BigDecimal("2.741")) }

    transaction(database) {
      UserEarningsTable
        .select { UserEarningsTable.userId eq userId }
        .count()
        .let { result ->
          assertThat(result).isEqualTo(0)
        }
    }
  }

  @Test
  fun `SHOULD add reward ON addSpecialChallengeReward`() {
    val userId = database.prepareUser()
    val currencyCad = Currency.getInstance("CAD")

    val potId = createPotConfig("POT_2")
    val userPotId = createUserPot(potId, userId)

    runBlocking { service.addSpecialChallengeReward(userPotId, userId, BigDecimal("2.741"), currencyCad, BigDecimal("3.1415")) }

    transaction(database) {
      UserSpecialChallengeRewardedTable
        .select { (UserSpecialChallengeRewardedTable.userSpecialPotId eq userPotId) }
        .count()
        .let { result ->
          assertThat(result).isEqualTo(1)
        }

      UserEarningsTable
        .select { UserEarningsTable.userId eq userId }
        .single()
        .let {
          assertThat(it[UserEarningsTable.metaUserEarningsId]).isEqualTo(-2)
          assertThat(it[calculatedUserEarningUsd]).isEqualByComparingTo(BigDecimal("2.741000"))
          assertThat(it[userCurrencyCode]).isEqualTo("CAD")
          assertThat(it[userCurrencyEarningAmount]).isEqualByComparingTo(BigDecimal("3.141500"))
        }
    }
  }

  @Test
  fun `SHOULD NOT add reward ON addChallengeSpecialReward WHEN reward is already tracked`() {
    val userId = database.prepareUser()
    val currencyCad = Currency.getInstance("CAD")

    val potId = createPotConfig("POT_1")
    val userPotId = createUserPot(potId, userId)

    transaction(database) {
      UserSpecialChallengeRewardedTable.insert {
        it[UserSpecialChallengeRewardedTable.userSpecialPotId] = userPotId
      }
    }

    runBlocking { service.addSpecialChallengeReward(userPotId, userId, BigDecimal("3.1415"), currencyCad, BigDecimal("2.741")) }

    transaction(database) {
      UserEarningsTable
        .select { UserEarningsTable.userId eq userId }
        .count()
        .let { result ->
          assertThat(result).isEqualTo(0)
        }
    }
  }

  @Test
  fun `SHOULD get last low earnings cashout period end ON getLastLowEarningsCashoutPeriodEnd WHEN a user has a low earnings cashout period tracked`() {
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    val userId = database.prepareUser(googleAdId = "google123", createdAt = now)
    database.trackLowEarningsCashoutPeriod(userId, now)

    runBlocking {
      service.getLastLowEarningsCashoutPeriodEnd(userId)
    }.let { assertThat(it).isEqualTo(now) }
  }

  @Test
  fun `SHOULD get null ON getLastLowEarningsCashoutPeriodEnd WHEN a user has no low earnings cashout period tracked`() {
    val userId = database.prepareUser()
    runBlocking {
      service.getLastLowEarningsCashoutPeriodEnd(userId)
    }.let { assertThat(it).isNull() }
  }

  @Test
  fun `SHOULD get all applovin revenue grouped by hours for a user ON getUserNonBannerApplovinRevenueByPeriods`() {
    val userId = database.prepareUser()
    val userId2 = database.prepareUser()
    val gameId = database.prepareGame()
    val gameId2 = database.prepareGame()
    val sinceDate = now.minus(3, ChronoUnit.HOURS).truncatedTo(ChronoUnit.HOURS)
    database.addRevenueByPeriods(userId, gameId, sinceDate.minusSeconds(60)) // out of range
    database.addRevenueByPeriods(userId, gameId, sinceDate.plusSeconds(60)) // in range game1, h1
    database.addRevenueByPeriods(userId, gameId2, sinceDate.plusSeconds(120)) // in range game2. h1
    database.addRevenueByPeriods(userId, gameId, sinceDate.plus(1, ChronoUnit.HOURS).plusSeconds(15)) // in range game1, h2
    database.addRevenueByPeriods(userId, gameId, sinceDate.plus(1, ChronoUnit.HOURS).plusSeconds(1115)) // in range game1, h2
    database.addRevenueByPeriods(userId, gameId, sinceDate.plus(1, ChronoUnit.HOURS).plusSeconds(2115)) // in range game1, h2
    database.addRevenueByPeriods(userId, gameId, sinceDate.plus(2, ChronoUnit.HOURS)) // in range game1, h3
    database.addRevenueByPeriods(userId2, gameId, sinceDate.plusSeconds(60)) // other user
    database.addRevenueByPeriods(userId2, gameId, sinceDate.minusSeconds(60)) // another user + out of range
    val expected = listOf(
      RevenueByPeriod(sinceDate, 2),
      RevenueByPeriod(sinceDate.plus(1, ChronoUnit.HOURS), 3),
      RevenueByPeriod(sinceDate.plus(2, ChronoUnit.HOURS), 1),
    )

    runBlocking { service.getUserNonBannerApplovinRevenueTransactionsCountByHours(userId, sinceDate) }
      .let { assertThat(it).isEqualTo(expected) }
  }

  @Test
  fun `SHOULD get all applovin revenue 5-min-intervals for user by games ON get5MinIntervalsWithRevenueByGames`() {
    val userId = database.prepareUser()
    val userId2 = database.prepareUser()
    val gameId = database.prepareGame()
    val gameId2 = database.prepareGame()
    val sinceDate = now.minus(3, ChronoUnit.HOURS).truncatedTo(ChronoUnit.HOURS)
    database.addRevenueByPeriods(userId, gameId, sinceDate.minusSeconds(60)) // out of range
    database.addRevenueByPeriods(userId, gameId, sinceDate.plusSeconds(60)) // in range game1
    database.addRevenueByPeriods(userId, gameId2, sinceDate.plusSeconds(120)) // in range game2
    database.addRevenueByPeriods(userId, gameId, sinceDate.plus(1, ChronoUnit.HOURS).plusSeconds(2115)) // in range game1, h2
    database.addRevenueByPeriods(userId, gameId, sinceDate.plus(1, ChronoUnit.HOURS).plusSeconds(15)) // in range game1, h2
    database.addRevenueByPeriods(userId, gameId, sinceDate.plus(1, ChronoUnit.HOURS).plusSeconds(1115)) // in range game1, h2
    database.addRevenueByPeriods(userId, gameId, sinceDate.plus(2, ChronoUnit.HOURS)) // in range game1, h3
    database.addRevenueByPeriods(userId2, gameId, sinceDate.plusSeconds(60)) // other user
    database.addRevenueByPeriods(userId2, gameId, sinceDate.minusSeconds(60)) // another user + out of range
    val expected = mapOf(
      gameId to listOf(
        sinceDate.plusSeconds(60),
        sinceDate.plus(1, ChronoUnit.HOURS).plusSeconds(15),
        sinceDate.plus(1, ChronoUnit.HOURS).plusSeconds(1115),
        sinceDate.plus(1, ChronoUnit.HOURS).plusSeconds(2115),
        sinceDate.plus(2, ChronoUnit.HOURS)
      ),
      gameId2 to listOf(
        sinceDate.plusSeconds(120)
      )
    )

    runBlocking { service.get5MinIntervalsWithRevenueByGames(userId, sinceDate) }
      .let { assertThat(it).isEqualTo(expected) }
  }

  @ParameterizedTest
  @ValueSource(strings = ["UserApplovinRevenueByPeriodsTable"])
  fun `SHOULD remove obsolete data from coins by periods or revenue by periods table`(option: String) {
    val (table, testingMethod) = when (option) {
      "UserApplovinRevenueByPeriodsTable" -> UserApplovinRevenueByPeriodsTable to service::removeBatchOfRevenueTrackedByPeriods
      else -> throw AssertionError("unexpected variation of test")
    }
    transaction(database) {
      table.deleteAll()
    }
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    timeService.mock({ now() }, now)

    val addRowMethod = when (option) {
      "UserApplovinRevenueByPeriodsTable" -> Database::addRevenueByPeriods
      else -> throw AssertionError("unexpected variation of test")
    }
    val obsoletePeriod = now.minus(20, ChronoUnit.DAYS)
    val actualPeriod = now.minus(14, ChronoUnit.DAYS)
    val user1 = database.prepareUser("f" + UUID.randomUUID().toString().takeLast(35))
    val user2 = database.prepareUser("a" + UUID.randomUUID().toString().takeLast(35))
    val games = (1..5).map { database.prepareGame() }
    listOf(user1, user2).forEach { user ->
      games.forEach { gameId ->
        addRowMethod.invoke(database, user, gameId, obsoletePeriod)
        addRowMethod.invoke(database, user, gameId, actualPeriod)
      }
    }

    runBlocking {
      testingMethod.invoke(3, "f")
    }
    @Suppress("UNCHECKED_CAST")
    val userIdColumn = table.columns.first { it.name == "user_id" } as Column<String>

    transaction(database) {
      table
        .select { userIdColumn.eq(user1) }
        .count()
        .let { assertThat(it).isEqualTo(7) }
      table
        .select { userIdColumn eq user2 }
        .count()
        .let { assertThat(it).isEqualTo(10) }
    }

    runBlocking {
      testingMethod.invoke(3, "f")
      testingMethod.invoke(3, "a")
      testingMethod.invoke(3, "a")
    }

    @Suppress("UNCHECKED_CAST")
    val periodStartColumn = table.columns.first { it.name == "period_start" } as Column<Instant>
    val counterColumn = when (option) {
      "UserApplovinRevenueByPeriodsTable" -> UserApplovinRevenueByPeriodsTable.revenueTransactionsCount
      else -> throw AssertionError("unexpected variation of test")
    }

    transaction(database) {
      table
        .slice(counterColumn, periodStartColumn)
        .selectAll()
        .map {
          assertThat(it[counterColumn]).isEqualTo(1)
          assertThat(it[periodStartColumn]).isEqualTo(actualPeriod)
        }
        .count().let { assertThat(it).isEqualTo(10) }
    }
  }

  @Test
  fun `SHOULD round down amount ON UserCurrencyEarnings_roundDownToSecondDigit`() {
    val expected = UserCurrencyEarnings(
      amountUsd = BigDecimal("1.23"),
      userCurrency = CAD,
      userCurrencyAmount = BigDecimal("3.45"),
      nonBoostedAmountUsd = BigDecimal("4.56"),
      nonBoostedUserCurrencyAmount = BigDecimal("5.67"),
    )

    val earnings = UserCurrencyEarnings(
      amountUsd = BigDecimal("1.23456"),
      userCurrency = CAD,
      userCurrencyAmount = BigDecimal("3.45678"),
      nonBoostedAmountUsd = BigDecimal("4.56789"),
      nonBoostedUserCurrencyAmount = BigDecimal("5.67891"),
    )

    val actual = earnings.roundDownToSecondDigit()

    assertThat(actual).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return zero earnings ON UserCurrencyEarnings_zeroEarnings`() {
    val expected = UserCurrencyEarnings(
      amountUsd = BigDecimal.ZERO,
      nonBoostedAmountUsd = null,
      userCurrency = CAD,
      userCurrencyAmount = BigDecimal.ZERO,
      nonBoostedUserCurrencyAmount = null,
    )

    val actual = UserCurrencyEarnings.zeroEarnings(CAD)

    assertThat(actual).isEqualTo(expected)
  }

  private fun insertCurrentCoinsBalance(userId: String, @Suppress("SameParameterValue") coinBalance: Int) =
    transaction(database) {
      UserCoinGoalCurrentCoinsBalanceTable.insert {
        it[UserCoinGoalCurrentCoinsBalanceTable.userId] = userId
        it[coins] = coinBalance
        it[goalCoins] = coinBalance
      }
    }

  private fun insertCurrentCoinsBalanceEm2(userId: String, @Suppress("SameParameterValue") coinBalance: Int) =
    transaction(database) {
      UserCoinGoalCurrentCoinsBalanceEm2Table.insert {
        it[UserCoinGoalCurrentCoinsBalanceEm2Table.userId] = userId
        it[gameCoins] = coinBalance.toBigDecimal()
        it[bonusCoins] = coinBalance.toBigDecimal()
        it[offerCoins] = coinBalance.toBigDecimal()
        it[goalCoins] = coinBalance.toBigDecimal()
      }
    }

  private fun insertEarnings(
    userId: String,
    usdEarnings: BigDecimal = BigDecimal.ONE,
    userCurrencyCode: String = "USD",
    userCurrencyEarnings: BigDecimal = BigDecimal.ONE,
    metaId: Int? = null,
    transactionId: String? = null,
    createdAt: Instant? = null,
    id: Int? = null,
    nonBoostedUsdEarnings: BigDecimal? = null,
    nonBoostedUserCurrencyEarnings: BigDecimal? = null,
  ) =
    transaction(database) {
      UserEarningsTable.insert {
        if (id != null)
          it[UserEarningsTable.id] = id
        it[UserEarningsTable.userId] = userId
        it[calculatedUserEarningUsd] = usdEarnings
        it[nonBoostedUserEarningUsd] = nonBoostedUsdEarnings
        it[UserEarningsTable.userCurrencyCode] = userCurrencyCode
        it[userCurrencyEarningAmount] = userCurrencyEarnings
        it[nonBoostedCurrencyEarningAmount] = nonBoostedUserCurrencyEarnings
        it[metaUserEarningsId] = metaId
        it[cashoutTransactionId] = transactionId
        if (createdAt != null)
          it[UserEarningsTable.createdAt] = createdAt
      }
    }

  private fun insertTransaction(userId: String): String {
    val transactionId = UUID.randomUUID().toString()
    transaction(database) {
      UserCashoutTransactionsTable.insert {
        it[id] = transactionId
        it[UserCashoutTransactionsTable.userId] = userId
        it[type] = UserCashoutTransactionsTable.Type.EARNINGS.key
        it[status] = UserCashoutTransactionsTable.Status.INITIAL.key
        it[provider] = "provider"
        it[countryCode] = "US"
        it[userIp] = "127.0.0.1"
      }
    }
    return transactionId
  }

  private fun insertMetaEarnings(): Int =
    transaction(database) {
      MetaUserEarningsTable.insert { } get MetaUserEarningsTable.id
    }

  private fun insertChallengeEvent(
    id: String,
    dateFrom: Instant = Instant.now(),
    dateTo: Instant = Instant.now(),
    enabled: Boolean = true,
    cfg: String = "{}"
  ) = transaction(database) {
    ChallengeEventTable.insert {
      it[ChallengeEventTable.id] = id
      it[ChallengeEventTable.dateFrom] = dateFrom
      it[ChallengeEventTable.dateTo] = dateTo
      it[ChallengeEventTable.enabled] = enabled
      it[ChallengeEventTable.cfg] = cfg
      it[eventType] = ChallengeEventType.GLOBAL
    }
  }

  private fun insertUserChallengeEvent(
    userId: String,
    eventId: String,
    state: ChallengeEventState = ChallengeEventState.COMPLETED,
    earnings: BigDecimal = BigDecimal.ZERO,
  ) = transaction(database) {
    UserChallengeEventTable.insert {
      it[UserChallengeEventTable.userId] = userId
      it[UserChallengeEventTable.eventId] = eventId
      it[UserChallengeEventTable.state] = state
      it[UserChallengeEventTable.earnings] = earnings
    }
  }

  private fun createUserPot(
    potId: Int,
    userId: String,
  ): String {
    val id = UUID.randomUUID()
    transaction(database) {
      UserSpecialChallengePotTable.insert {
        it[UserSpecialChallengePotTable.id] = id.toString()
        it[UserSpecialChallengePotTable.userId] = userId
        it[UserSpecialChallengePotTable.counter] = 0
        it[UserSpecialChallengePotTable.potId] = potId
        it[UserSpecialChallengePotTable.state] = SpecialChallengePotState.COMPLETED
        it[UserSpecialChallengePotTable.applovinNonBannerRev] = BigDecimal.ZERO
        it[UserSpecialChallengePotTable.earnings] = BigDecimal.ZERO
        it[UserSpecialChallengePotTable.progress] = 100
      }
    }
    return id.toString()
  }

  private fun createPotConfig(
    key: String,
  ) =
    transaction(database) {
      SpecialChallengePotTable.insert {
        it[SpecialChallengePotTable.potKey] = key
        it[SpecialChallengePotTable.uiConfig] = "{}"
        it[SpecialChallengePotTable.enabled] = true
        it[SpecialChallengePotTable.progressMax] = 100
      }[SpecialChallengePotTable.id]
    }

  private fun insertGames(): Int = database.prepareGame(gameStub)
}
