package com.moregames.playtime.earnings

import assertk.assertThat
import assertk.assertions.*
import com.google.protobuf.Int32Value
import com.justplayapps.base.Common
import com.justplayapps.playtime.rewarding.proto.getUserDataResponse
import com.justplayapps.service.rewarding.earnings.*
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService.Companion.MAX_CHUNK_SIZE
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService.UserCoinsBasedEarningsData
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService.UserEarningsWithOfferwallAmount
import com.justplayapps.service.rewarding.earnings.UserEarningsService.Companion.FILTER_VALUE_ALL
import com.justplayapps.service.rewarding.earnings.UserEarningsService.Companion.MAX_REVENUE_ROWS_TO_DELETE_PER_CRON_RUN
import com.justplayapps.service.rewarding.earnings.dto.Earnings
import com.justplayapps.service.rewarding.earnings.dto.EarningsCalculationResult
import com.justplayapps.service.rewarding.earnings.dto.UserCurrencyEarnings
import com.justplayapps.service.rewarding.earnings.proto.createUserRevenueMessagesMessage
import com.justplayapps.service.rewarding.facade.AbTestingFacade
import com.justplayapps.service.rewarding.facade.PlaytimeFacade
import com.justplayapps.service.rewarding.revenue.RevenuePersistenceService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.Variations
import com.moregames.base.applovin.APPLOVIN_AD_UNIT_FORMAT_BANNER
import com.moregames.base.applovin.APPLOVIN_AD_UNIT_FORMAT_INTER
import com.moregames.base.bus.MessageBus
import com.moregames.base.coins.UserCurrentCoinsGoalBalance
import com.moregames.base.junit.MockExtension
import com.moregames.base.messaging.dto.EarningsAddedEventDto
import com.moregames.base.messaging.dto.RevenueReceivedEventDto
import com.moregames.base.util.*
import com.moregames.playtime.earnings.currency.dto.CurrencyExchangeResultDto
import com.moregames.playtime.earnings.dto.GenericRevenueDto
import com.moregames.playtime.earnings.dto.UsedQuota
import com.moregames.playtime.earnings.dto.UserEarningsQuotasDto
import com.moregames.playtime.user.cashout.dto.CashoutPeriodDto
import com.moregames.playtime.utils.USD
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Instant
import java.util.*

@OptIn(ExperimentalCoroutinesApi::class)
@ExtendWith(MockExtension::class)
class UserEarningsServiceTest(
  private val revenuePersistenceService: RevenuePersistenceService,
  private val playtimeFacade: PlaytimeFacade,
  private val earningsCalculationsService: EarningsCalculationsService,
  private val userEarningsPersistenceService: UserEarningsPersistenceService,
  private val userCurrentCoinsBalanceService: UserCurrentCoinsBalanceService,
  private val timeService: TimeService,
  private val abTestingFacade: AbTestingFacade,
  private val messageBus: MessageBus,
) {

  private val service = UserEarningsService(
    revenuePersistenceService = revenuePersistenceService,
    earningsCalculationsService = earningsCalculationsService,
    userEarningsPersistenceService = userEarningsPersistenceService,
    userCurrentCoinsBalanceService = userCurrentCoinsBalanceService,
    timeService = timeService,
    abTestingFacade = abTestingFacade,
    messageBus = messageBus,
    playtimeFacade = playtimeFacade,
  )

  private companion object {
    const val USER_ID = "user1"
    const val META_ID = 123
    val now: Instant = Instant.now()

    val someEarningsStub = UserCurrencyEarnings(BigDecimal.ONE, USD, BigDecimal.ONE)

    val periodStart = now
    val periodEnd: Instant = now.plusSeconds(42)
    val sampleUserCoinsBasedEarningsData = UserCoinsBasedEarningsData(
      userId = USER_ID,
      coinsForOneDollar = BigDecimal("220.0"),
      periodStart = periodStart,
      periodEnd = periodEnd,
      userQuality = null,
      currentToFirstEcpmRatio = BigDecimal.ONE,
      noEarnings = false,
      coinGoal = 42,
      earningsAmount = BigDecimal("7.7004"),
      em2Coins = BigDecimal("1200.12"),
      em2GameCoins = BigDecimal("500.04"),
      em2BonusCoins = BigDecimal("400.04"),
      em2OfferCoins = BigDecimal("300.04"),
      realRevenue = BigDecimal("6.0004"),
      realGameRevenue = BigDecimal("2.0004"),
      em2Revenue = BigDecimal("3.5004"),
      em2Capped = BigDecimal("1.954690"),
      em2GameRevenue = BigDecimal("1.5004"),
      em2GameCapped = BigDecimal("0.772509"),
      em2Variation = Variations.EM2_BETA_01,
      usedQuota = BigDecimal("1.5000"),
    )
    val revenueReceivedEvent = RevenueReceivedEventDto(
      eventId = "eventId",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("0.0005123"),
      networkId = -1,
      gameId = 1,
      amountExtra = BigDecimal.ZERO,
      adUnitFormat = "banner",
      createdAt = Instant.now()
    )
  }

  @BeforeEach
  fun before() {
    Dispatchers.setMain(StandardTestDispatcher())
    earningsCalculationsService.answer(
      { convertRevenueToEarnings(any(), any(), any(), any()) },
      {
        val userId = it.arguments[0] as String
        val revenue = it.arguments[1] as BigDecimal
        Earnings(userId, revenue * BigDecimal("0.5"))
      }
    )
    whenever(timeService.now()).thenReturn(now)
    playtimeFacade.answer(
      { getConvertedUsdToUserCurrency(any(), eq(Currency.getInstance("USD"))) },
      { request ->
        CurrencyExchangeResultDto(
          request.getArgument(0) as BigDecimal,
          request.getArgument(1) as Currency,
          request.getArgument(0) as BigDecimal,
          request.getArgument(0) as BigDecimal
        )
      })
    userEarningsPersistenceService.mock(
      { addUserRevenue(any(), any(), any(), any(), any(), any(), any(), any()) }, true
    )
    playtimeFacade.answer(
      { revenueWithChallengesCut(any(), any()) },
      {
        (it.getArgument(1) as List<GenericRevenueDto>)
          .sumOf { rev -> rev.amount }
      }
    )
  }

  @Test
  fun `SHOULD save revenue ON saveRevenue`() {
    runBlocking {
      service.saveRevenue(revenueReceivedEvent)
    }.also { assertThat(it).isEqualTo(true) }

    verifyAddUserRevenueTriggered(revenueReceivedEvent)
    verifyBlocking(userEarningsPersistenceService) {
      trackGenericRevenueTotals(revenueReceivedEvent.userId, revenueReceivedEvent.source, revenueReceivedEvent.timestamp, revenueReceivedEvent.amount)
    }
    verifyBlocking(userEarningsPersistenceService) {
      trackGenericRevenueDailyTotals(
        userId = revenueReceivedEvent.userId,
        source = revenueReceivedEvent.source,
        timestamp = revenueReceivedEvent.timestamp,
        amount = revenueReceivedEvent.amount
      )
    }
    verifyNoMoreInteractions(userEarningsPersistenceService)
  }

  @Test
  fun `SHOULD track applovin non banner revenue ON saveRevenue`() {
    val event = revenueReceivedEvent.copy(adUnitFormat = APPLOVIN_AD_UNIT_FORMAT_INTER)

    runBlocking {
      service.saveRevenue(event)
    }.also { assertThat(it).isEqualTo(true) }

    with(event) {
      verifyBlocking(userEarningsPersistenceService) { trackApplovinNonBannerRevenueBy5min(userId, gameId!!, timestamp, amount) }
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["notApplovin", "nullAdUnitFormat", "blankAdUnitFormat", "banner", "noGameId", "normCase"])
  fun `SHOULD not track applovin non banner revenue ON saveRevenue WHEN condition does not match`(option: String) {
    var event = revenueReceivedEvent.copy(adUnitFormat = APPLOVIN_AD_UNIT_FORMAT_INTER)
    event = when (option) {
      "notApplovin" -> event.copy(source = RevenueReceivedEventDto.RevenueSource.ADJOE)
      "nullAdUnitFormat" -> event.copy(adUnitFormat = null)
      "blankAdUnitFormat" -> event.copy(adUnitFormat = "")
      "banner" -> event.copy(adUnitFormat = APPLOVIN_AD_UNIT_FORMAT_BANNER)
      "noGameId" -> event.copy(gameId = null)
        .also {
          userEarningsPersistenceService.mock(
            {
              addUserRevenue(event.eventId, event.userId, event.source, event.timestamp, event.amount, event.networkId!!, null, event.amountExtra)
            }, true
          )
        }

      else -> event
    }

    runBlocking {
      service.saveRevenue(event)
    }.also { assertThat(it).isEqualTo(true) }

    with(event) {
      if (option != "normCase") {
        verifyBlocking(userEarningsPersistenceService, never()) { trackApplovinNonBannerRevenueBy5min(any(), any(), any(), any()) }
      } else {
        verifyBlocking(userEarningsPersistenceService) { trackApplovinNonBannerRevenueBy5min(userId, gameId!!, timestamp, amount) }
      }
    }
  }

  @Test
  fun `SHOULD not save revenue once again ON saveRevenue WHEN event was already saved in db`() {
    userEarningsPersistenceService.mock(
      { addUserRevenue(any(), any(), any(), any(), any(), any(), any(), any()) }, false
    )

    runBlocking { service.saveRevenue(revenueReceivedEvent) }
      .also { assertThat(it).isEqualTo(false) }

    verifyAddUserRevenueTriggered(revenueReceivedEvent)
    verifyNoMoreInteractions(userEarningsPersistenceService)
  }

  @Test
  fun `SHOULD bind revenue AND do not send events ON giveEarningsToUser IF user is deleted`() {
    val revenue = GenericRevenueDto(
      id = "eventId",
      userId = USER_ID,
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("2.10"),
      amountExtra = BigDecimal("0.25"),
      gameId = null,
    )
    val metaId = 1

    userEarningsPersistenceService.mock(
      {
        bindRevenueWithEarnings(
          userId = USER_ID,
          revenueEventIds = listOf(revenue.id),
          calculatedUserEarningUsd = BigDecimal("1.15"),
          nonBoostedUserEarningUsd = null,
          userCurrency = Currency.getInstance("CAD"),
          userCurrencyEarningAmount = BigDecimal("1.05"),
          nonBoostedUserCurrencyEarningAmount = null,
        )
      },
      metaId
    )
    playtimeFacade.mock(
      { getConvertedUsdToUserCurrency(BigDecimal("1.15"), Currency.getInstance("CAD")) },
      CurrencyExchangeResultDto(BigDecimal("1.15"), Currency.getInstance("CAD"), BigDecimal("1.05"), BigDecimal("1.051"))
    )

    runTest {
      service.giveEarningsToUser(
        USER_ID,
        revenueList = listOf(revenue),
        earningsFromRevenue = Earnings(USER_ID, BigDecimal("0.85")),
        earningsWithAdditionsAmountUsd = BigDecimal("1.15"),
        nonBoostedEarningsWithAdditionsAmountUsd = null,
        userCurrency = Currency.getInstance("CAD"),
      )
        .let { actual ->
          assertThat(actual).isEqualTo(
            EarningsCalculationResult.Simple(
              metaId = metaId,
              amount = BigDecimal("1.15"),
              amountNoRounding = BigDecimal("1.051")
            )
          )
        }
    }

    verifyBlocking(userEarningsPersistenceService) {
      bindRevenueWithEarnings(
        userId = USER_ID,
        revenueEventIds = listOf(revenue.id),
        calculatedUserEarningUsd = BigDecimal("1.15"),
        nonBoostedUserEarningUsd = null,
        userCurrency = Currency.getInstance("CAD"),
        userCurrencyEarningAmount = BigDecimal("1.05"),
        nonBoostedUserCurrencyEarningAmount = null,
      )
    }
    verifyBlocking(userEarningsPersistenceService, never()) { saveUserQuotas(any()) }
    verifyBlocking(messageBus) {
      publish(
        EarningsAddedEventDto(
          metaId = metaId,
          userId = USER_ID,
          amount = BigDecimal("1.15"),
          createdAt = now
        ),
        now.plusSeconds(CashoutPeriodDto.PERIOD_END_SHIFT_SECONDS)
      )
    }
    verifyBlocking(userEarningsPersistenceService, never()) { trackLastLowEarningsCashoutPeriod(USER_ID, now) }
  }

  @Test
  fun `SHOULD bind revenue AND return zero earnings ON giveEarningsToUser WHEN currency amount is low`() = runTest {
    val revenue = GenericRevenueDto(
      id = "eventId",
      userId = USER_ID,
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("2.10"),
      amountExtra = BigDecimal("0.25"),
      gameId = null,
    )
    val metaId = 1

    userEarningsPersistenceService.mock(
      {
        bindRevenueWithEarnings(
          userId = USER_ID,
          revenueEventIds = listOf(revenue.id),
          calculatedUserEarningUsd = BigDecimal.ZERO,
          nonBoostedUserEarningUsd = null,
          userCurrency = Currency.getInstance("CAD"),
          userCurrencyEarningAmount = BigDecimal.ZERO,
          nonBoostedUserCurrencyEarningAmount = null,
        )
      },
      metaId
    )
    playtimeFacade.mock(
      { getConvertedUsdToUserCurrency(BigDecimal("1.15"), Currency.getInstance("CAD")) },
      CurrencyExchangeResultDto(BigDecimal("1.15"), Currency.getInstance("CAD"), BigDecimal.ZERO, BigDecimal("0.005"))
    )

    service.giveEarningsToUser(
      USER_ID,
      revenueList = listOf(revenue),
      earningsFromRevenue = Earnings(USER_ID, BigDecimal("0.85")),
      earningsWithAdditionsAmountUsd = BigDecimal("1.15"),
      nonBoostedEarningsWithAdditionsAmountUsd = null,
      userCurrency = Currency.getInstance("CAD"),
    ).let { actual ->
      assertThat(actual).isEqualTo(
        EarningsCalculationResult.Simple(
          metaId = metaId,
          amount = BigDecimal.ZERO,
          amountNoRounding = BigDecimal("0.005")
        )
      )
    }

    verifyBlocking(userEarningsPersistenceService) {
      bindRevenueWithEarnings(
        userId = USER_ID,
        revenueEventIds = listOf(revenue.id),
        calculatedUserEarningUsd = BigDecimal.ZERO,
        nonBoostedUserEarningUsd = null,
        userCurrency = Currency.getInstance("CAD"),
        userCurrencyEarningAmount = BigDecimal.ZERO,
        nonBoostedUserCurrencyEarningAmount = null,
      )
    }
    verifyBlocking(userEarningsPersistenceService, never()) { saveUserQuotas(any()) }
    verifyBlocking(userEarningsPersistenceService) { trackLastLowEarningsCashoutPeriod(USER_ID, now) }
    verifyBlocking(userEarningsPersistenceService) {
      trackEarningsCalculationData(metaId = metaId, revenue = BigDecimal("2.10"), offerwallRevenue = BigDecimal.ZERO, extraRevenue = BigDecimal("0.25"))
    }
    verifyNoMoreInteractions(userEarningsPersistenceService)
    verifyNoInteractions(messageBus)
    verifyNoMoreInteractions(userEarningsPersistenceService)
  }

  @Test
  fun `SHOULD save quotas ON giveEarningsToUser WHEN quotas defined`() = runTest {
    val revenue = GenericRevenueDto(
      id = "eventId",
      userId = USER_ID,
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("2.10"),
      amountExtra = BigDecimal("0.25"),
      gameId = null,
    )
    val metaId = 1
    val userQuotas = UserEarningsQuotasDto(
      userId = USER_ID,
      quotas = listOf("quotas"),
      periodEnd = now.plusSeconds(42),
      values = listOf(BigDecimal.ONE)
    )

    userEarningsPersistenceService.mock(
      {
        bindRevenueWithEarnings(
          userId = USER_ID,
          revenueEventIds = listOf(revenue.id),
          calculatedUserEarningUsd = BigDecimal("1.15"),
          nonBoostedUserEarningUsd = null,
          userCurrency = Currency.getInstance("CAD"),
          userCurrencyEarningAmount = BigDecimal("1.05"),
          nonBoostedUserCurrencyEarningAmount = null,
        )
      },
      metaId
    )
    playtimeFacade.mock(
      { getConvertedUsdToUserCurrency(BigDecimal("1.15"), Currency.getInstance("CAD")) },
      CurrencyExchangeResultDto(BigDecimal("1.15"), Currency.getInstance("CAD"), BigDecimal("1.05"), BigDecimal("1.051"))
    )

    service.giveEarningsToUser(
      USER_ID,
      revenueList = listOf(revenue),
      earningsFromRevenue = Earnings(
        USER_ID,
        earningsSum = BigDecimal("0.85"),
        quotasDto = userQuotas
      ),
      earningsWithAdditionsAmountUsd = BigDecimal("1.15"),
      nonBoostedEarningsWithAdditionsAmountUsd = null,
      userCurrency = Currency.getInstance("CAD"),
    )
      .let { actual ->
        assertThat(actual).isEqualTo(
          EarningsCalculationResult.Simple(
            metaId = metaId,
            amount = BigDecimal("1.15"),
            amountNoRounding = BigDecimal("1.051")
          )
        )
      }

    verifyBlocking(userEarningsPersistenceService) { saveUserQuotas(userQuotas) }
  }

  @Test
  fun `SHOULD convert revenue to earnings with specific currency and send earnings event ON convertRevenueToEarnings`() {
    val revenue = GenericRevenueDto(
      id = "eventId",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("2.00"),
      amountExtra = null,
      gameId = null,
    )
    val metaId = 1
    val userData = getUserDataResponse {
      userCurrency = "CAD"
      platform = Common.AppPlatformProto.ANDROID
    }
    userEarningsPersistenceService.mock({ getUnconvertedRevenue(USER_ID) }, listOf(revenue))
    earningsCalculationsService.mock({
      convertRevenueToEarnings(USER_ID, BigDecimal("2.00"), BigDecimal.ZERO, userData)
    }, Earnings(USER_ID, BigDecimal("0.85")))
    playtimeFacade.mock(
      { getConvertedUsdToUserCurrency(BigDecimal("0.85"), Currency.getInstance("CAD")) },
      CurrencyExchangeResultDto(BigDecimal("0.85"), Currency.getInstance("CAD"), BigDecimal("1.05"), BigDecimal("1.05"))
    )
    userEarningsPersistenceService.mock({
      bindRevenueWithEarnings(
        userId = USER_ID,
        revenueEventIds = listOf(revenue.id),
        calculatedUserEarningUsd = BigDecimal("0.85"),
        nonBoostedUserEarningUsd = null,
        userCurrency = Currency.getInstance("CAD"),
        userCurrencyEarningAmount = BigDecimal("1.05"),
        nonBoostedUserCurrencyEarningAmount = null,
      )
    }, metaId)
    userCurrentCoinsBalanceService.mock({ getUserCurrentCoinsBalance(USER_ID, userData.platform.fromProto()) }, UserCurrentCoinsGoalBalance(0, 0, 20))

    runTest {
      service.convertRevenueToEarnings(USER_ID, userData, periodStart)
    }

    verifyBlocking(messageBus) {
      publish(
        EarningsAddedEventDto(
          metaId = metaId,
          userId = USER_ID,
          amount = BigDecimal("0.85"),
          createdAt = now
        ),
        now.plusSeconds(CashoutPeriodDto.PERIOD_END_SHIFT_SECONDS)
      )
    }
    verifyBlocking(userEarningsPersistenceService) {
      bindRevenueWithEarnings(
        userId = USER_ID,
        revenueEventIds = listOf(revenue.id),
        calculatedUserEarningUsd = BigDecimal("0.85"),
        nonBoostedUserEarningUsd = null,
        userCurrency = Currency.getInstance("CAD"),
        userCurrencyEarningAmount = BigDecimal("1.05"),
        nonBoostedUserCurrencyEarningAmount = null,
      )
    }
  }

  @Test
  fun `SHOULD convert revenue to earnings with non-boosted ON convertRevenueToEarnings WHEN boosted earnings defined`() {
    val revenue = GenericRevenueDto(
      id = "eventId",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("2.00"),
      amountExtra = null,
      gameId = null,
    )
    val metaId = 1
    val userData = getUserDataResponse {
      userCurrency = "CAD"
      platform = Common.AppPlatformProto.ANDROID
    }
    userEarningsPersistenceService.mock({ getUnconvertedRevenue(USER_ID) }, listOf(revenue))
    earningsCalculationsService.mock({
      convertRevenueToEarnings(USER_ID, BigDecimal("2.00"), BigDecimal.ZERO, userData)
    }, Earnings(USER_ID, BigDecimal("0.85")))
    playtimeFacade.mock(
      { getConvertedUsdToUserCurrency(BigDecimal("0.85"), Currency.getInstance("CAD")) },
      CurrencyExchangeResultDto(BigDecimal("0.85"), Currency.getInstance("CAD"), BigDecimal("1.05"), BigDecimal("1.05"))
    )
    userEarningsPersistenceService.mock({
      bindRevenueWithEarnings(
        userId = USER_ID,
        revenueEventIds = listOf(revenue.id),
        calculatedUserEarningUsd = BigDecimal("0.85"),
        nonBoostedUserEarningUsd = null,
        userCurrency = Currency.getInstance("CAD"),
        userCurrencyEarningAmount = BigDecimal("1.05"),
        nonBoostedUserCurrencyEarningAmount = null,
      )
    }, metaId)
    userCurrentCoinsBalanceService.mock({ getUserCurrentCoinsBalance(USER_ID, userData.platform.fromProto()) }, UserCurrentCoinsGoalBalance(0, 0, 20))

    runTest {
      service.convertRevenueToEarnings(USER_ID, userData, periodStart)
    }

    verifyBlocking(messageBus) {
      publish(
        EarningsAddedEventDto(
          metaId = metaId,
          userId = USER_ID,
          amount = BigDecimal("0.85"),
          createdAt = now
        ),
        now.plusSeconds(CashoutPeriodDto.PERIOD_END_SHIFT_SECONDS)
      )
    }
    verifyBlocking(userEarningsPersistenceService) {
      bindRevenueWithEarnings(
        userId = USER_ID,
        revenueEventIds = listOf(revenue.id),
        calculatedUserEarningUsd = BigDecimal("0.85"),
        nonBoostedUserEarningUsd = null,
        userCurrency = Currency.getInstance("CAD"),
        userCurrencyEarningAmount = BigDecimal("1.05"),
        nonBoostedUserCurrencyEarningAmount = null,
      )
    }
  }


  @Test
  fun `SHOULD convert revenue to earnings ON convertRevenueToEarnings WHEN challenges take its toll`() {
    val offerwallRevenue = GenericRevenueDto(
      id = "revOfferwall",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.FYBER,
      amount = BigDecimal("0.71"),
      amountExtra = BigDecimal("0.22"),
      gameId = null,
    )
    val gameRevenue = GenericRevenueDto(
      id = "revGame",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("3.14"),
      amountExtra = null,
      gameId = null,
    )
    val revenues = listOf(offerwallRevenue, gameRevenue)
    val revenueIds = revenues.map(GenericRevenueDto::id)
    val metaId = 1
    val userData = getUserDataResponse {
      userCurrency = "CAD"
      platform = Common.AppPlatformProto.ANDROID
    }

    playtimeFacade.mock({ revenueWithChallengesCut(USER_ID, listOf(gameRevenue)) }, BigDecimal("0.628"))
    userEarningsPersistenceService.mock({ getUnconvertedRevenue(USER_ID) }, revenues)
    earningsCalculationsService.mock({
      convertRevenueToEarnings(USER_ID, BigDecimal("1.558"), BigDecimal("0.93"), userData)
    }, Earnings(USER_ID, BigDecimal("0.85")))
    playtimeFacade.mock(
      { getConvertedUsdToUserCurrency(BigDecimal("0.85"), Currency.getInstance("CAD")) },
      CurrencyExchangeResultDto(BigDecimal("0.85"), Currency.getInstance("CAD"), BigDecimal("1.05"), BigDecimal("1.05"))
    )
    userEarningsPersistenceService.mock({
      bindRevenueWithEarnings(
        userId = USER_ID,
        revenueEventIds = revenueIds,
        calculatedUserEarningUsd = BigDecimal("0.85"),
        nonBoostedUserEarningUsd = null,
        userCurrency = Currency.getInstance("CAD"),
        userCurrencyEarningAmount = BigDecimal("1.05"),
        nonBoostedUserCurrencyEarningAmount = null,
      )
    }, metaId)
    userCurrentCoinsBalanceService.mock({ getUserCurrentCoinsBalance(USER_ID, userData.platform.fromProto()) }, UserCurrentCoinsGoalBalance(0, 0, 20))

    runTest {
      service.convertRevenueToEarnings(USER_ID, userData, periodStart)
    }

    verifyBlocking(messageBus) {
      publish(
        EarningsAddedEventDto(
          metaId = metaId,
          userId = USER_ID,
          amount = BigDecimal("0.85"),
          createdAt = now
        ),
        now.plusSeconds(CashoutPeriodDto.PERIOD_END_SHIFT_SECONDS)
      )
    }
    verifyBlocking(userEarningsPersistenceService) {
      bindRevenueWithEarnings(
        userId = USER_ID,
        revenueEventIds = revenueIds,
        calculatedUserEarningUsd = BigDecimal("0.85"),
        nonBoostedUserEarningUsd = null,
        userCurrency = Currency.getInstance("CAD"),
        userCurrencyEarningAmount = BigDecimal("1.05"),
        nonBoostedUserCurrencyEarningAmount = null,
      )
    }
  }

  @Test
  fun `SHOULD convert revenue to earnings with specific currency and send earnings event ON convertRevenueToEarnings WHEN there is extra amount`() {
    val revenue = GenericRevenueDto(
      id = "eventId",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("0.50"),
      amountExtra = BigDecimal("0.10"),
      gameId = null,
    )
    val userData = getUserDataResponse {
      userCurrency = "USD"
      platform = Common.AppPlatformProto.ANDROID
    }
    val metaId = 1
    userEarningsPersistenceService.mock({ getUnconvertedRevenue(USER_ID) }, listOf(revenue))
    earningsCalculationsService.mock({
      convertRevenueToEarnings(USER_ID, BigDecimal("0.60"), BigDecimal.ZERO, userData)
    }, Earnings(USER_ID, BigDecimal("0.30")))
    playtimeFacade.mock(
      { getConvertedUsdToUserCurrency(BigDecimal("0.30"), Currency.getInstance("USD")) },
      CurrencyExchangeResultDto(BigDecimal("0.30"), Currency.getInstance("USD"), BigDecimal("0.30"), BigDecimal("0.30"))
    )
    userEarningsPersistenceService.mock({
      bindRevenueWithEarnings(
        userId = USER_ID,
        revenueEventIds = listOf(revenue.id),
        calculatedUserEarningUsd = BigDecimal("0.30"),
        nonBoostedUserEarningUsd = null,
        userCurrency = Currency.getInstance("USD"),
        userCurrencyEarningAmount = BigDecimal("0.30"),
        nonBoostedUserCurrencyEarningAmount = null,
      )
    }, metaId)
    userCurrentCoinsBalanceService.mock({ getUserCurrentCoinsBalance(USER_ID, userData.platform.fromProto()) }, UserCurrentCoinsGoalBalance(0, 0, 20))

    runTest {
      service.convertRevenueToEarnings(USER_ID, userData, periodStart)
    }

    verifyBlocking(messageBus) {
      publish(
        EarningsAddedEventDto(
          metaId = metaId,
          userId = USER_ID,
          amount = BigDecimal("0.30"),
          createdAt = now
        ),
        now.plusSeconds(CashoutPeriodDto.PERIOD_END_SHIFT_SECONDS)
      )
    }
    verifyBlocking(userEarningsPersistenceService) {
      bindRevenueWithEarnings(
        userId = USER_ID,
        revenueEventIds = listOf(revenue.id),
        calculatedUserEarningUsd = BigDecimal("0.30"),
        nonBoostedUserEarningUsd = null,
        userCurrency = Currency.getInstance("USD"),
        userCurrencyEarningAmount = BigDecimal("0.30"),
        nonBoostedUserCurrencyEarningAmount = null,
      )
    }
  }

  @Test
  fun `SHOULD convert revenue to earnings with specific currency and send earnings event ON convertRevenueToEarnings WHEN there is extra amount AND earnings exceeds critical ratio`() {
    val revenue = GenericRevenueDto(
      id = "eventId",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("1.00"),
      amountExtra = BigDecimal("0.52"),
      gameId = null,
    )
    val userData = getUserDataResponse {
      userCurrency = "USD"
      platform = Common.AppPlatformProto.ANDROID
    }
    val metaId = 1
    userEarningsPersistenceService.mock({ getUnconvertedRevenue(USER_ID) }, listOf(revenue))
    earningsCalculationsService.mock({
      convertRevenueToEarnings(USER_ID, BigDecimal("1.52"), BigDecimal.ZERO, userData)
    }, Earnings(USER_ID, BigDecimal("0.76")))
    playtimeFacade.mock(
      { getConvertedUsdToUserCurrency(BigDecimal("0.7500"), Currency.getInstance("USD")) },
      CurrencyExchangeResultDto(BigDecimal("0.7500"), Currency.getInstance("USD"), BigDecimal("0.75"), BigDecimal("0.75"))
    )
    userEarningsPersistenceService.mock({
      bindRevenueWithEarnings(
        userId = USER_ID,
        revenueEventIds = listOf(revenue.id),
        calculatedUserEarningUsd = BigDecimal("0.7500"),
        nonBoostedUserEarningUsd = null,
        userCurrency = Currency.getInstance("USD"),
        userCurrencyEarningAmount = BigDecimal("0.75"),
        nonBoostedUserCurrencyEarningAmount = null,
      )
    }, metaId)
    userCurrentCoinsBalanceService.mock({ getUserCurrentCoinsBalance(USER_ID, userData.platform.fromProto()) }, UserCurrentCoinsGoalBalance(0, 0, 20))

    runTest {
      service.convertRevenueToEarnings(USER_ID, userData, periodStart)
    }

    verifyBlocking(messageBus) {
      publish(
        EarningsAddedEventDto(
          metaId = metaId,
          userId = USER_ID,
          amount = BigDecimal("0.7500"),
          createdAt = now
        ),
        now.plusSeconds(CashoutPeriodDto.PERIOD_END_SHIFT_SECONDS)
      )
    }
    verifyBlocking(userEarningsPersistenceService) {
      bindRevenueWithEarnings(
        userId = USER_ID,
        revenueEventIds = listOf(revenue.id),
        calculatedUserEarningUsd = BigDecimal("0.7500"),
        nonBoostedUserEarningUsd = null,
        userCurrency = Currency.getInstance("USD"),
        userCurrencyEarningAmount = BigDecimal("0.75"),
        nonBoostedUserCurrencyEarningAmount = null,
      )
    }
  }

  @Test
  fun `SHOULD convert revenue to earnings but NOT send earnings event and not mark quota as used ON convertRevenueToEarnings WHEN earnings amount is zero`() {
    val revenue = GenericRevenueDto(
      id = "eventId",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("2.00"),
      amountExtra = null,
      gameId = null,
    )
    val metaId = 1
    val quotasDto = UserEarningsQuotasDto(USER_ID, "1,1,1,1,1,1,1,1".split(","), Instant.now())
    userEarningsPersistenceService.mock({ getUnconvertedRevenue(USER_ID) }, listOf(revenue))
    val userData = getUserDataResponse {
      userCurrency = "USD"
      platform = Common.AppPlatformProto.ANDROID
    }

    earningsCalculationsService.mock(
      { convertRevenueToEarnings(USER_ID, BigDecimal("2.00"), BigDecimal.ZERO, userData) },
      Earnings(USER_ID, BigDecimal("0.00"), quotasDto)
    )
    userEarningsPersistenceService.mock(
      {
        bindRevenueWithEarnings(
          userId = USER_ID,
          revenueEventIds = listOf(revenue.id),
          calculatedUserEarningUsd = BigDecimal.ZERO,
          nonBoostedUserEarningUsd = null,
          userCurrency = Currency.getInstance("USD"),
          userCurrencyEarningAmount = BigDecimal.ZERO,
          nonBoostedUserCurrencyEarningAmount = null,
        )
      },
      metaId
    )
    userCurrentCoinsBalanceService.mock({ getUserCurrentCoinsBalance(USER_ID, userData.platform.fromProto()) }, UserCurrentCoinsGoalBalance(0, 0, 20))

    runTest {
      service.convertRevenueToEarnings(USER_ID, userData, periodStart)
    }

    verifyBlocking(userEarningsPersistenceService) {
      bindRevenueWithEarnings(
        userId = USER_ID,
        revenueEventIds = listOf(revenue.id),
        calculatedUserEarningUsd = BigDecimal.ZERO,
        nonBoostedUserEarningUsd = null,
        userCurrency = Currency.getInstance("USD"),
        userCurrencyEarningAmount = BigDecimal.ZERO,
        nonBoostedUserCurrencyEarningAmount = null,
      )
    }
    verifyBlocking(userEarningsPersistenceService, never()) { saveUserQuotas(any()) }
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD convert revenue to earnings but NOT send earnings event and not mark quota as used ON convertRevenueToEarnings WHEN earnings amount in user currency is close to zero`() {
    val earningsAmountUsd = BigDecimal.ZERO
    val earningsAmountUserCurrency = BigDecimal("0.001")
    val currency = Currency.getInstance("CAD")
    val revenue = GenericRevenueDto(
      id = "eventId",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = earningsAmountUserCurrency,
      amountExtra = null,
      gameId = null,
    )
    val metaId = 1
    val quotasDto = UserEarningsQuotasDto(USER_ID, "1,1,1,1,1,1,1,1".split(","), Instant.now())
    userEarningsPersistenceService.mock({ getUnconvertedRevenue(USER_ID) }, listOf(revenue))
    playtimeFacade.mock(
      { getConvertedUsdToUserCurrency(any(), any()) },
      CurrencyExchangeResultDto(earningsAmountUserCurrency, Currency.getInstance("USD"), earningsAmountUserCurrency, earningsAmountUserCurrency)
    )

    val userData = getUserDataResponse {
      userCurrency = currency.currencyCode
      platform = Common.AppPlatformProto.ANDROID
    }

    earningsCalculationsService.mock(
      { convertRevenueToEarnings(USER_ID, earningsAmountUserCurrency, BigDecimal.ZERO, userData) },
      Earnings(USER_ID, BigDecimal("0.0001"), quotasDto)
    )
    userEarningsPersistenceService.mock({
      bindRevenueWithEarnings(
        userId = USER_ID,
        revenueEventIds = listOf(revenue.id),
        calculatedUserEarningUsd = earningsAmountUsd,
        nonBoostedUserEarningUsd = null,
        userCurrency = currency,
        userCurrencyEarningAmount = BigDecimal.ZERO,
        nonBoostedUserCurrencyEarningAmount = null,
      )
    }, metaId)
    userCurrentCoinsBalanceService.mock({ getUserCurrentCoinsBalance(USER_ID, userData.platform.fromProto()) }, UserCurrentCoinsGoalBalance(0, 0, 20))

    runTest {
      service.convertRevenueToEarnings(USER_ID, userData, periodStart)
    }

    verifyBlocking(userEarningsPersistenceService) {
      bindRevenueWithEarnings(
        userId = USER_ID,
        revenueEventIds = listOf(revenue.id),
        calculatedUserEarningUsd = earningsAmountUsd,
        nonBoostedUserEarningUsd = null,
        userCurrency = currency,
        userCurrencyEarningAmount = BigDecimal.ZERO,
        nonBoostedUserCurrencyEarningAmount = null,
      )
    }
    verifyBlocking(userEarningsPersistenceService, never()) { saveUserQuotas(any()) }
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD convert revenue to earnings but NOT send earnings event and not mark quota as used ON convertRevenueToEarnings WHEN earnings amount zero`() {
    val earningsAmountUsd = BigDecimal.ZERO
    val earningsAmountUserCurrency = BigDecimal.ZERO
    val currency = Currency.getInstance("CAD")
    val revenue = GenericRevenueDto(
      id = "eventId",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = earningsAmountUserCurrency,
      amountExtra = null,
      gameId = null,
    )
    val metaId = 1
    val quotasDto = UserEarningsQuotasDto(USER_ID, "1,1,1,1,1,1,1,1".split(","), Instant.now())
    userEarningsPersistenceService.mock({ getUnconvertedRevenue(USER_ID) }, listOf(revenue))
    playtimeFacade.mock(
      { getConvertedUsdToUserCurrency(any(), any()) },
      CurrencyExchangeResultDto(earningsAmountUserCurrency, currency, earningsAmountUserCurrency, earningsAmountUserCurrency)
    )

    val userData = getUserDataResponse {
      userCurrency = currency.currencyCode
      platform = Common.AppPlatformProto.ANDROID
    }

    earningsCalculationsService.mock(
      { convertRevenueToEarnings(USER_ID, earningsAmountUserCurrency, BigDecimal.ZERO, userData) },
      Earnings(USER_ID, BigDecimal("0.0001"), quotasDto)
    )
    userEarningsPersistenceService.mock(
      {
        bindRevenueWithEarnings(
          userId = USER_ID,
          revenueEventIds = listOf(revenue.id),
          calculatedUserEarningUsd = earningsAmountUsd,
          nonBoostedUserEarningUsd = null,
          userCurrency = currency,
          userCurrencyEarningAmount = earningsAmountUserCurrency,
          nonBoostedUserCurrencyEarningAmount = null,
        )
      },
      metaId
    )
    userCurrentCoinsBalanceService.mock({ getUserCurrentCoinsBalance(USER_ID, userData.platform.fromProto()) }, UserCurrentCoinsGoalBalance(0, 0, 20))

    runTest {
      service.convertRevenueToEarnings(USER_ID, userData, periodStart)
    }

    verifyBlocking(userEarningsPersistenceService) {
      bindRevenueWithEarnings(
        userId = USER_ID,
        revenueEventIds = listOf(revenue.id),
        calculatedUserEarningUsd = earningsAmountUsd,
        nonBoostedUserEarningUsd = null,
        userCurrency = currency,
        userCurrencyEarningAmount = earningsAmountUserCurrency,
        nonBoostedUserCurrencyEarningAmount = null,
      )
    }
    verifyBlocking(userEarningsPersistenceService, never()) { saveUserQuotas(any()) }
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD skip conversion to earnings ON convertRevenueToEarnings WHEN there is no any revenue`() {
    userEarningsPersistenceService.mock({ getUnconvertedRevenue(USER_ID) }, listOf())
    val userData = getUserDataResponse { }

    runTest {
      service.convertRevenueToEarnings(USER_ID, userData, periodStart)
    }

    verifyBlocking(userEarningsPersistenceService) {
      getUnconvertedRevenue(USER_ID)
    }
    verifyNoMoreInteractions(userEarningsPersistenceService)
    verifyNoInteractions(earningsCalculationsService)
    verifyNoInteractions(messageBus)
    verifyBlocking(userEarningsPersistenceService, never()) { saveUserQuotas(any()) }
  }

  @Test
  fun `SHOULD convert revenue to earnings and provide offerwallRevenue explicitly and save user quotas ON convertRevenueToEarnings`() {
    val revenue = GenericRevenueDto(
      id = "eventId1",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("2.00"),
      amountExtra = null,
      gameId = null,
    )
    val offerWallRevenue = GenericRevenueDto(
      id = "eventId2",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.IRON_SOURCE,
      amount = BigDecimal("22.00"),
      amountExtra = null,
      gameId = null,
    )
    val offerWallRevenue2 = GenericRevenueDto(
      id = "eventId2",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.FYBER,
      amount = BigDecimal("0.50"),
      amountExtra = null,
      gameId = null,
    )
    val offerWallRevenue4 = GenericRevenueDto(
      id = "eventId4",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.TAPJOY,
      amount = BigDecimal("0.25"),
      amountExtra = null,
      gameId = null,
    )
    val offerWallRevenue5 = GenericRevenueDto(
      id = "eventId4",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.ADJOE,
      amount = BigDecimal("0.25"),
      amountExtra = null,
      gameId = null,
    )
    val metaId = 1
    val quotasDto = UserEarningsQuotasDto(USER_ID, "1,1,1,1,1,1,1,1".split(","), Instant.now())
    userEarningsPersistenceService.mock(
      { getUnconvertedRevenue(USER_ID) }, listOf(
        revenue, offerWallRevenue, offerWallRevenue.copy(amount = BigDecimal.TEN, id = "eventId3"), offerWallRevenue2, offerWallRevenue4, offerWallRevenue5
      )
    )

    val userData = getUserDataResponse {
      userCurrency = "USD"
      platform = Common.AppPlatformProto.ANDROID
    }

    earningsCalculationsService.mock(
      { convertRevenueToEarnings(USER_ID, BigDecimal("35.00"), BigDecimal("33.00"), userData) },
      Earnings(USER_ID, BigDecimal("2.1"), quotasDto)
    )
    userEarningsPersistenceService.mock({ bindRevenueWithEarnings(any(), any(), any(), anyOrNull(), any(), any(), anyOrNull()) }, metaId)
    userCurrentCoinsBalanceService.mock({ getUserCurrentCoinsBalance(USER_ID, userData.platform.fromProto()) }, UserCurrentCoinsGoalBalance(0, 0, 20))

    runTest {
      service.convertRevenueToEarnings(USER_ID, userData, periodStart)
    }
    verifyBlocking(earningsCalculationsService) { convertRevenueToEarnings(USER_ID, BigDecimal("35.00"), BigDecimal("33.00"), userData) }
    verifyBlocking(userEarningsPersistenceService) { saveUserQuotas(quotasDto) }

    verifyBlocking(messageBus) {
      publish(createUserRevenueMessagesMessage {
        userId = USER_ID
        userRevenue = BigDecimal("35.00").toProto()
        gamesRevenue = BigDecimal("2.00").toProto()
        userRevenueWithoutEarnings = BigDecimal("32.90").toProto()
      })
    }
  }

  @Test
  fun `SHOULD call removeOldDataFromCurrentGenericRevenue ON removeOldDataFromCurrentGenericRevenue`() = runTest {
    userEarningsPersistenceService.mock({ removeOldDataFromCurrentGenericRevenue(MAX_REVENUE_ROWS_TO_DELETE_PER_CRON_RUN, MAX_CHUNK_SIZE) }, 123)

    val result = service.removeOldDataFromCurrentGenericRevenue()

    verifyBlocking(userEarningsPersistenceService) { removeOldDataFromCurrentGenericRevenue(MAX_REVENUE_ROWS_TO_DELETE_PER_CRON_RUN, MAX_CHUNK_SIZE) }
    assertThat(result).isEqualTo(123)
  }

  @Test
  fun `SHOULD call removeUnconvertedOldRevenueForDeletedUsers ON removeUnconvertedOldRevenueForDeletedUsers`() = runTest {
    userEarningsPersistenceService.mock({ removeUnconvertedOldRevenueForDeletedUsers() }, 10 to BigDecimal.ONE)

    val result = service.removeUnconvertedOldRevenueForDeletedUsers()

    verifyBlocking(userEarningsPersistenceService) { removeUnconvertedOldRevenueForDeletedUsers() }
    assertThat(result).isEqualTo(10 to BigDecimal.ONE)
  }

  @Test
  fun `SHOULD return total usd earnings ON getTotalUsdEarningsForUser`() = runTest {
    val expected = BigDecimal("1.00")
    userEarningsPersistenceService.mock({ calculateTotalUsdEarningsForUser(USER_ID) }, expected)

    val actual = service.getTotalUsdEarningsForUser(USER_ID)

    assertThat(actual).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return zero non paid usd earnings ON getNonCashedUserCurrencyEarnings WHEN user has no earnings`() = runTest {
    userEarningsPersistenceService.mock({ calculateTotalUsdEarningsForUser(USER_ID) }, null)

    val actual = service.getTotalUsdEarningsForUser(USER_ID)

    assertThat(actual).isEqualTo(BigDecimal.ZERO)
  }

  @Test
  fun `SHOULD return total usd earnings ON getNonCashedUserEarningsWithOfferwallAmount`() = runTest {
    val expected = UserEarningsWithOfferwallAmount(totalRevenue = BigDecimal("11.00"), offerwallRevenue = BigDecimal("5.0"))
    userEarningsPersistenceService.mock({ loadUnpaidEarningsWithOfferwallAmount(USER_ID) }, expected)

    val actual = service.getNonCashedUserEarningsWithOfferwallAmount(USER_ID)

    assertThat(actual).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return zero non paid earnings ON getNonCashedUserEarningsWithOfferwallAmount WHEN user has no earnings`() = runTest {
    userEarningsPersistenceService.mock({ loadUnpaidEarningsWithOfferwallAmount(USER_ID) }, null)

    val actual = service.getNonCashedUserEarningsWithOfferwallAmount(USER_ID)

    assertThat(actual).isEqualTo(UserEarningsWithOfferwallAmount(BigDecimal.ZERO, BigDecimal.ZERO))
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD call userHasNoEarningsAfter from persistence layer ON userHasNoEarningsAfter`(result: Boolean) = runTest {
    userEarningsPersistenceService.mock({ noEarningsAfter(USER_ID, now) }, result)

    service.noEarningsAfter(USER_ID, now)
      .let { assertThat(it).isEqualTo(result) }

    verifyBlocking(userEarningsPersistenceService) { noEarningsAfter(USER_ID, now) }
  }

  @Test
  fun `SHOULD calculate and pass to persistence layer coins based earnings ON trackEm2EarningsCalculationData`() {
    mockExpectedCoinsBasedEarnings(sampleUserCoinsBasedEarningsData)
    val earningsComputationData = earningsComputationDataFrom(sampleUserCoinsBasedEarningsData)

    runTest {
      service.trackEm2EarningsCalculationData(
        USER_ID,
        periodStart,
        periodEnd,
        earningsComputationData,
        coinGoal = 42,
        userQuality = sampleUserCoinsBasedEarningsData.userQuality,
        currentToFirstEcpmRatio = sampleUserCoinsBasedEarningsData.currentToFirstEcpmRatio,
      )
    }

    verifyBlocking(userEarningsPersistenceService) { trackEm2EarningsCalculationData(sampleUserCoinsBasedEarningsData) }
  }

  @Test
  fun `SHOULD calculate and pass to persistence layer coins based earnings ON trackEm2EarningsCalculationData WHEN user quality is defined`() {
    val expectedEarnings = sampleUserCoinsBasedEarningsData.copy(
      userQuality = BigDecimal("0.2"),
      currentToFirstEcpmRatio = BigDecimal("1.5"),
    )
    val earningsComputationData = earningsComputationDataFrom(expectedEarnings)

    mockExpectedCoinsBasedEarnings(expectedEarnings)

    runTest {
      service.trackEm2EarningsCalculationData(
        USER_ID,
        periodStart,
        periodEnd,
        earningsComputationData,
        coinGoal = 42,
        userQuality = expectedEarnings.userQuality,
        currentToFirstEcpmRatio = expectedEarnings.currentToFirstEcpmRatio,
      )
    }

    verifyBlocking(userEarningsPersistenceService) { trackEm2EarningsCalculationData(expectedEarnings) }
  }

  @Test
  fun `SHOULD not trigger adding coins based earnings on checkFilteringAndTrackEm2EarningsCalculationData WHEN filter is set to null`() {
    mockExpectedCoinsBasedEarnings(sampleUserCoinsBasedEarningsData)
    val earningsComputationData = earningsComputationDataFrom(sampleUserCoinsBasedEarningsData)

    runTest {
      service.checkFilteringAndTrackEm2EarningsCalculationData(
        USER_ID,
        now,
        now,
        earningsComputationData = earningsComputationData,
        coinGoal = 42,
        userFilter = null,
        userQuality = sampleUserCoinsBasedEarningsData.userQuality,
        currentToFirstEcpmRatio = sampleUserCoinsBasedEarningsData.currentToFirstEcpmRatio,
      )
    }

    verifyBlocking(userEarningsPersistenceService, never()) { trackEm2EarningsCalculationData(any()) }
  }

  @Test
  fun `SHOULD not trigger adding coins based earnings on checkFilteringAndTrackEm2EarningsCalculationData WHEN userId does not match the filter`() {
    mockExpectedCoinsBasedEarnings(sampleUserCoinsBasedEarningsData)
    val earningsComputationData = earningsComputationDataFrom(sampleUserCoinsBasedEarningsData)

    runTest {
      service.checkFilteringAndTrackEm2EarningsCalculationData(
        USER_ID,
        now,
        now,
        earningsComputationData = earningsComputationData,
        coinGoal = 42,
        userFilter = "ff",
        userQuality = sampleUserCoinsBasedEarningsData.userQuality,
        currentToFirstEcpmRatio = sampleUserCoinsBasedEarningsData.currentToFirstEcpmRatio,
      )
    }

    verifyBlocking(userEarningsPersistenceService, never()) { trackEm2EarningsCalculationData(any()) }
  }

  @Test
  fun `SHOULD trigger adding coins based earnings on checkFilteringAndTrackEm2EarningsCalculationData WHEN userId matches the filter`() {
    mockExpectedCoinsBasedEarnings(sampleUserCoinsBasedEarningsData)
    val earningsComputationData = earningsComputationDataFrom(sampleUserCoinsBasedEarningsData)

    runTest {
      service.checkFilteringAndTrackEm2EarningsCalculationData(
        USER_ID,
        periodStart,
        periodEnd,
        earningsComputationData,
        coinGoal = 42,
        userFilter = "us",
        userQuality = sampleUserCoinsBasedEarningsData.userQuality,
        currentToFirstEcpmRatio = sampleUserCoinsBasedEarningsData.currentToFirstEcpmRatio,
      )
    }

    verifyBlocking(userEarningsPersistenceService) { trackEm2EarningsCalculationData(sampleUserCoinsBasedEarningsData) }
  }

  @Test
  fun `SHOULD trigger adding coins based earnings on checkFilteringAndTrackEm2EarningsCalculationData WHEN filter is set to all`() {
    mockExpectedCoinsBasedEarnings(sampleUserCoinsBasedEarningsData)
    val earningsComputationData = earningsComputationDataFrom(sampleUserCoinsBasedEarningsData)

    runTest {
      service.checkFilteringAndTrackEm2EarningsCalculationData(
        USER_ID,
        periodStart,
        periodEnd,
        earningsComputationData = earningsComputationData,
        coinGoal = 42,
        userFilter = FILTER_VALUE_ALL,
        userQuality = sampleUserCoinsBasedEarningsData.userQuality,
        currentToFirstEcpmRatio = sampleUserCoinsBasedEarningsData.currentToFirstEcpmRatio,
      )
    }

    verifyBlocking(userEarningsPersistenceService) { trackEm2EarningsCalculationData(sampleUserCoinsBasedEarningsData) }
  }

  @ParameterizedTest
  @ValueSource(
    strings =
      ["good case", "too big earnings", "coin goal is not reached", "is not highly trusted",
        "not the first cp", "no such user", "no current cashout period"]
  )
  fun `SHOULD top up ten cents for user earnings if he is highly trusted, reached coin goal and it is the very first cashout period`(option: String) = runTest {
    val revenueAmount = BigDecimal("1.00").takeIf { option != "too big earnings" } ?: BigDecimal("60.0")
    val earningsWithBonusTenCents = BigDecimal("0.60")
    val revenue = GenericRevenueDto(
      id = "eventId1",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = revenueAmount,
      amountExtra = null,
      gameId = null,
    )

    val userData = getUserDataResponse {
      isHighlyTrustedUser = option != "is not highly trusted"
      if (option != "no current cashout period") {
        cashoutPeriodCounter = Int32Value.of(if (option != "not the first cp") 1 else 2)
      }
      coinGoal = 10
      userCurrency = "USD"
      platform = Common.AppPlatformProto.ANDROID
    }

    userCurrentCoinsBalanceService.mock(
      { getUserCurrentCoinsBalance(USER_ID, userData.platform.fromProto()) },
      UserCurrentCoinsGoalBalance(
        coins = 0,
        gameCoins = 0,
        goalCoins = if (option != "no such user") {
          if (option != "coin goal is not reached") 20 else 1
        } else 1
      )
    )

    val metaId = 1
    val calculatedEarnings = Earnings(USER_ID, BigDecimal("0.5")).takeIf { option != "too big earnings" } ?: Earnings(USER_ID, BigDecimal("29.99"))
    userEarningsPersistenceService.mock({ getUnconvertedRevenue(USER_ID) }, listOf(revenue))
    earningsCalculationsService.mock({ convertRevenueToEarnings(USER_ID, revenueAmount, BigDecimal.ZERO, userData) }, calculatedEarnings)
    userEarningsPersistenceService.mock({ bindRevenueWithEarnings(any(), any(), any(), anyOrNull(), any(), any(), anyOrNull()) }, metaId)

    service.convertRevenueToEarnings(USER_ID, userData, periodStart)
      .let {
        assertThat(it.amount).isEqualByComparingTo(earningsWithBonusTenCents.takeIf { option == "good case" } ?: calculatedEarnings.earningsSum)
      }

    verifyBlocking(earningsCalculationsService) { convertRevenueToEarnings(USER_ID, revenueAmount, BigDecimal.ZERO, userData) }
  }

  @Test
  fun `SHOULD return boosted earnings ON convertRevenueToEarnings WHEN top-up is applied`() = runTest {
    val revenueAmount = BigDecimal("1.00")
    val earningsWithAdditions = BigDecimal("0.765")

    val expected = EarningsCalculationResult.Simple(
      metaId = 1,
      amount = BigDecimal("0.765"),
      amountNoRounding = earningsWithAdditions,
    )

    val revenue = GenericRevenueDto(
      id = "eventId1",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = revenueAmount,
      amountExtra = null,
      gameId = null,
    )

    val userData = getUserDataResponse {
      isHighlyTrustedUser = true
      cashoutPeriodCounter = Int32Value.of(1)
      coinGoal = 10
      userCurrency = "USD"
      platform = Common.AppPlatformProto.ANDROID
    }

    userCurrentCoinsBalanceService.mock(
      { getUserCurrentCoinsBalance(USER_ID, userData.platform.fromProto()) },
      UserCurrentCoinsGoalBalance(
        coins = 0,
        gameCoins = 0,
        goalCoins = 20
      )
    )

    val metaId = 1
    val calculatedEarnings = Earnings(USER_ID, BigDecimal("0.5"))

    playtimeFacade.mock(
      { getBoostedModeConfig(USER_ID, periodStart) },
      PlaytimeFacade.BoostedModeConfig(
        earningsIncreaseCoefficient = BigDecimal("1.33"),
        earningsFakeDecreaseCoefficient = BigDecimal("1.53"),
      )
    )
    userEarningsPersistenceService.mock({ getUnconvertedRevenue(USER_ID) }, listOf(revenue))
    earningsCalculationsService.mock({ convertRevenueToEarnings(USER_ID, revenueAmount, BigDecimal.ZERO, userData) }, calculatedEarnings)
    // mock controls right nonBoosted value calculation
    userEarningsPersistenceService.mock({ bindRevenueWithEarnings(any(), any(), any(), eq(BigDecimal("0.426797")), any(), any(), anyOrNull()) }, metaId)

    service.convertRevenueToEarnings(USER_ID, userData, periodStart).let { actual ->
      assertThat(actual).isEqualTo(expected)
    }

    verifyBlocking(earningsCalculationsService) { convertRevenueToEarnings(USER_ID, revenueAmount, BigDecimal.ZERO, userData) }
  }

  @Test
  fun `SHOULD return top-up amount ON getFirstCpTopUp`() = runTest {
    val earningsAmount = BigDecimal("29.80")
    val userData = getUserDataResponse {
      coinGoal = 10
      isHighlyTrustedUser = true
      cashoutPeriodCounter = Int32Value.of(1)
      platform = Common.AppPlatformProto.ANDROID
    }
    userCurrentCoinsBalanceService.mock({ getUserCurrentCoinsBalance(USER_ID, userData.platform.fromProto()) }, UserCurrentCoinsGoalBalance(0, 0, 20))

    service.getFirstCpTopUp(USER_ID, earningsAmount, userData).let { actual ->
      assertThat(actual).isEqualByComparingTo(BigDecimal("0.1"))
    }
  }

  @Test
  fun `SHOULD return 0 ON getFirstCpTopUp WHEN earnings too big`() = runTest {
    val earningsAmount = BigDecimal("29.91")
    val userData = getUserDataResponse {
      coinGoal = 10
      isHighlyTrustedUser = true
      cashoutPeriodCounter = Int32Value.of(1)
      platform = Common.AppPlatformProto.ANDROID
    }
    userCurrentCoinsBalanceService.mock({ getUserCurrentCoinsBalance(USER_ID, userData.platform.fromProto()) }, UserCurrentCoinsGoalBalance(0, 0, 20))

    service.getFirstCpTopUp(USER_ID, earningsAmount, userData).let { actual ->
      assertThat(actual).isZero()
    }
  }

  @Test
  fun `SHOULD return 0 ON getFirstCpTopUp WHEN coin goal not reached`() = runTest {
    val earningsAmount = BigDecimal("29.80")
    val userData = getUserDataResponse {
      coinGoal = 10
      isHighlyTrustedUser = true
      cashoutPeriodCounter = Int32Value.of(1)
      platform = Common.AppPlatformProto.ANDROID
    }
    userCurrentCoinsBalanceService.mock({ getUserCurrentCoinsBalance(USER_ID, userData.platform.fromProto()) }, UserCurrentCoinsGoalBalance(0, 0, 1))

    service.getFirstCpTopUp(USER_ID, earningsAmount, userData).let { actual ->
      assertThat(actual).isZero()
    }
  }

  @Test
  fun `SHOULD return 0 ON getFirstCpTopUp WHEN NOT highly trusted`() = runTest {
    val earningsAmount = BigDecimal("29.80")
    val userData = getUserDataResponse {
      coinGoal = 10
      isHighlyTrustedUser = false
      cashoutPeriodCounter = Int32Value.of(1)
      platform = Common.AppPlatformProto.ANDROID
    }
    userCurrentCoinsBalanceService.mock({ getUserCurrentCoinsBalance(USER_ID, userData.platform.fromProto()) }, UserCurrentCoinsGoalBalance(0, 0, 20))

    service.getFirstCpTopUp(USER_ID, earningsAmount, userData).let { actual ->
      assertThat(actual).isZero()
    }
  }

  @Test
  fun `SHOULD return 0 ON getFirstCpTopUp WHEN not first cp`() = runTest {
    val earningsAmount = BigDecimal("29.80")
    val userData = getUserDataResponse {
      coinGoal = 10
      isHighlyTrustedUser = true
      cashoutPeriodCounter = Int32Value.of(2)
      platform = Common.AppPlatformProto.ANDROID
    }
    userCurrentCoinsBalanceService.mock({ getUserCurrentCoinsBalance(USER_ID, userData.platform.fromProto()) }, UserCurrentCoinsGoalBalance(0, 0, 20))

    service.getFirstCpTopUp(USER_ID, earningsAmount, userData).let { actual ->
      assertThat(actual).isZero()
    }
  }

  @Test
  fun `SHOULD track cashout revenue ON giveEarningsToUser`() {
    val revenue = GenericRevenueDto(
      id = "eventId",
      userId = USER_ID,
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("2.10"),
      amountExtra = BigDecimal("0.40"),
      gameId = null,
    )
    val offerwallRevenue = GenericRevenueDto(
      id = "eventIdOf",
      userId = USER_ID,
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.TAPJOY,
      amount = BigDecimal("6.66"),
      amountExtra = BigDecimal("0.44"),
      gameId = null,
    )
    val metaId = 1

    userEarningsPersistenceService.mock(
      {
        bindRevenueWithEarnings(
          userId = USER_ID,
          revenueEventIds = listOf(revenue.id, offerwallRevenue.id),
          calculatedUserEarningUsd = BigDecimal("7.45"),
          nonBoostedUserEarningUsd = null,
          userCurrency = Currency.getInstance("CAD"),
          userCurrencyEarningAmount = BigDecimal("1.05"),
          nonBoostedUserCurrencyEarningAmount = null,
        )
      },
      metaId
    )
    playtimeFacade.mock(
      { getConvertedUsdToUserCurrency(BigDecimal("7.45"), Currency.getInstance("CAD")) },
      CurrencyExchangeResultDto(BigDecimal("7.45"), Currency.getInstance("CAD"), BigDecimal("1.05"), BigDecimal("1.051"))
    )

    runTest {
      service.giveEarningsToUser(
        USER_ID,
        revenueList = listOf(revenue, offerwallRevenue),
        earningsFromRevenue = Earnings(USER_ID, BigDecimal("6.57")),
        earningsWithAdditionsAmountUsd = BigDecimal("7.45"),
        nonBoostedEarningsWithAdditionsAmountUsd = null,
        Currency.getInstance("CAD"),
      )
        .let { actual ->
          assertThat(actual).isEqualTo(
            EarningsCalculationResult.Simple(
              metaId = metaId,
              amount = BigDecimal("7.45"),
              amountNoRounding = BigDecimal("1.051")
            )
          )
        }
    }

    verifyBlocking(userEarningsPersistenceService) {
      bindRevenueWithEarnings(
        userId = USER_ID,
        revenueEventIds = listOf(revenue.id, offerwallRevenue.id),
        calculatedUserEarningUsd = BigDecimal("7.45"),
        nonBoostedUserEarningUsd = null,
        userCurrency = Currency.getInstance("CAD"),
        userCurrencyEarningAmount = BigDecimal("1.05"),
        nonBoostedUserCurrencyEarningAmount = null,
      )
    }
    verifyBlocking(userEarningsPersistenceService) {
      trackEarningsCalculationData(metaId = metaId, revenue = BigDecimal("8.76"), offerwallRevenue = BigDecimal("6.66"), extraRevenue = BigDecimal("0.84"))
    }
    verifyBlocking(userEarningsPersistenceService, never()) { saveUserQuotas(any()) }
    verifyBlocking(userEarningsPersistenceService, never()) { trackLastLowEarningsCashoutPeriod(USER_ID, now) }
  }

  @Test
  fun `SHOULD not track earnings calculation data ON giveEarningsToUser with zero earnings`() {
    val revenue = GenericRevenueDto(
      id = "eventId",
      userId = USER_ID,
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("0.00"),
      amountExtra = BigDecimal("0.00"),
      gameId = null,
    )
    val offerwallRevenue = GenericRevenueDto(
      id = "eventIdOf",
      userId = USER_ID,
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.TAPJOY,
      amount = BigDecimal("0.00"),
      amountExtra = BigDecimal("0.00"),
      gameId = null,
    )
    val metaId = 1

    userEarningsPersistenceService.mock(
      {
        bindRevenueWithEarnings(
          userId = USER_ID,
          revenueEventIds = listOf(revenue.id, offerwallRevenue.id),
          calculatedUserEarningUsd = BigDecimal("0"),
          nonBoostedUserEarningUsd = null,
          userCurrency = Currency.getInstance("CAD"),
          userCurrencyEarningAmount = BigDecimal("0"),
          nonBoostedUserCurrencyEarningAmount = null,
        )
      },
      metaId
    )
    playtimeFacade.mock(
      { getConvertedUsdToUserCurrency(BigDecimal("0.00"), Currency.getInstance("CAD")) },
      CurrencyExchangeResultDto(BigDecimal("0.00"), Currency.getInstance("CAD"), BigDecimal("0"), BigDecimal("0.00"))
    )

    runTest {
      service.giveEarningsToUser(
        USER_ID,
        revenueList = listOf(revenue, offerwallRevenue),
        earningsFromRevenue = Earnings(USER_ID, BigDecimal("0.00")),
        earningsWithAdditionsAmountUsd = BigDecimal("0.00"),
        nonBoostedEarningsWithAdditionsAmountUsd = null,
        userCurrency = Currency.getInstance("CAD"),
      )
        .let { actual ->
          assertThat(actual).isEqualTo(
            EarningsCalculationResult.Simple(
              metaId = metaId,
              amount = BigDecimal("0"),
              amountNoRounding = BigDecimal("0.00")
            )
          )
        }
    }

    verifyBlocking(userEarningsPersistenceService) {
      bindRevenueWithEarnings(
        userId = USER_ID,
        revenueEventIds = listOf(revenue.id, offerwallRevenue.id),
        calculatedUserEarningUsd = BigDecimal("0"),
        nonBoostedUserEarningUsd = null,
        userCurrency = Currency.getInstance("CAD"),
        userCurrencyEarningAmount = BigDecimal("0"),
        nonBoostedUserCurrencyEarningAmount = null,
      )
    }
    verifyBlocking(userEarningsPersistenceService, never()) {
      trackEarningsCalculationData(any(), any(), any(), any())
    }
    verifyBlocking(userEarningsPersistenceService, never()) { saveUserQuotas(any()) }
    verifyBlocking(userEarningsPersistenceService) { trackLastLowEarningsCashoutPeriod(USER_ID, now) }
  }


  @Test
  fun `SHOULD save non boosted earnings ON giveEarningsToUser WHEN non boosted earnings defined`() {
    val revenue = GenericRevenueDto(
      id = "eventId",
      userId = USER_ID,
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("2.10"),
      amountExtra = BigDecimal("0.40"),
      gameId = null,
    )
    val metaId = 1

    userEarningsPersistenceService.mock(
      {
        bindRevenueWithEarnings(
          userId = USER_ID,
          revenueEventIds = listOf(revenue.id),
          calculatedUserEarningUsd = BigDecimal("7.45"),
          nonBoostedUserEarningUsd = BigDecimal("7.11"),
          userCurrency = Currency.getInstance("CAD"),
          userCurrencyEarningAmount = BigDecimal("8.45"),
          nonBoostedUserCurrencyEarningAmount = BigDecimal("8.15"),
        )
      },
      metaId
    )
    playtimeFacade.mock(
      { getConvertedUsdToUserCurrency(BigDecimal("7.45"), Currency.getInstance("CAD")) },
      CurrencyExchangeResultDto(BigDecimal("7.45"), Currency.getInstance("CAD"), BigDecimal("8.45"), BigDecimal("8.45432"))
    )
    playtimeFacade.mock(
      { getConvertedUsdToUserCurrency(BigDecimal("7.11"), Currency.getInstance("CAD")) },
      CurrencyExchangeResultDto(BigDecimal("7.15"), Currency.getInstance("CAD"), BigDecimal("8.15"), BigDecimal("8.15432"))
    )

    runTest {
      service.giveEarningsToUser(
        USER_ID,
        revenueList = listOf(revenue),
        earningsFromRevenue = Earnings(USER_ID, BigDecimal("6.57")),
        earningsWithAdditionsAmountUsd = BigDecimal("7.45"),
        nonBoostedEarningsWithAdditionsAmountUsd = BigDecimal("7.11"),
        Currency.getInstance("CAD"),
      )
        .let { actual ->
          assertThat(actual).isEqualTo(
            EarningsCalculationResult.Simple(
              metaId = metaId,
              amount = BigDecimal("7.45"),
              amountNoRounding = BigDecimal("8.45432")
            )
          )
        }
    }

    verifyBlocking(userEarningsPersistenceService) {
      bindRevenueWithEarnings(
        userId = USER_ID,
        revenueEventIds = listOf(revenue.id),
        calculatedUserEarningUsd = BigDecimal("7.45"),
        nonBoostedUserEarningUsd = BigDecimal("7.11"),
        userCurrency = Currency.getInstance("CAD"),
        userCurrencyEarningAmount = BigDecimal("8.45"),
        nonBoostedUserCurrencyEarningAmount = BigDecimal("8.15"),
      )
    }
  }

  @Test
  fun `SHOULD trigger persistence layer method ON trackUnpaidUserEarnings`() {
    runBlocking {
      service.trackUnpaidUserEarnings(USER_ID, someEarningsStub)
    }

    verifyBlocking(userEarningsPersistenceService) { trackUnpaidUserEarnings(USER_ID, someEarningsStub) }
  }

  @Test
  fun `SHOULD trigger persistence layer method ON userEverHadEarnings`() {
    runBlocking {
      service.userEverHadEarnings(USER_ID)
    }

    verifyBlocking(userEarningsPersistenceService) { userEverHadEarnings(USER_ID) }
  }

  @Test
  fun `SHOULD not boost earnings ON boostEarnings WHEN NOT boost defined`() {
    runBlocking { service.boostEarnings(USER_ID, BigDecimal("3.1415"), periodStart) }.let { actual ->
      assertThat(actual).isNull()
    }
  }

  @Test
  fun `SHOULD boost earnings ON boostEarnings WHEN boost defined`() {
    val expected = UserEarningsService.BoostedEarnings(
      earningsBefore = BigDecimal("2.053268"),
      earningsAfter = BigDecimal("4.178195"),
    )

    playtimeFacade.mock(
      { getBoostedModeConfig(USER_ID, periodStart) },
      PlaytimeFacade.BoostedModeConfig(
        earningsIncreaseCoefficient = BigDecimal("1.33"),
        earningsFakeDecreaseCoefficient = BigDecimal("1.53"),
      )
    )

    runBlocking { service.boostEarnings(USER_ID, BigDecimal("3.1415"), periodStart) }.let { actual ->
      assertThat(actual).isNotNull().isEqualTo(expected)
    }
  }

  private fun mockExpectedCoinsBasedEarnings(earningsData: UserCoinsBasedEarningsData) {
    abTestingFacade.mock({ assignedVariation(USER_ID, ClientExperiment.EARNINGS_MODEL_V2) }, earningsData.em2Variation)
  }

  private fun earningsComputationDataFrom(earningsData: UserCoinsBasedEarningsData): EarningsCalculationResult.Em2 =
    EarningsCalculationResult.Em2(
      simpleCalculationResult = EarningsCalculationResult.Simple(
        metaId = META_ID,
        amount = earningsData.earningsAmount,
        amountNoRounding = BigDecimal("1.23456")
      ),
      noEarnings = false,
      realRevenue = earningsData.realRevenue,
      realGameRevenue = earningsData.realGameRevenue,
      em2CoinsBalance = UserCurrentCoinsBalance(
        gameCoins = earningsData.em2GameCoins,
        offerCoins = earningsData.em2OfferCoins,
        bonusCoins = earningsData.em2BonusCoins
      ),
      coinsForOneDollar = earningsData.coinsForOneDollar,
      em2Revenue = earningsData.em2Revenue,
      em2GameRevenue = earningsData.em2GameRevenue,
      earnings = Earnings(
        userId = earningsData.userId,
        earningsSum = BigDecimal("2.34"),
        quotasDto = UserEarningsQuotasDto(
          userId = earningsData.userId,
          quotas = listOf("quotas"),
          periodEnd = earningsData.periodEnd,
          values = listOf(BigDecimal.ONE)
        ),
        usedQuota = UsedQuota(earningsData.usedQuota)
      )
    )

  private fun verifyAddUserRevenueTriggered(event: RevenueReceivedEventDto) = with(event) {
    verifyBlocking(userEarningsPersistenceService) {
      addUserRevenue(
        eventId = eventId,
        userId = userId,
        source = source,
        timestamp = timestamp,
        amount = amount,
        networkId = -1,
        gameId = gameId,
        amountExtra = event.amountExtra
      )
    }
  }
}
