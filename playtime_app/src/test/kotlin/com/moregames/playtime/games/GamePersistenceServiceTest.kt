package com.moregames.playtime.games

import assertk.assertThat
import assertk.assertions.*
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.gameStub
import com.moregames.base.table.*
import com.moregames.base.util.TimeService
import com.moregames.base.util.prepareGame
import com.moregames.base.util.prepareUser
import com.moregames.playtime.general.GamesSetupService
import com.moregames.playtime.general.GamesSetupService.AdUnitIds
import com.moregames.playtime.general.GamesSetupService.AdUnitType.*
import com.moregames.playtime.general.table.GameAdUnitIdsTable
import com.moregames.playtime.revenue.table.UserGameBannersShownTable
import com.moregames.playtime.tracking.table.GameAdUnitsRequestsEventsTable
import com.moregames.playtime.tracking.table.UserGameTimeSpentTable
import com.moregames.playtime.utils.iosPreGameScreenStub
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Instant
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import java.util.*
import kotlin.random.Random
import kotlin.test.assertEquals
import kotlin.time.Duration.Companion.seconds

@ExtendWith(DatabaseExtension::class)
class GamePersistenceServiceTest(private val database: Database) {

  private lateinit var service: GamePersistenceService
  private val timeService: TimeService = mock()
  private val now = Instant.now()

  @BeforeEach
  fun before() {
    service = GamePersistenceService(database, timeService)
    transaction(database) {
      GamesTable.update({ GamesTable.platform eq "IOS" })
      {
        it[isDisabled] = false
        it[doNotShow] = false
      }
    }
    transaction(database) {
      GameAdUnitsRequestsEventsTable.deleteAll()
    }
  }

  companion object {
    val game = gameStub.copy()
  }

  init {
    whenever(timeService.now()).thenReturn(now)
  }

  @Test
  fun `SHOULD not load disabled games ON loadGames`() {
    val gameId1 = database.prepareGame(isDisabled = true)
    val gameId2 = database.prepareGame()
    val gameId3 = database.prepareGame(isDisabled = false)

    val actual = runBlocking {
      service.loadGames(ANDROID)
    }

    actual.map { it.id }.let {
      assertThat(it).contains(gameId2)
      assertThat(it).contains(gameId3)
      assertThat(it).doesNotContain(gameId1)
    }
  }

  @Test
  fun `SHOULD include platform in game ON loadGames`() {
    val gameId1 = database.prepareGame(isDisabled = false, platform = ANDROID)

    val actual = runBlocking {
      service.loadGames(ANDROID)
    }

    actual.map { it.platform }.let {
      assertThat(it).contains(ANDROID)
      assertThat(it).doesNotContain(IOS)
    }

    actual.map { it.id }.let {
      assertThat(it).contains(gameId1)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["en", "EN"])
  fun `SHOULD not load disabled not visible or games with non matching locale ON loadVisibleGames`(locale: String) {
    val appIds = (1..3).map { UUID.randomUUID().toString() }
    val gameId1 = database.prepareGame(isDisabled = true)
    val gameId2 = database.prepareGame()
    val gameId3 = database.prepareGame(isDisabled = false)
    val gameId4 = database.prepareGame(doNotShow = true)
    val gameId5 = database.prepareGame(doNotShow = false)
    val gameId6 = database.prepareGame(applicationId = appIds[0])
    val gameId7 = database.prepareGame(applicationId = appIds[1])
    val gameId8 = database.prepareGame(applicationId = appIds[2])

    database.addGameLocaleDependency(applicationId = appIds[0], localeList = "es,pt,de") // no en
    database.addGameLocaleDependency(applicationId = appIds[1], localeList = "en") // en only
    database.addGameLocaleDependency(applicationId = appIds[2], localeList = "de,en,es") // en+

    val actual = runBlocking {
      service.loadVisibleGames(locale, ANDROID)
    }

    actual.map { it.id }.let { list ->
      assertThat(list.count { it == gameId2 }).isEqualTo(1)
      assertThat(list.count { it == gameId3 }).isEqualTo(1)
      assertThat(list.count { it == gameId5 }).isEqualTo(1)
      assertThat(list.count { it == gameId7 }).isEqualTo(1)
      assertThat(list.count { it == gameId8 }).isEqualTo(1)
      assertThat(list).doesNotContain(gameId1)
      assertThat(list).doesNotContain(gameId4)
      assertThat(list).doesNotContain(gameId6)
    }
  }

  @Test
  fun `SHOULD return ad units setup ON loadGameAdUnitIds`() {
    val packageId = "test.package"

    database.prepareGameAdUnitId(ANDROID, packageId, BANNER, "aui1", true)
    database.prepareGameAdUnitId(ANDROID, packageId, REWARDED, "aui2", true)
    database.prepareGameAdUnitId(ANDROID, packageId, INTERSTITIAL, "aui3", true)
    database.prepareGameAdUnitId(ANDROID, "another.package", INTERSTITIAL, "aui4", true)
    database.prepareGameAdUnitId(ANDROID, packageId, BANNER, "aui5", false)
    database.prepareGameAdUnitId(ANDROID, packageId, REWARDED, "aui6", false)
    database.prepareGameAdUnitId(ANDROID, packageId, INTERSTITIAL, "aui7", false)
    database.prepareGameAdUnitId(ANDROID, "another.package", INTERSTITIAL, "aui8", false)
    database.prepareGameAdUnitId(IOS, packageId, BANNER, "ios_aui1", true)
    database.prepareGameAdUnitId(IOS, packageId, REWARDED, "ios_aui2", true)
    database.prepareGameAdUnitId(IOS, packageId, INTERSTITIAL, "ios_aui3", true)

    runBlocking {
      assertThat(service.loadGameAdUnitIds(ANDROID, packageId, true)).isEqualTo(
        AdUnitIds(mapOf(BANNER to "aui1", REWARDED to "aui2", INTERSTITIAL to "aui3"))
      )
      assertThat(service.loadGameAdUnitIds(ANDROID, packageId, false)).isEqualTo(
        AdUnitIds(mapOf(BANNER to "aui5", REWARDED to "aui6", INTERSTITIAL to "aui7"))
      )
      assertThat(service.loadGameAdUnitIds(ANDROID, "another.package", false)).isNull() // data is not full
      assertThat(service.loadGameAdUnitIds(ANDROID, "significantly.another.package", false)).isNull() // no data
      assertThat(service.loadGameAdUnitIds(IOS, packageId, true)).isEqualTo(
        AdUnitIds(
          mapOf(BANNER to "ios_aui1", REWARDED to "ios_aui2", INTERSTITIAL to "ios_aui3")
        )
      )
    }
  }

  @Test
  fun `SHOULD ignore trailing space in package_id when getting game_ad_unit_ids`() {
    val packageId = "test.packagespace "

    database.prepareGameAdUnitId(ANDROID, packageId, BANNER, "aui1", true)
    database.prepareGameAdUnitId(ANDROID, packageId, REWARDED, "aui2", true)
    database.prepareGameAdUnitId(ANDROID, packageId, INTERSTITIAL, "aui3", true)

    runBlocking {
      assertThat(service.loadGameAdUnitIds(ANDROID, "test.packagespace", true)).isEqualTo(
        AdUnitIds(
          mapOf(BANNER to "aui1", REWARDED to "aui2", INTERSTITIAL to "aui3")
        )
      )
    }
  }

  @Test
  fun `SHOULD return only gimica games on loadVisibleAndroidGimicaGamesOffer`() {
    val rnd = UUID.randomUUID().toString()
    database.prepareGame(applicationId = "com.gimica.treasuremaster$rnd")
    database.prepareGame(applicationId = "com.gimica.solitaireverse$rnd")
    database.prepareGame(applicationId = "com.relaxingbraintraining.cookiejellymatch$rnd")
    database.prepareGame(applicationId = "com.gimica.madsmash$rnd")
    database.prepareGame(applicationId = "com.relaxingbraintraining.raccoonbubbles$rnd")
    database.prepareGame(applicationId = "com.gimica.zentiles$rnd")
    database.prepareGame(applicationId = "com.gimica.zentilesIos$rnd", platform = IOS)

    val actual = runBlocking {
      service.loadVisibleAndroidGimicaGamesOffer("en")
    }

    assertThat(actual.map { it.applicationId }.filter { it.contains(rnd) })
      .containsOnly("com.gimica.treasuremaster$rnd", "com.gimica.solitaireverse$rnd", "com.gimica.madsmash$rnd", "com.gimica.zentiles$rnd")
    assertThat(actual.map { it.applicationId }.all { it.contains("com.gimica") }).isTrue()
  }

  @Test
  fun `SHOULD return only gimica games on loadVisibleIosGimicaGamesOffer`() {
    val rnd = UUID.randomUUID().toString()

    database.prepareGame(applicationId = "com.gimica.treasuremaster$rnd", platform = IOS)
    database.prepareGame(applicationId = "com.gimica.solitaireverse$rnd", platform = IOS)
    database.prepareGame(applicationId = "com.relaxingbraintraining.cookiejellymatch$rnd", platform = IOS)
    database.prepareGame(applicationId = "com.gimica.madsmash$rnd", platform = IOS)
    database.prepareGame(applicationId = "com.relaxingbraintraining.raccoonbubbles$rnd", platform = IOS)
    database.prepareGame(applicationId = "com.gimica.zentiles$rnd", platform = IOS)
    database.prepareGame(applicationId = "com.gimica.zentilesAndroid$rnd")

    val actual = runBlocking {
      service.loadVisibleIosGimicaGamesOffer("en")
    }


    assertThat(actual.map { it.applicationId }.filter { it.contains(rnd) })
      .containsOnly("com.gimica.treasuremaster$rnd", "com.gimica.solitaireverse$rnd", "com.gimica.madsmash$rnd", "com.gimica.zentiles$rnd")
    assertThat(actual.map { it.applicationId }.all { it.contains("com.gimica") }).isTrue()
  }

  @Test
  fun `SHOULD return correct marblemadness ad unit ids`() {
    var actual = runBlocking { service.loadGameAdUnitIds(ANDROID, "com.gimica.marblemadness", true) }
    var expected = AdUnitIds(
      mapOf(BANNER to "97cdfb68794d1405", REWARDED to "ba5580d3c304ae14", INTERSTITIAL to "9cd4ec52863ba068")
    )
    assertThat(actual).isEqualTo(expected)

    actual = runBlocking { service.loadGameAdUnitIds(ANDROID, "com.gimica.marblemadness", false) }
    expected = AdUnitIds(
      mapOf(BANNER to "066297da4b5d45b3", REWARDED to "14d9a65f62b1a56a", INTERSTITIAL to "83d07f01c9f082b9")
    )
    assertThat(actual).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return correct com_gimica_sudoku ad unit ids`() {
    var actual = runBlocking { service.loadGameAdUnitIds(ANDROID, "com.gimica.sudoku", true) }
    var expected = AdUnitIds(
      mapOf(BANNER to "3f3314056fd3f688", REWARDED to "d5cf0797407498ab", INTERSTITIAL to "57aef79f86881766")
    )
    assertThat(actual).isEqualTo(expected)

    actual = runBlocking { service.loadGameAdUnitIds(ANDROID, "com.gimica.sudoku", false) }
    expected = AdUnitIds(
      mapOf(BANNER to "54690e23e69bdc83", REWARDED to "c42743e04596078b", INTERSTITIAL to "a7badec311f2832a")
    )
    assertThat(actual).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return correct com_gimica_brickdoku ad unit ids`() {
    var actual = runBlocking { service.loadGameAdUnitIds(ANDROID, "com.gimica.brickdoku", true) }
    var expected = AdUnitIds(
      mapOf(BANNER to "3db9beab33a863ed", REWARDED to "869c58ede3d2a55d", INTERSTITIAL to "4da22a13c6b5e21c")
    )
    assertThat(actual).isEqualTo(expected)

    actual = runBlocking { service.loadGameAdUnitIds(ANDROID, "com.gimica.brickdoku", false) }
    expected = AdUnitIds(
      mapOf(BANNER to "ee1f10ee92cca3bf", REWARDED to "c110a39c99121f1f", INTERSTITIAL to "304cc0f578a5676f")
    )
    assertThat(actual).isEqualTo(expected)
  }

  @Test
  fun `SHOULD correctly load show_for_lat field ON loadVisibleGames`() {
    val game1 = database.prepareGame()
    val game2 = database.prepareGame(showForLat = false)
    val game3 = database.prepareGame(showForLat = true)

    runBlocking { service.loadVisibleGames("en", ANDROID) }.let { actual ->
      assertThat(actual.find { it.id == game1 }!!.showForLat).isFalse()
      assertThat(actual.find { it.id == game2 }!!.showForLat).isFalse()
      assertThat(actual.find { it.id == game3 }!!.showForLat).isTrue()
    }
  }

  @Test
  fun `SHOULD correctly load iOS games ON loadVisibleGames`() {
    runBlocking { service.loadVisibleGames("en", IOS) }.let { actual ->
      with(actual[0]) {
        assertEquals("com.gimica.madsmash", applicationId)
        assertEquals(500001, orderKey)
      }
      with(actual[1]) {
        assertEquals("com.gimica.solitaireverse", applicationId)
        assertEquals(500002, orderKey)
      }
      with(actual[2]) {
        assertEquals("com.gimica.zentiles", applicationId)
        assertEquals(500003, orderKey)
      }
      with(actual[3]) {
        assertEquals("com.gimica.bubblepop", applicationId)
        assertEquals(500004, orderKey)
      }
      with(actual[4]) {
        assertEquals("com.gimica.treasuremaster", applicationId)
        assertEquals(500005, orderKey)
      }
    }
  }

  @Test
  fun `SHOULD fetch demo_game_url ON loadVisibleGames`() {
    transaction(database) {
      GamesTable.update({ GamesTable.id eq 200044 })
      {
        it[isDisabled] = false
        it[doNotShow] = false
      }
    }
    runBlocking { service.loadVisibleGames("en", ANDROID) }.let { actual ->
      with(actual.find { it.id == 200039 }!!) {
        assertEquals("com.gimica.treasuremaster", applicationId)
        assertEquals(
          expected = "https://games-cdn.justplayapi.com/treasure-master/android/release/index.html?packageId=com.gimica.treasuremaster.webgl.demo",
          actual = demoGameUrl
        )
      }
      with(actual.find { it.id == 200044 }!!) {
        assertEquals("com.gimica.solitaireverse", applicationId)
        assertEquals(
          expected = "https://games-cdn.justplayapi.com/solitaire-verse/android/release/index.html?packageId=com.gimica.solitaireverse.webgl.demo",
          actual = demoGameUrl
        )
      }
    }
  }

  @Test
  fun `SHOULD load visible games by ids ON loadVisibleIosGamesByIds`() {
    val actual = runBlocking {
      service.loadVisibleIosGamesByIds(setOf(500001))
    }

    assertEquals(1, actual.size)
    with(actual[0]) {
      assertEquals(500001, id)
      assertEquals("com.gimica.treasuremaster", applicationId)
      assertEquals("Treasure Masters", name)
      assertEquals("\$_ios_treasuremaster_description", description)
      assertEquals("treasure_master.png", iconFilename)
      assertEquals("treasure_master_preview.jpg", imageFilename)
      assertEquals("\$_ios_text_install_top", infoTextInstall)
      assertEquals("id6444407009", iosApplicationId)
      assertEquals("LaunchTreasureMasters:", iosGameUrl)
    }
  }

  @Test
  fun `SHOULD load games by ids ON loadAndroidGamesByIds`() {
    val result = runBlocking {
      service.loadAndroidGamesByIds(setOf(200039, 200040, 200074))
    }

    assertEquals(3, result.size)
    with(result.first { it.id == 200039 }) {
      assertEquals(200039, id)
      assertEquals("com.gimica.treasuremaster", applicationId)
      assertEquals("Treasure Master", name)
      assertEquals("com.unity3d.player.UnityPlayerActivity", activityName)
      assertEquals("\$_treasure_master_description", description)
      assertEquals("treasure_master.png", iconFilename)
      assertEquals("tm_preview_image_only_v1.jpg", imageFilename)
      assertEquals(1, orderKey)
      assertEquals("gimica-api-key", applovinApiKey)
      assertEquals("install_image_20230504.jpg", installImageFilename)
      assertEquals("games/video_preview/TreasureMaster.mp4", videoPreviewFilename)
      assertEquals("\$_treasure_master_text_install_top", infoTextInstallTop)
      assertEquals("\$_treasure_master_text_install_bottom", infoTextInstallBottom)
      assertEquals(null, expImageFilename)
      assertEquals(null, backGroundColor)
      assertEquals(4, publisherId)
      assertEquals(true, showForLat)
      assertEquals(false, doNotShow)
    }

    with(result.first { it.id == 200074 }) {
      assertEquals(false, doNotShow)
    }

  }

  @Test
  fun `SHOULD load visible games by application ids ON loadVisibleIosGamesByApplicationIds`() {
    val actual = runBlocking {
      service.loadVisibleIosGamesByApplicationIds(setOf("com.gimica.treasuremaster"))
    }

    assertEquals(1, actual.size)
    with(actual[0]) {
      assertEquals(500001, id)
      assertEquals("com.gimica.treasuremaster", applicationId)
      assertEquals("Treasure Masters", name)
      assertEquals("\$_ios_treasuremaster_description", description)
      assertEquals("treasure_master.png", iconFilename)
      assertEquals("treasure_master_preview.jpg", imageFilename)
      assertEquals("\$_ios_text_install_top", infoTextInstall)
      assertEquals("id6444407009", iosApplicationId)
      assertEquals("LaunchTreasureMasters:", iosGameUrl)
    }
  }

  @Test
  fun `SHOULD load visible games exclude ids ON loadVisibleGamesExcludeIds`() {
    val actual = runBlocking {
      service.loadVisibleIosGamesExcludeIds(
        setOf(500001, 500003, 500004, 500005, 500006, 500007, 500008, 500009, 500010, 500011, 500012, 500013, 500014, 500014, 500015, 500016)
      )
    }

    assertEquals(1, actual.size)
    with(actual[0]) {
      assertEquals(500002, id)
      assertEquals("com.gimica.solitaireverse", applicationId)
      assertEquals("Solitaire Verse", name)
      assertEquals("\$_ios_solitaireverse_description", description)
      assertEquals("solitaire_verse_green.jpg", iconFilename)
      assertEquals("solitaire_verse_preview.jpg", imageFilename)
      assertEquals("\$_ios_text_install_top", infoTextInstall)
      assertEquals("id1667538256", iosApplicationId)
      assertEquals("LaunchSolitaireVerse:", iosGameUrl)
    }
  }

  @Test
  fun `Mandatory iOS games values should not be empty`() {
    runBlocking {
      service.loadGames(IOS)
    }
      .filterNot { it.applicationId == "com.gimica.justplay" }
      .let { games ->
        assertThat(games.all { game -> game.iosApplicationId != null && game.iosGameUrl != null }).isTrue()
        assertThat(games.filter { it.webglUrl != null && it.demoGameUrl == null }.size).isEqualTo(7)
      }
  }

  @Test
  fun `SHOULD load iOS games with adjust links WHEN we have some`() {
    transaction(database) {
      GamesWebAppTable.insert {
        it[GamesWebAppTable.gameId] = 500001
        it[GamesWebAppTable.adjustLink] = "https://treasuremaster.go.link?adj_t=1p2tji1r&user_id={userId}"
      }
      GamesWebAppTable.insert {
        it[GamesWebAppTable.gameId] = 500002
        it[GamesWebAppTable.adjustLink] = "https://solitaireverse.go.link?adj_t=2p2tji1r&user_id={userId}"
      }
    }

    val result = runBlocking {
      service.loadGames(IOS)
    }
    result.first { it.id == 500001 }
      .let { assertThat(it.webAppAdjustLink).isEqualTo("https://treasuremaster.go.link?adj_t=1p2tji1r&user_id={userId}") }
    result.first { it.id == 500002 }
      .let { assertThat(it.webAppAdjustLink).isEqualTo("https://solitaireverse.go.link?adj_t=2p2tji1r&user_id={userId}") }
    result.first { it.id == 500003 }
      .let { assertThat(it.webAppAdjustLink).isNull() }
  }

  @ParameterizedTest
  @CsvSource(
    "true,true",
    "true,false",
    "false,true",
    "false,false",
  )
  fun `SHOULD load new games ON loadNewGames ON loadVisibleGamesByIds WHEN different visibilities`(visible: Boolean, enabled: Boolean) {
    //disabling other IOS games for simplify assert
    transaction(database) {
      GamesTable.update({ (GamesTable.id neq 500001) and (GamesTable.platform eq "IOS") })
      {
        it[isDisabled] = true
      }
    }
    transaction(database) {
      GamesTable.update({ (GamesTable.id eq 500001) and (GamesTable.platform eq "IOS") })
      {
        it[isDisabled] = !enabled
        it[doNotShow] = !visible
      }
    }

    val actual = runBlocking {
      service.loadVisibleIosGamesByIds(setOf(500001, 500002))
    }

    assertEquals(if (visible && enabled) 1 else 0, actual.size)
  }

  @ParameterizedTest
  @CsvSource(
    "true,true",
    "true,false",
    "false,true",
    "false,false",
  )
  fun `SHOULD load played games ON loadPlayedGames ON loadVisibleGamesExcludeIds WHEN different visibilities`(visible: Boolean, enabled: Boolean) {
    //disabling other IOS games for simplify assert
    transaction(database) {
      GamesTable.update({ (GamesTable.id neq 500002) and (GamesTable.platform eq "IOS") })
      {
        it[isDisabled] = true
      }
    }
    transaction(database) {
      GamesTable.update({ (GamesTable.id eq 500002) and (GamesTable.platform eq "IOS") })
      {
        it[isDisabled] = !enabled
        it[doNotShow] = !visible
      }
    }

    val actual = runBlocking {
      service.loadVisibleIosGamesExcludeIds(setOf(500001))
    }

    assertEquals(if (visible && enabled) 1 else 0, actual.size)
  }

  @Test
  fun `SHOULD return null ON getGameIdByApplicationId WHEN there is no such game`() {
    database.prepareGame(gameStub.copy(applicationId = "com.cocomagic.merger"))

    runBlocking {
      service.getGameIdByApplicationId("com.cocomagic.slideblock", ANDROID)
    }.let { actual ->
      assertThat(actual).isNull()
    }
  }

  @Test
  fun `SHOULD return game id ON getGameIdByApplicationId`() {
    val gameId = database.prepareGame(gameStub.copy(applicationId = "com.cocomagic.slideblock"))

    runBlocking {
      service.getGameIdByApplicationId("com.cocomagic.slideblock", ANDROID)
    }.let { actual ->
      assertThat(actual).isEqualTo(gameId)
    }
  }

  @Test
  fun `SHOULD return ios preGame screens ON loadIosPreGameScreens`() {
    runBlocking { service.loadIosPreGameScreens() }.let {
      assertEquals(14, it.size)
      assertEquals(iosPreGameScreenStub[500001], it[500001])
      assertEquals(iosPreGameScreenStub[500002], it[500002])
      assertEquals(iosPreGameScreenStub[500006], it[500006])
      assertEquals(iosPreGameScreenStub[500014], it[500014])
      assertEquals(iosPreGameScreenStub[500015], it[500015])
    }
  }

  @Test
  fun `SHOULD return correct iOS com_gimica_watersorter ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "41823ce8fcc73658",
          REWARDED to "a49332869716a70b",
          INTERSTITIAL to "6288dd0d43918c3b"
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.watersorter", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "9c6a86139c8b1bb2",
          REWARDED to "3fff986ff0eb9fed",
          INTERSTITIAL to "05a4cd294c9d42f1"
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.watersorter", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct com_gimica_spaceconnect ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "7c94e64c2f53d79c",
          REWARDED to "b99efa65b0894401",
          INTERSTITIAL to "4153d2d8ba7f36dd"
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.spaceconnect", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "5973bc3da32d01eb",
          REWARDED to "5193d1d4dd89f928",
          INTERSTITIAL to "578952a188f392a5"
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.spaceconnect", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_gimica_sugarmatch ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "ab52d28dd3728cb6",
          REWARDED to "41b3b36dd1b6eea9",
          INTERSTITIAL to "19d3bdd02cd238b9",
          INTERSTITIAL_HIGH to "2857247079fcdb01",
          INTERSTITIAL_MEDIUM to "06a4d03648e2fe9e",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.sugarmatch", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "2a007b7c1d19d724",
          REWARDED to "dc2873ed14a4b4c5",
          INTERSTITIAL to "b97188c1b55c2fb4",
          INTERSTITIAL_HIGH to "a7cea7a7f257a29e",
          INTERSTITIAL_MEDIUM to "9e41f2148d590b45",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.sugarmatch", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct IOS com_gimica_sugarmatch ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "50bb60983970f1de",
          REWARDED to "930db3aabc0a8506",
          INTERSTITIAL to "57456223701a3c92",
          INTERSTITIAL_HIGH to "373a7e56b759d38b",
          INTERSTITIAL_MEDIUM to "416459f9f4412b76",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.sugarmatch", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "104f26c88a73559e",
          REWARDED to "d440bb15e359420d",
          INTERSTITIAL to "e417a553cea0f38b",
          INTERSTITIAL_HIGH to "70d0b20b287fe2b9",
          INTERSTITIAL_MEDIUM to "4dda1b0a6e9b9209",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.sugarmatch", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct IOS com_gimica_fairytalematch ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "c761356449ae2ee7",
          REWARDED to "75650480cf818f8f",
          INTERSTITIAL to "cd4fe58f53ab91bf"
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.fairytalematch", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "aa383e76484be533",
          REWARDED to "67cbaff99d13453f",
          INTERSTITIAL to "976db61b08cc899e"
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.fairytalematch", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_gimica_tangram ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "ee28f0f718756c23",
          REWARDED to "af5c3ec7bc698e56",
          INTERSTITIAL to "226e42c1c5603b0e"
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.tangram", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "aa3605384724fe6a",
          REWARDED to "6a2980c9938eaa98",
          INTERSTITIAL to "71678bc1c2143182"
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.tangram", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct iOS com_gimica_puzzlepopblaster ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "3d526823652b4ee5",
          REWARDED to "d88f06c211090dd5",
          INTERSTITIAL to "5f7498be3fb77238"
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.puzzlepopblaster", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "9e234cfcf381a472",
          REWARDED to "b1254f2143482883",
          INTERSTITIAL to "2c25a8393430e567"
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.puzzlepopblaster", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct iOS com_gimica_tilematchpro ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "8c41179a8b7f110d",
          REWARDED to "db2afc99f7940f23",
          INTERSTITIAL to "b63b9a7d6755f09b"
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.tilematchpro", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "218d0c9672a14de7",
          REWARDED to "cba6cd7f7ad26865",
          INTERSTITIAL to "0f86ddb77ece8b49"
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.tilematchpro", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct iOS com_gimica_justplay ad unit ids`() {
    val expected = AdUnitIds(
      mapOf(
        BANNER to "1cb72efd7a22989d",
        REWARDED to "5a478b01441a9fe5",
        INTERSTITIAL to "cf3c3f61f45f6da4"
      )
    )
    assertEquals(expected, runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.justplay", isHighlyTrustedUser = false) })
    assertEquals(expected, runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.justplay", isHighlyTrustedUser = true) })
  }

  @Test
  fun `SHOULD return correct iOS com_justplay_app ad unit ids`() {
    val expected = AdUnitIds(
      mapOf(
        BANNER to "1cb72efd7a22989d",
        REWARDED to "5a478b01441a9fe5",
        INTERSTITIAL to "cf3c3f61f45f6da4"
      )
    )
    assertEquals(expected, runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.justplay.app", isHighlyTrustedUser = false) })
    assertEquals(expected, runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.justplay.app", isHighlyTrustedUser = true) })
  }

  @Test
  fun `SHOULD return correct Android com_justplay_app ad unit ids`() {
    val expected = AdUnitIds(
      mapOf(
        BANNER to "1cb72efd7a22989d",
        REWARDED to "8b69d8e39d5b25a1",
        INTERSTITIAL to "8409660f06628c06"
      )
    )
    assertEquals(expected, runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.justplay.app", isHighlyTrustedUser = false) })
    assertEquals(expected, runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.justplay.app", isHighlyTrustedUser = true) })
  }

  @Test
  fun `SHOULD return correct Android com_gimica_solitaire ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "2c55c8d278f94a08",
          REWARDED to "387e9afa5936348c",
          INTERSTITIAL to "588dfc7f21ce670f"
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.solitaire", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "0603d95e29a031ed",
          REWARDED to "a7685c58c6b615a5",
          INTERSTITIAL to "e644d7232e976577"
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.solitaire", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_gimica_spidersolitaire ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "660d208b8aa0e8de",
          REWARDED to "29c8fbed254543fd",
          INTERSTITIAL to "70664a493b583a84"
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.spidersolitaire", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "156df03bcc193226",
          REWARDED to "e5845fe528a5c582",
          INTERSTITIAL to "f1b49934e74a1062"
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.spidersolitaire", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_gimica_blockbuster ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "db368e5e0c30f261",
          REWARDED to "19f16d374607fdf3",
          INTERSTITIAL to "495bd9e05e37b3da"
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.blockbuster", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "83e4faabb80244e9",
          REWARDED to "8b98ca4e2371f11a",
          INTERSTITIAL to "3ccb371dca60283d"
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.blockbuster", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_gimica_madsmash ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "a0c0aec657883750",
          REWARDED to "893e6843177731ee",
          INTERSTITIAL to "97e25053b50cd326",
          INTERSTITIAL_HIGH to "a21ad0fc505438fd",
          INTERSTITIAL_MEDIUM to "e71c77b88bae12a9",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.madsmash", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "e1fd06ee55d4b533",
          REWARDED to "599033c1c38c1472",
          INTERSTITIAL to "f50ebccbafada334",
          INTERSTITIAL_HIGH to "44febc28368d1d90",
          INTERSTITIAL_MEDIUM to "2f8a1e8c7c454824",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.madsmash", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct iOS com_gimica_madsmash ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "20d9b996f76c05f2",
          REWARDED to "69a8e1bb0ad91a90",
          INTERSTITIAL to "750c20c818abe5a3",
          INTERSTITIAL_HIGH to "e6cac7c80b0d7204",
          INTERSTITIAL_MEDIUM to "f57d3d48d60ec548",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.madsmash", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "54cf63abcf1145d8",
          REWARDED to "7cbc13c8b18dec5e",
          INTERSTITIAL to "9072e026d9c64d64",
          INTERSTITIAL_HIGH to "ade4b490aacbb069",
          INTERSTITIAL_MEDIUM to "07fd902ce59551e6",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.madsmash", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_gimica_solitaireverse ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "4599c9e4f9a241bc",
          REWARDED to "df6ca3c88c8148f7",
          INTERSTITIAL to "396de120011b4732",
          INTERSTITIAL_HIGH to "2da4c0027188e86f",
          INTERSTITIAL_MEDIUM to "bbb8e13cee1035f1",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.solitaireverse", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "1369996c1036db24",
          REWARDED to "447429ee53770849",
          INTERSTITIAL to "43fb869cbfb03e6e",
          INTERSTITIAL_HIGH to "d69ee18b8984ec50",
          INTERSTITIAL_MEDIUM to "8aab6f664341f6aa",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.solitaireverse", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct iOS com_gimica_solitaireverse ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "2e8e150fccb2b029",
          REWARDED to "17cf20259d867f67",
          INTERSTITIAL to "49f5ce97ea9bfa91",
          INTERSTITIAL_HIGH to "43d3c3d877bded55",
          INTERSTITIAL_MEDIUM to "524be51ba9e7d403",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.solitaireverse", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "161bad12a74e91f8",
          REWARDED to "07d34a4f33280c85",
          INTERSTITIAL to "8b2318a8b715e610",
          INTERSTITIAL_HIGH to "4980ac29fe9e81e3",
          INTERSTITIAL_MEDIUM to "b49143436090e648",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.solitaireverse", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_gimica_treasuremaster ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "32a6b6ea7c5dfad6",
          REWARDED to "d5afe82db7dce762",
          INTERSTITIAL to "2cc5af3f559f24b0",
          INTERSTITIAL_HIGH to "f91aceb387ea01d8",
          INTERSTITIAL_MEDIUM to "a7ecbd8d18d992f3",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.treasuremaster", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "99972e6ba827fb96",
          REWARDED to "e291142c50d4000c",
          INTERSTITIAL to "d679e69e2fd589ae",
          INTERSTITIAL_HIGH to "633e9f33440a6b1c",
          INTERSTITIAL_MEDIUM to "24257516b02eff81",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.treasuremaster", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct iOS com_gimica_treasuremaster ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "9fffcde0bcadb962",
          REWARDED to "9d01dfc7335d3df3",
          INTERSTITIAL to "af298c4576e031f6",
          INTERSTITIAL_HIGH to "0913478f69b5dfa0",
          INTERSTITIAL_MEDIUM to "35e66d4533086453",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.treasuremaster", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "477c4b61d88a4f93",
          REWARDED to "7b8406799686f293",
          INTERSTITIAL to "ba1e840c8722d14f",
          INTERSTITIAL_HIGH to "cc33807c4e5d7e4a",
          INTERSTITIAL_MEDIUM to "0dd623d4ed493094",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.treasuremaster", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_gimica_ballbounce ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "6eed3d5c0d859086",
          REWARDED to "5f0edc015f8f167c",
          INTERSTITIAL to "1087af393e7ddb2b",
          INTERSTITIAL_HIGH to "25c704e4a6826ac9",
          INTERSTITIAL_MEDIUM to "a2d332e51aea5feb",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.ballbounce", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "113f0b1ed70dc747",
          REWARDED to "d75fc015e2dc0168",
          INTERSTITIAL to "8f0877c1bba3ca43",
          INTERSTITIAL_HIGH to "ab0c6231c300f8e6",
          INTERSTITIAL_MEDIUM to "772160393f16c9df",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.ballbounce", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct iOS com_gimica_ballbounce ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "1ea0109424044d2a",
          REWARDED to "fdbc623308225438",
          INTERSTITIAL to "a50b6acb50502cf9",
          INTERSTITIAL_HIGH to "7956d9d8c29024bc",
          INTERSTITIAL_MEDIUM to "2d945b0e811471b8",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.ballbounce", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "d9823b872747c7da",
          REWARDED to "dc60e25a6f896a1e",
          INTERSTITIAL to "f1ee9d84014147fc",
          INTERSTITIAL_HIGH to "67e2c25ddad7d0d1",
          INTERSTITIAL_MEDIUM to "eb15e8c224c30351",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.ballbounce", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_gimica_zentiles ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "0981ca7106765af2",
          REWARDED to "e4c1bae5d104dcd0",
          INTERSTITIAL to "1cb72efd6a11979d",
          INTERSTITIAL_HIGH to "3ddb01e0693d692f",
          INTERSTITIAL_MEDIUM to "23e119fb8e065c8e",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.zentiles", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "1a7f62f07b6c258c",
          REWARDED to "f2c91588faf629e7",
          INTERSTITIAL to "938e0fad0ed9c141",
          INTERSTITIAL_HIGH to "45ef6b9279298416",
          INTERSTITIAL_MEDIUM to "329ac80843936e31",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.zentiles", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct iOS com_gimica_zentiles ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "295de58d7cfccb54",
          REWARDED to "d30cb238a9ec4aed",
          INTERSTITIAL to "07d79d259d8f8b42",
          INTERSTITIAL_HIGH to "2cc3fa36cd55465d",
          INTERSTITIAL_MEDIUM to "3cd0c4a7b22c831e",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.zentiles", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "2bc3fe9e2236c72e",
          REWARDED to "d1538288dcdd05df",
          INTERSTITIAL to "f4d4040795aa0ad8",
          INTERSTITIAL_HIGH to "b8a97a45ce87cff3",
          INTERSTITIAL_MEDIUM to "7e168c6d88289d20",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = IOS, packageId = "com.gimica.zentiles", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_gimica_atlantis ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "19df1e9d7e2340b7",
          INTERSTITIAL to "e032251ff1e4d94d",
          REWARDED to "8bef2cd99a8eba32",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.atlantis", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "0a57e50b068f7c09",
          INTERSTITIAL to "7ca399ad8c8ca349",
          REWARDED to "949dee1612e2dec4",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.atlantis", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_bubblechef_bubbleshooter ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "12a1f432c8e099cc",
          INTERSTITIAL to "900d2580f0efe033",
          REWARDED to "ffaf3a0641e34d3a",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.bubblechef.bubbleshooter", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "86fad4e45a5fa3ab",
          INTERSTITIAL to "26a88c858be5e1fa",
          REWARDED to "46349ed84015ec7f",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.bubblechef.bubbleshooter", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_forevergreen_atlantis ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "33ff1bd0a6071cf2",
          INTERSTITIAL to "9ccecf6151045e8a",
          REWARDED to "2d1dd1481ed80739",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.forevergreen.atlantis", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "d6026554035f3a64",
          INTERSTITIAL to "4943ad83642e99c8",
          REWARDED to "440a6faf17915f23",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.forevergreen.atlantis", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_forevergreen_solitaire ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "2722a32fe48a065f",
          INTERSTITIAL to "039f608e62ea6638",
          REWARDED to "a8dc01c2021c8b07",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.forevergreen.solitaire", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "34e98aa3334d95f1",
          INTERSTITIAL to "79e4939b054edcc3",
          REWARDED to "48b26e8690edbc9f",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.forevergreen.solitaire", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_gimica_threadform ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "072f20b4113c02f6",
          INTERSTITIAL to "6b5ca71c068631ad",
          REWARDED to "5da4ebf0d70b8152",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.threadform", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "1a7d00780bf5a792",
          INTERSTITIAL to "4de944bec2db0109",
          REWARDED to "a1d810d28f1821b9",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.threadform", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_gimica_hexadrop ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "aec5e9e5202a312c",
          INTERSTITIAL to "433cc660ddfdb054",
          REWARDED to "e095841902c86070",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.hexadrop", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "dd221cf00c99cce2",
          INTERSTITIAL to "ce24a766de2b0202",
          REWARDED to "183b751b666cfd18",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.hexadrop", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_gimica_dontpop ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "865fafb40615639d",
          INTERSTITIAL to "2f75b8be4277573c",
          REWARDED to "739679b189835ac1",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.dontpop", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "78f006aedccd434a",
          INTERSTITIAL to "fafe08fb87d7755f",
          REWARDED to "26b58d381c88e729",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.dontpop", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_gimica_oneline ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "4d419705798ed9f6",
          INTERSTITIAL to "7805b4e702a1e7fd",
          REWARDED to "3c1f0a6ad162811d",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.oneline", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "b9e38c3440568043",
          INTERSTITIAL to "ef50416a5112fb33",
          REWARDED to "ccb5dc8718117530",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.oneline", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_gimica_aeroescape ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "c2fec995841e35fc",
          INTERSTITIAL to "accc7989012c7539",
          REWARDED to "80baf3cb8dde2e34",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.aeroescape", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "9923615d538a7d2c",
          INTERSTITIAL to "bb5c330319570529",
          REWARDED to "1776f40ba8f7f139",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.aeroescape", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_gimica_slamdunk ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "b9e34beb135a34e2",
          INTERSTITIAL to "0a2c84536e3a912e",
          REWARDED to "e63fbefd27f58ab5",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.slamdunk", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "4f5b3f827e518031",
          INTERSTITIAL to "b19a03a3b1bd16a2",
          REWARDED to "c20caeee77691ee0",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.slamdunk", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_gimica_slicepuzzle ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "fa1e1aff854e1f29",
          INTERSTITIAL to "e47cb15c13c4aa39",
          REWARDED to "6da5a66c3dba4263",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.slicepuzzle", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "70ba09f4e193c363",
          INTERSTITIAL to "68f5d802bc27b7b5",
          REWARDED to "f28646218b3b98bb",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.slicepuzzle", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_gimica_slideandroll ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "3d3ca60d27846bda",
          INTERSTITIAL to "10090ebc34d8c5c5",
          REWARDED to "e92be5443651c335",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.slideandroll", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "976e34a8ff13207a",
          INTERSTITIAL to "e07e26fca87caa70",
          REWARDED to "1187df806617d046",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.gimica.slideandroll", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD return correct Android com_forevergreen_foodblast ad unit ids`() {
    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "a8172a4e6f3dfe05",
          INTERSTITIAL to "5c72d0483692c51a",
          REWARDED to "7ef281e77b0e0431",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.forevergreen.foodblast", isHighlyTrustedUser = true) }
    )

    assertEquals(
      AdUnitIds(
        mapOf(
          BANNER to "7bac4d6121db8ff2",
          INTERSTITIAL to "d55d19971142e06e",
          REWARDED to "351c5d84aeef1712",
        )
      ),
      runBlocking { service.loadGameAdUnitIds(platform = ANDROID, packageId = "com.forevergreen.foodblast", isHighlyTrustedUser = false) }
    )
  }

  @Test
  fun `SHOULD compute diff between first_shown_at and updated_at and return it ON trackAndCountBannersShown`() {
    val userId = database.prepareUser()
    val gameId = database.prepareGame()
    transaction(database) {
      UserGameBannersShownTable.insert {
        it[UserGameBannersShownTable.userId] = userId
        it[UserGameBannersShownTable.gameId] = gameId
        it[firstShownAt] = now.minusSeconds(1)
        it[updatedAt] = now.minusSeconds(1)
      }
    }
    runBlocking { service.trackAndComputeBannersInterval(userId, gameId) }
      .let { assertThat(it).isBetween(0, 1) }

    transaction(database) {
      UserGameBannersShownTable
        .slice(UserGameBannersShownTable.firstShownAt)
        .select { (UserGameBannersShownTable.userId eq userId) and (UserGameBannersShownTable.gameId eq gameId) }
        .first()[UserGameBannersShownTable.firstShownAt]
        .let { assertThat(it).isBetween(now.minusSeconds(2), now) }
    }
  }

  @Test
  fun `SHOULD return user games in order of first banner show ON getGamesOrderedByFirstTimeBannerShow`() {
    val userId = database.prepareUser()
    val timeNow = now.truncatedTo(ChronoUnit.SECONDS)
    val gamesPlayed = listOf(
      database.prepareGame() to timeNow.minusSeconds(1),
      database.prepareGame() to timeNow.minusSeconds(2),
      database.prepareGame() to timeNow.minusSeconds(3),
      database.prepareGame() to timeNow.minusSeconds(2),
      database.prepareGame() to timeNow.minusSeconds(1),
    ).also {
      transaction(database) {
        UserGameBannersShownTable.batchInsert(it) {
          this[UserGameBannersShownTable.userId] = userId
          this[UserGameBannersShownTable.gameId] = it.first
          this[UserGameBannersShownTable.bannersShown] = 1 + Random.nextInt(3)
          this[UserGameBannersShownTable.firstShownAt] = it.second
          this[UserGameBannersShownTable.updatedAt] = it.second
        }
      }
    }

    runBlocking { service.getGamesOrderedByFirstTimeBannerShow(userId) }
      .let { actual ->
        assertThat(actual[0]).isEqualTo(gamesPlayed[2])
        assertThat(actual[1]).isEqualTo(gamesPlayed[1])
        assertThat(actual[2]).isEqualTo(gamesPlayed[3])
        assertThat(actual[3]).isEqualTo(gamesPlayed[0])
        assertThat(actual[4]).isEqualTo(gamesPlayed[4])
      }
  }

  @Test
  fun `SHOULD track GameAdUnitsRequestsEvents ON trackGameAdUnitsRequestedEventReturnSuccess`() {
    val userId = database.prepareUser()
    val applicationId = UUID.randomUUID().toString()

    runBlocking {
      service.trackGameAdUnitsRequestedEventReturnSuccess(userId, applicationId)
    }.let { assertThat(it).isTrue() }

    transaction(database) {
      GameAdUnitsRequestsEventsTable
        .slice(GameAdUnitsRequestsEventsTable.applicationId)
        .select {
          (GameAdUnitsRequestsEventsTable.userId eq userId) and
            (GameAdUnitsRequestsEventsTable.applicationId eq applicationId)
        }.firstOrNull().let { assertThat(it).isNotNull() }
    }
  }

  @Test
  fun `SHOULD not track GameAdUnitsRequestsEvents ON trackGameAdUnitsRequestedEventReturnSuccess WHEN it was tracked before`() {
    val userId = database.prepareUser()
    val applicationId = UUID.randomUUID().toString()

    transaction(database) {
      GameAdUnitsRequestsEventsTable.insert {
        it[GameAdUnitsRequestsEventsTable.userId] = userId
        it[GameAdUnitsRequestsEventsTable.applicationId] = applicationId
      }
    }

    runBlocking {
      service.trackGameAdUnitsRequestedEventReturnSuccess(userId, applicationId)
    }.let { assertThat(it).isFalse() }
  }

  @Test
  fun `SHOULD return false ON isGameAdUnitsEverRequested WHEN NOT ad units requested`() {
    val userId = database.prepareUser()

    runBlocking { service.isGameAdUnitsEverRequested(userId) }.let { actual ->
      assertThat(actual).isFalse()
    }
  }

  @Test
  fun `SHOULD return true ON isGameAdUnitsEverRequested WHEN ad units requested`() {
    val userId = database.prepareUser()
    val applicationId = UUID.randomUUID().toString()

    transaction(database) {
      GameAdUnitsRequestsEventsTable.insert {
        it[GameAdUnitsRequestsEventsTable.userId] = userId
        it[GameAdUnitsRequestsEventsTable.applicationId] = applicationId
      }
    }

    runBlocking { service.isGameAdUnitsEverRequested(userId) }.let { actual ->
      assertThat(actual).isTrue()
    }
  }

  @Test
  fun `SHOULD return time spent on getTimeSpent`() {
    val userId = database.prepareUser()
    val userId2 = database.prepareUser()
    val games = (1..3).map { database.prepareGame() }
    runBlocking {
      service.getTimeSpent(userId, games[0])
    }.let { assertThat(it).isEqualTo(0.seconds) }

    transaction(database) {
      UserGameTimeSpentTable
        .insert {
          it[UserGameTimeSpentTable.userId] = userId
          it[gameId] = games[1]
          it[timeSpentSeconds] = 10500
        }
    }

    runBlocking {
      service.getTimeSpent(userId, games[1])
    }.let { assertThat(it).isEqualTo(10500L.seconds) }

    runBlocking {
      service.getTimeSpent(userId2, games[0])
    }.let { assertThat(it).isEqualTo(0.seconds) }
  }

  @Test
  fun `SHOULD track time spent on trackTimeSpent`() {
    val userId = database.prepareUser()
    val userId2 = database.prepareUser()
    val games = (1..3).map { database.prepareGame() }

    runBlocking {
      service.trackTimeSpent(userId, games[1], 10500)
      service.trackTimeSpent(userId, games[0], 5)
      service.trackTimeSpent(userId, games[0], 15)

      service.getTimeSpent(userId2, games[0])
        .let { assertThat(it).isEqualTo(0.seconds) }

      service.getTimeSpent(userId, games[0])
        .let { assertThat(it).isEqualTo(20L.seconds) }
      service.getTimeSpent(userId, games[1])
        .let { assertThat(it).isEqualTo(10500L.seconds) }
    }
  }

  @Test
  fun `SHOULD return zero duration ON getTimeSpent_userId WHEN no data`() {
    val userId = database.prepareUser()

    runBlocking { service.getTimeSpent(userId) }.let { actual ->
      assertThat(actual).isEqualTo(kotlin.time.Duration.ZERO)
    }
  }

  @Test
  fun `SHOULD return total spent duration ON getTimeSpent_userId`() {
    val userId = database.prepareUser()
    val userIdOther = database.prepareUser()
    val gameId1 = database.prepareGame()
    val gameId2 = database.prepareGame()

    database.addTimeSpent(userId, gameId1, 314)
    database.addTimeSpent(userId, gameId2, 271)
    database.addTimeSpent(userIdOther, gameId1, 602)

    runBlocking { service.getTimeSpent(userId) }.let { actual ->
      assertThat(actual).isEqualTo((314 + 271).seconds)
    }
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD find game id of the boosted game ON findTodayIosBoostedGameId WHEN we have a setup for today`(haveSetup: Boolean) {
    val today = LocalDate.now()
    val gameIds = (1..3).map { database.prepareGame() }
    transaction(database) {
      IosBoostedGameTable.deleteAll()
      IosBoostedGameTable.insert {
        it[IosBoostedGameTable.gameId] = gameIds[0]
        it[IosBoostedGameTable.forDay] = today.minusDays(1)
        it[IosBoostedGameTable.imageFilename] = "some image file name here"
      }
      IosBoostedGameTable.insert {
        it[IosBoostedGameTable.gameId] = gameIds[1]
        it[IosBoostedGameTable.forDay] = today.takeIf { haveSetup } ?: today.plusDays(2)
        it[IosBoostedGameTable.imageFilename] = "some image file name here"
      }
      IosBoostedGameTable.insert {
        it[IosBoostedGameTable.gameId] = gameIds[2]
        it[IosBoostedGameTable.forDay] = today.plusDays(1)
        it[IosBoostedGameTable.imageFilename] = "some image file name here"
      }
    }

    runBlocking {
      service.findTodayIosBoostedGameId(today)
    }.let {
      if (haveSetup)
        assertThat(it).isEqualTo(gameIds[1])
      else
        assertThat(it).isNull()
    }
  }

  @Test
  fun `SHOULD return null ON loadVisibleIosBoostedGame WHEN we have no setup for today`() {
    val today = LocalDate.now()
    transaction(database) {
      IosBoostedGameTable.deleteAll()
      IosBoostedGameTable.insert {
        it[IosBoostedGameTable.gameId] = 500001 //TM
        it[IosBoostedGameTable.forDay] = today.minusDays(1)
        it[IosBoostedGameTable.imageFilename] = "some image file name here"
      }
      IosBoostedGameTable.insert {
        it[IosBoostedGameTable.gameId] = 500001
        it[IosBoostedGameTable.forDay] = today.plusDays(1)
        it[IosBoostedGameTable.imageFilename] = "some image file name here"
      }
    }

    runBlocking {
      service.loadVisibleIosBoostedGame(today)
    }.let { assertThat(it).isNull() }
  }

  @Test
  fun `SHOULD return an ios game api dto with replaced image and description ON loadVisibleIosBoostedGame WHEN we have a setup for today`() {
    val today = LocalDate.now()
    transaction(database) {
      IosBoostedGameTable.deleteAll()
      IosBoostedGameTable.insert {
        it[IosBoostedGameTable.gameId] = 500001 //TM
        it[IosBoostedGameTable.forDay] = today.minusDays(1)
        it[IosBoostedGameTable.imageFilename] = "some image file name here"
      }
      IosBoostedGameTable.insert {
        it[IosBoostedGameTable.gameId] = 500002 //Solitaire
        it[IosBoostedGameTable.forDay] = today
        it[IosBoostedGameTable.imageFilename] = "some image file name here"
        it[IosBoostedGameTable.description] = "some fancy description here"
      }
      IosBoostedGameTable.insert {
        it[IosBoostedGameTable.gameId] = 500001
        it[IosBoostedGameTable.forDay] = today.plusDays(1)
        it[IosBoostedGameTable.imageFilename] = "some image file name here"
      }
      IosBoostedGameTable.insert {
        it[IosBoostedGameTable.gameId] = 500003
        it[IosBoostedGameTable.forDay] = today.plusDays(2)
        it[IosBoostedGameTable.imageFilename] = "some image file name here"
      }
    }

    runBlocking {
      service.loadVisibleIosBoostedGame(today)
    }.let {
      it!!
      assertThat(it.id).isEqualTo(500002)
      assertThat(it.imageFilename).isEqualTo("some image file name here")
      assertThat(it.applicationId).isEqualTo("com.gimica.solitaireverse")
      assertThat(it.description).isEqualTo("some fancy description here")
    }

    runBlocking {
      service.loadVisibleIosBoostedGame(today.plusDays(2))
    }.let {
      it!!
      assertThat(it.id).isEqualTo(500003)
      assertThat(it.imageFilename).isEqualTo("some image file name here")
      assertThat(it.applicationId).isEqualTo("com.gimica.ballbounce")
      assertThat(it.description).isEqualTo("\$_ios_ballbounce_description")//no special description
    }
  }

  @Test
  fun `SHOULD return null ON loadVisibleIosBoostedGame WHEN we have no setups`() {
    val today = LocalDate.now()
    transaction(database) {
      IosBoostedGameTable.deleteAll()
    }

    runBlocking {
      service.loadVisibleIosBoostedGame(today)
    }.let { assertThat(it).isNull() }
  }

  private fun Database.addTimeSpent(userId: String, gameId: Int, seconds: Long) =
    transaction(this) {
      UserGameTimeSpentTable.insert {
        it[UserGameTimeSpentTable.userId] = userId
        it[UserGameTimeSpentTable.gameId] = gameId
        it[timeSpentSeconds] = seconds
      }
    }
}

fun Database.addGameLocaleDependency(applicationId: String, localeList: String) {
  transaction(this) {
    GamesLocaleDependentTable
      .batchInsert(localeList.split(",")) { locale ->
        this[GamesLocaleDependentTable.applicationId] = applicationId
        this[GamesLocaleDependentTable.locale] = locale
        this[GamesLocaleDependentTable.platform] = ANDROID.name
      }
  }
}

private fun Database.prepareGameAdUnitId(
  appPlatform: AppPlatform = ANDROID,
  packageId: String,
  type: GamesSetupService.AdUnitType,
  adUnitId: String,
  isHighlyTrustedUser: Boolean
) {
  transaction(this) {
    GameAdUnitIdsTable
      .insert {
        it[GameAdUnitIdsTable.appPlatform] = appPlatform.name
        it[GameAdUnitIdsTable.packageId] = packageId
        it[GameAdUnitIdsTable.type] = type.name
        it[GameAdUnitIdsTable.adUnitId] = adUnitId
        it[GameAdUnitIdsTable.isHighlyTrustedUser] = isHighlyTrustedUser
      }
  }
}