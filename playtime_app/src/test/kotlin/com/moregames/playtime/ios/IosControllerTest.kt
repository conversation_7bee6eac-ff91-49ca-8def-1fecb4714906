package com.moregames.playtime.ios

import assertk.all
import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isNull
import assertk.assertions.isNullOrEmpty
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment.IOS_USERS_INTERVIEW
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.coins.UserCurrentCoinsGoalBalance
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.dto.AppVersionDto
import com.moregames.base.dto.TrackingDataType.IDFA
import com.moregames.base.dto.TrackingDataType.IDFV
import com.moregames.base.util.ClientVersionsSupport.getDesiredAppVersion
import com.moregames.base.util.ClientVersionsSupport.getUserCreationMinAppVersion
import com.moregames.base.util.Constants.COUNTRY_HEADER
import com.moregames.base.util.Constants.IOS_APP_VERSION_HEADER
import com.moregames.base.util.Constants.IP_HEADER
import com.moregames.base.util.Constants.MARKET_HEADER
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.buseffects.AmplitudeEventEffectHandler
import com.moregames.playtime.earnings.dto.UserCurrencyEarnings
import com.moregames.playtime.ios.dto.IdfaApiDto
import com.moregames.playtime.ios.dto.IdfvApiDto
import com.moregames.playtime.ios.dto.user.IosInterviewDecisionApiDto
import com.moregames.playtime.ios.examination.IosExaminationController
import com.moregames.playtime.ios.news.IosNewsService
import com.moregames.playtime.notifications.status.UserNotificationStatusService
import com.moregames.playtime.notifications.status.UserNotificationsStatusApiDto
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.translations.TranslationService
import com.moregames.playtime.user.UserController
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.achievement.AchievementService
import com.moregames.playtime.user.achievement.AchievementType
import com.moregames.playtime.user.achievement.UserAchievementApiDto
import com.moregames.playtime.user.achievement.UserAchievementsApiDto
import com.moregames.playtime.user.cashout.*
import com.moregames.playtime.user.cashout.dto.CashoutPeriodDto
import com.moregames.playtime.user.coingoal.GameCoinGoalClaimService
import com.moregames.playtime.user.dto.AdjustIdDto
import com.moregames.playtime.user.dto.ConsentApiDto
import com.moregames.playtime.user.dto.DeviceTokenApiDto
import com.moregames.playtime.user.dto.FirebaseAppInstanceIdDto
import com.moregames.playtime.user.interview.UserInterviewService
import com.moregames.playtime.user.toprunningbar.TopRunningBarDto
import com.moregames.playtime.user.toprunningbar.TopRunningBarService
import com.moregames.playtime.user.tracking.TrackingData
import com.moregames.playtime.user.verification.VerificationController
import com.moregames.playtime.util.installDefaultContentNegotiation
import com.moregames.playtime.utils.*
import com.moregames.playtime.utils.Json.defaultJsonConverter
import io.ktor.application.*
import io.ktor.http.*
import io.ktor.routing.*
import io.ktor.server.testing.*
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import net.javacrumbs.jsonunit.JsonAssert.assertJsonEquals
import net.javacrumbs.jsonunit.JsonAssert.whenIgnoringPaths
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*
import kotlin.test.assertEquals

@ExperimentalSerializationApi
class IosControllerTest {
  private val userService: UserService = mock()
  private val cashoutPeriodsService: CashoutPeriodsService = mock()
  private val cashoutStatusService: CashoutStatusService = mock()
  private val timeService: TimeService = mock()
  private val cashoutController: CashoutController = mock()
  private val paymentsController: PaymentsController = mock()
  private val verificationController: VerificationController = mock()
  private val iosNewsService: IosNewsService = mock {
    onBlocking { getLocalizedNews(EN_LOCALE) } doReturn iosNewsStub
  }
  private val abTestingService: AbTestingService = mock {
    onBlocking { isUserExperimentParticipant(any(), any()) } doReturn false
    onBlocking { assignedVariationValue(any(), any(), any()) } doReturn DEFAULT
  }
  private val iosExaminationController: IosExaminationController = mock()
  private val topRunningBarService: TopRunningBarService = mock()
  private val iosOnlineUsersService: IosOnlineUsersService = mock()
  private val translationService: TranslationService = mock()
  private val userInterviewService: UserInterviewService = mock()
  private val gameCoinGoalClaimService: GameCoinGoalClaimService = mock()
  private val rewardingFacade: RewardingFacade = mock()
  private val cashoutService: CashoutService = mock {
    onBlocking { userHasCashouts(any()) } doReturn false
  }
  private val achievementService: AchievementService = mock()
  private val userNotificationStatusService: UserNotificationStatusService = mock()

  private val amplitudeEventEffectHandler: AmplitudeEventEffectHandler = mock {
    onBlocking { shouldUseAmplitudeAnalytics(USER_ID) } doReturn false
  }
  private val applicationConfig: ApplicationConfig = mock()

  private fun controller(): Application.() -> Unit = {
    install(IgnoreTrailingSlash)
    installDefaultContentNegotiation()
    routing {
      IosController(
        userService = userService,
        cashoutPeriodsService = cashoutPeriodsService,
        cashoutStatusService = cashoutStatusService,
        timeService = timeService,
        cashoutController = cashoutController,
        paymentsController = paymentsController,
        verificationController = verificationController,
        iosNewsService = iosNewsService,
        abTestingService = abTestingService,
        rewardingFacade = rewardingFacade,
        iosExaminationController = iosExaminationController,
        topRunningBarService = topRunningBarService,
        iosOnlineUsersService = iosOnlineUsersService,
        translationService = translationService,
        userInterviewService = userInterviewService,
        gameCoinGoalClaimService = gameCoinGoalClaimService,
        cashoutService = cashoutService,
        leaderboardService = mock(),
        achievementService = achievementService,
        iosQualifiedUserService = mock(),
        userNotificationStatusService = userNotificationStatusService,
        amplitudeEventEffectHandler = amplitudeEventEffectHandler,
        applicationConfig = applicationConfig,
        iosGamesController = mock(),
      ).startRouting(this)
    }
  }

  private companion object {
    const val USER_ID = "userId"
    val now: Instant = Instant.now()
    val cashoutPeriod = CashoutPeriodDto(
      userId = USER_ID,
      periodStart = now.minus(12, ChronoUnit.HOURS),
      periodEnd = now.plus(12, ChronoUnit.HOURS),
      coinGoal = 2000,
      counter = 3,
      noEarningsCounter = 0,
      coinGoalMilestones = emptyList(),
    )
  }

  @BeforeEach
  fun before() {
    whenever(timeService.now()).thenReturn(now)
    rewardingFacade.mock({ inflatingCoinsMultiplier(USER_ID) }, 2000)
    rewardingFacade.mock({ getUserCurrentCoinsBalance(USER_ID, IOS) }, UserCurrentCoinsGoalBalance(0, 0, 0))
  }

  @Test
  fun `SHOULD enable nested controllers ON controller creation`() = withTestApplication(controller()) {
    // UserId context: /users/${userId}/...
    val cashoutControllerRouteCapture = argumentCaptor<Route>()
    val paymentsControllerRouteCapture = argumentCaptor<Route>()
    val verificationControllerCapture = argumentCaptor<Route>()
    val iosExaminationControllerCapture = argumentCaptor<Route>()
    verify(cashoutController).startRouting(cashoutControllerRouteCapture.capture())
    verify(paymentsController).startRouting(paymentsControllerRouteCapture.capture())
    verify(verificationController).startRouting(verificationControllerCapture.capture())
    verify(iosExaminationController).startRouting(iosExaminationControllerCapture.capture())

    var cashoutRoute: Route? = cashoutControllerRouteCapture.firstValue
    var paymentsRoute: Route? = paymentsControllerRouteCapture.firstValue
    var verificationRoute: Route? = verificationControllerCapture.firstValue
    var iosExaminationRoute: Route? = iosExaminationControllerCapture.firstValue
    listOf("{${UserController.USER_ID_PARAMETER}}", "users", "ios", "").forEach { path ->

      assertThat(cashoutRoute?.selector?.toString()).isEqualTo(path)
      assertThat(paymentsRoute?.selector?.toString()).isEqualTo(path)
      assertThat(verificationRoute?.selector?.toString()).isEqualTo(path)
      assertThat(iosExaminationRoute?.selector?.toString()).isEqualTo(path)

      cashoutRoute = cashoutRoute?.parent
      paymentsRoute = paymentsRoute?.parent
      verificationRoute = verificationRoute?.parent
      iosExaminationRoute = iosExaminationRoute?.parent
    }
    assertThat(cashoutRoute).isNull()
    assertThat(paymentsRoute).isNull()
    assertThat(verificationRoute).isNull()
    assertThat(iosExaminationRoute).isNull()
  }

  @Test
  fun `SHOULD return news ON news call`() = withTestApplication(controller()) {
    //language=json
    val expected = """
      {
        "news": [
          {
            "title": "New monsters are added to Treasure Master!",
            "text": "New monsters have been added to one of our best games! Welcome “The Mummy”, “The Dragon”, and “The Skeleton”! To enjoy the new monsters, make sure your version of Treasure Master is up to date.",
            "imageUrl": "https://storage.googleapis.com/public-playtime/images/iOS_news_tm_new_monsters.png"
          },
          {
            "title": "Epic Boss Battles Await: Unleash Your Archery Skills!",
            "text": "Engage in adrenaline-pumping boss battles where the precise aim is crucial. Test your archery skills and take down formidable foes with well-placed arrow shots. Can you defeat the toughest bosses and claim legendary rewards?",
            "imageUrl": "https://storage.googleapis.com/public-playtime/images/iOS_news_tm_boss_battles.png",
            "detailedDescription": "some detailed description",
            "detailedImageUrl": "https://storage.googleapis.com/public-playtime/images/some_image_url.png",
            "link": "some link",
            "featureSurveyId": "survey id",
            "game": {
              "appstoreId": "id6478022829",
              "bundleId": "applicationId",
              "installButtonText": "infoTextInstallTop",
              "appScheme": "LaunchFairytaleMansion:",
              "applicationId": "",
              "iosGameUrl": ""
      }            
          }
        ]
      }
    """.trimIndent()

    val actual = handleRequest(
      method = HttpMethod.Get,
      uri = "/ios/news"
    )

    assertJsonEquals(expected, actual.response.content!!)
  }

  @Test
  fun `SHOULD return translations ON translations call`() =
    withTestApplication(controller()) {
      translationService.mock({ getAppTranslations(any(), any(), anyOrNull()) }, iosTranslationsStub)
      //language=json
      val expected = """
      {
          "language": "en",
          "translations": {
              "Main.MyBalance": "😇Loyalty Points:",
              "Main.CashOut": "😇Cash out!",
              "Main.Claim": "😇Claim!",
              "Main.Reach": "😇Reach the Loyalty Points goal to maximize rewards!",
              "Main.Goal": "😇Goal",
              "Main.EarnPlayingGames": "😇Earn playing games:",
              "Main.EarnCompletingOffers": "😇Earn completing offers:",
              "Main.PlayAndEarn": "😇Play & Earn",
              "Main.DoAndEarn": "😇Do and earn",
              "Main.ByGimicaGames": "😇By Gimica Games",
              "AboutUs.MainText": "<b>IT IS TEST TEXT</b>\nJustPlay is the <b>Gimica Games Loyalty Program</b>, an app made <b>exclusively</b> for <b>Gimica Games</b> players...\n\nBy having JustPlay installed you’ll be able to accumulate <b>Loyalty Points</b> for playing your favorites games from <b>Gimica Games\n\nEvery 3 hours</b>, you’ll be able to convert loyalty points accumulated by playing to <b>real rewards</b>!"
          }
      }
    """.trimIndent()

      val actual = handleRequest(
        method = HttpMethod.Get,
        uri = "/ios/translations?languageTag=en"
      )

      assertEquals(HttpStatusCode.OK, actual.response.status())
      assertJsonEquals(expected, actual.response.content!!)
    }

  @Test
  fun `SHOULD save idfa ON idfa post call`() = withTestApplication(controller()) {
    val idfa = UUID.randomUUID().toString()

    val actual = handleRequest(
      method = HttpMethod.Post,
      uri = "/ios/users/${USER_ID}/idfa"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(IOS_APP_VERSION_HEADER, "1")
      setBody(defaultJsonConverter.encodeToString(IdfaApiDto(idfa = idfa)))
    }

    assertThat(actual.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userService, times(1)) { addUserTrackingData(USER_ID, TrackingData(idfa, IDFA, IOS)) }
  }

  @Test
  fun `SHOULD save idfv ON idfa post call`() = withTestApplication(controller()) {
    val idfv = UUID.randomUUID().toString()

    val actual = handleRequest(
      method = HttpMethod.Post,
      uri = "/ios/users/${USER_ID}/idfv"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(IOS_APP_VERSION_HEADER, "1")
      setBody(defaultJsonConverter.encodeToString(IdfvApiDto(idfv = idfv)))
    }

    assertThat(actual.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userService, times(1)) { updateTrackingData(USER_ID, TrackingData(idfv, IDFV, IOS), appVersion = AppVersionDto(IOS, 1)) }
  }


  @Test
  fun `SHOULD save device token ON deviceToken post call`() = withTestApplication(controller()) {
    val deviceToken = "deviceToken"

    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/ios/users/${USER_ID}/deviceToken"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(IOS_APP_VERSION_HEADER, "1")
      setBody(defaultJsonConverter.encodeToString(DeviceTokenApiDto(deviceToken)))
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userService) {
      updateDeviceToken(USER_ID, deviceToken, IOS)
    }
  }

  @Test
  fun `SHOULD NOT save device token ON deviceToken post call WHEN token is empty`() = withTestApplication(controller()) {
    handleRequest(
      method = HttpMethod.Post,
      uri = "/ios/users/${USER_ID}/deviceToken"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(IOS_APP_VERSION_HEADER, "1")
      setBody(defaultJsonConverter.encodeToString(DeviceTokenApiDto("")))
    }
      .also { assertThat(it.response.status()).isEqualTo(HttpStatusCode.OK) }

    verifyBlocking(userService, never()) { updateDeviceToken(any(), any(), any()) }
  }

  @Test
  fun `SHOULD call updateAdjustId ON POST adjustId`() = withTestApplication(controller()) {
    val adjustId = "adjustId"

    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/ios/users/${USER_ID}/adjustId"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(IOS_APP_VERSION_HEADER, "1")
      setBody(defaultJsonConverter.encodeToString(AdjustIdDto(adjustId = adjustId)))
    }

    verifyBlocking(userService) { updateAdjustId(USER_ID, adjustId) }
    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD save firebaseAppInstanceId ON firebaseAppInstanceId post call`() = withTestApplication(controller()) {
    val firebaseAppInstanceId = "firebaseAppInstanceId"

    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/ios/users/$USER_ID/firebaseAppInstanceId"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(IOS_APP_VERSION_HEADER, "1")
      setBody(defaultJsonConverter.encodeToString(FirebaseAppInstanceIdDto(firebaseAppInstanceId)))
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userService) {
      addOrUpdateFirebaseAppInstanceId(USER_ID, firebaseAppInstanceId, IOS)
    }
  }

  @Test
  fun `SHOULD return user data ON get user data call`() = withTestApplication(controller()) {
    val user = user.copy(
      userId = USER_ID,
    )

    rewardingFacade.mock({ getUserCurrentCoinsBalance(USER_ID, IOS) }, UserCurrentCoinsGoalBalance(314, 271, 602))

    userService.mock({ loadCoinGoalUser(USER_ID) }, user.copy(userId = USER_ID))
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)
    cashoutService.mock(
      { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) },
      UserCurrencyEarnings(BigDecimal("10.019"), Currency.getInstance("USD"), BigDecimal("10.019"))
    )
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriod)
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)
    rewardingFacade.mock({ inflatingCoinsMultiplier(USER_ID) }, 2000)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/ios/users/$USER_ID"
    ) {
      addHeader(IOS_APP_VERSION_HEADER, "1")
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    //language=json
    val expectedResponse = """
      {
        "id": "userId",
        "coins": 602,
        "coinsBalance": 314,
        "coinGoal": 4000000,
        "cashoutAvailable": true,
        "cashoutAmount": "$10.01",
        "nextCashoutTimestamp": "2023-12-06T22:18:49.521946Z",
        "useAmplitudeAnalytics": false,
        "configuration": {
          "cashoutForm": "doubleEmail",
          "cashoutConfirmationMode":"oneClickCashout",
          "moreGamesMode" : "horizontalBig",
          "coinGoalSignsMode": "showGoalMarkers",
          "allGamesButtonMode": "hideButton",
          "rewardsScreenMode": "combined",
          "coinsAmountSeparatorMode": "localeRelated",
          "beEmailValidation": true,
          "newCashoutFlowEnabled":true
        },
        "timestamp": "2023-12-06T10:18:49.521946Z",
        "bitlabInflationMultiplier": 2000
      }
    """.trimIndent()
    assertJsonEquals(expectedResponse, response.response.content, whenIgnoringPaths("timestamp", "nextCashoutTimestamp"))
  }

  @ParameterizedTest
  @ValueSource(booleans = [false, true])
  fun `SHOULD respect market header ON get user data call`(marketHeaderDefined: Boolean) = withTestApplication(controller()) {
    userService.mock({ loadCoinGoalUser(USER_ID) }, user.copy(userId = USER_ID))
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)
    cashoutService.mock(
      { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) },
      UserCurrencyEarnings(BigDecimal("10.019"), Currency.getInstance("USD"), BigDecimal("10.019"))
    )
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriod)
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)
    rewardingFacade.mock({ inflatingCoinsMultiplier(USER_ID) }, 2000)

    val appVersion = AppVersionDto(platform = IOS, 1)
    val expected = if (marketHeaderDefined) "market" else null

    handleRequest(
      method = HttpMethod.Get,
      uri = "/ios/users/$USER_ID"
    ) {
      addHeader(IOS_APP_VERSION_HEADER, "1")
      if (marketHeaderDefined) addHeader(MARKET_HEADER, "market")
    }

    verifyBlocking(userService) {
      onUserSpecificRequest(eq(USER_ID), any(), eq(appVersion), eq(null), any(), eq(expected))
    }
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD return useAmplitudeAnalytics true ON get user when user on Experiment`(onVariation: Boolean) = withTestApplication(controller()) {
    amplitudeEventEffectHandler.mock({ shouldUseAmplitudeAnalytics(USER_ID) }, onVariation)
    userService.mock({ loadCoinGoalUser(USER_ID) }, user.copy(userId = USER_ID))
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)
    cashoutService.mock(
      { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) },
      UserCurrencyEarnings(BigDecimal("10.019"), Currency.getInstance("USD"), BigDecimal("10.019"))
    )
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriod)
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)
    rewardingFacade.mock({ inflatingCoinsMultiplier(USER_ID) }, 2000)

    val response = handleRequest(method = HttpMethod.Get, uri = "ios/users/$USER_ID") {
      addHeader(IOS_APP_VERSION_HEADER, "1")
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    //language=json
    val expectedResponse = """
      {
        "id": "userId",
        "coins": 0,
        "coinsBalance": 0,
        "coinGoal": 4000000,
        "cashoutAvailable": true,
        "cashoutAmount": "$10.01",
        "nextCashoutTimestamp": "2023-12-06T22:18:49.521946Z",
        "useAmplitudeAnalytics": $onVariation,
        "configuration": {
          "cashoutForm": "doubleEmail",
          "cashoutConfirmationMode":"oneClickCashout",
          "moreGamesMode" : "horizontalBig",
          "coinGoalSignsMode": "showGoalMarkers",
          "allGamesButtonMode": "hideButton",
          "rewardsScreenMode": "combined",
          "coinsAmountSeparatorMode": "localeRelated",
          "beEmailValidation": true,
          "newCashoutFlowEnabled":true
        },
        "timestamp": "2023-12-06T10:18:49.521946Z",
        "bitlabInflationMultiplier": 2000
      }
    """.trimIndent()
    assertJsonEquals(expectedResponse, response.response.content, whenIgnoringPaths("timestamp", "nextCashoutTimestamp"))
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD return user data ON get user data call WHEN cashout is not enabled`(isCashoutEnabled: Boolean) = withTestApplication(controller()) {
    userService.mock({ loadCoinGoalUser(USER_ID) }, user.copy(userId = USER_ID))
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)
    cashoutService.mock(
      { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) },
      UserCurrencyEarnings(BigDecimal("10.019"), Currency.getInstance("USD"), BigDecimal("10.019"))
    )
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriod)
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, isCashoutEnabled)
    rewardingFacade.mock({ inflatingCoinsMultiplier(USER_ID) }, 2000)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/ios/users/$USER_ID"
    ) {
      addHeader(IOS_APP_VERSION_HEADER, "1")
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    //language=json
    val expectedResponse = """
      {
        "id": "userId",
        "coins": 0,
        "coinsBalance": 0,
        "coinGoal": 4000000,
        "cashoutAvailable": $isCashoutEnabled,
        "cashoutAmount": "$10.01",
        "nextCashoutTimestamp": "2023-12-06T22:18:49.521946Z",
        "useAmplitudeAnalytics": false,
        "configuration": {
          "cashoutForm": "doubleEmail",
          "cashoutConfirmationMode":"oneClickCashout",
          "moreGamesMode" : "horizontalBig",
          "coinGoalSignsMode": "showGoalMarkers",
          "allGamesButtonMode": "hideButton",
          "rewardsScreenMode": "combined",
          "coinsAmountSeparatorMode": "localeRelated",
          "beEmailValidation": true,
          "newCashoutFlowEnabled":true
        },        
        "timestamp": "2023-12-06T10:18:49.521946Z",
        "bitlabInflationMultiplier": 2000
      }
    """.trimIndent()
    assertJsonEquals(expectedResponse, response.response.content, whenIgnoringPaths("timestamp", "nextCashoutTimestamp"))
  }

  @Test
  fun `SHOULD return user data ON get user data call WHEN cashout amount is zero`() = withTestApplication(controller()) {
    userService.mock({ loadCoinGoalUser(USER_ID) }, user.copy(userId = USER_ID))
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)
    cashoutService.mock(
      { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) },
      UserCurrencyEarnings(BigDecimal("0.00"), Currency.getInstance("USD"), BigDecimal("0.00"))
    )
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriod)
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)
    rewardingFacade.mock({ inflatingCoinsMultiplier(USER_ID) }, 2000)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/ios/users/$USER_ID"
    ) {
      addHeader(IOS_APP_VERSION_HEADER, "1")
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    //language=json
    val expectedResponse = """
      {
        "id": "userId",
        "coins": 0,
        "coinsBalance": 0,
        "coinGoal": 4000000,
        "cashoutAvailable": false,
        "nextCashoutTimestamp": "2023-12-06T22:18:49.521946Z",
        "useAmplitudeAnalytics": false,
        "configuration": {
          "cashoutForm": "doubleEmail",
          "cashoutConfirmationMode":"oneClickCashout",
          "moreGamesMode" : "horizontalBig",
          "coinGoalSignsMode": "showGoalMarkers",
          "allGamesButtonMode": "hideButton",
          "rewardsScreenMode": "combined",
          "coinsAmountSeparatorMode": "localeRelated",
          "beEmailValidation": true,
          "newCashoutFlowEnabled":true
        },
        "timestamp": "2023-12-06T10:18:49.521946Z",
        "bitlabInflationMultiplier": 2000
      }
    """.trimIndent()
    assertJsonEquals(expectedResponse, response.response.content, whenIgnoringPaths("timestamp", "nextCashoutTimestamp"))
  }

  @Test
  fun `SHOULD return proper value of cashoutForm ON get user`() = withTestApplication(controller()) {
    userService.mock({ loadCoinGoalUser(USER_ID) }, user.copy(userId = USER_ID))
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)
    cashoutService.mock(
      { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) },
      UserCurrencyEarnings(BigDecimal("10.019"), Currency.getInstance("USD"), BigDecimal("10.019"))
    )
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriod)
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)
    rewardingFacade.mock({ inflatingCoinsMultiplier(USER_ID) }, 2000)
    amplitudeEventEffectHandler.mock({ shouldUseAmplitudeAnalytics(USER_ID) }, true)

    val response = handleRequest(method = HttpMethod.Get, uri = "ios/users/$USER_ID") {
      addHeader(IOS_APP_VERSION_HEADER, "82")
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    //language=json
    val expectedResponse = """
      {
        "id": "userId",
        "coins": 0,
        "coinsBalance": 0,
        "coinGoal": 4000000,
        "cashoutAvailable": true,
        "cashoutAmount": "$10.01",
        "nextCashoutTimestamp": "2023-12-06T22:18:49.521946Z",
        "useAmplitudeAnalytics": true,
        "configuration": {
          "cashoutForm": "doubleEmail",
          "cashoutConfirmationMode":"oneClickCashout",
          "moreGamesMode" : "horizontalBig",
          "coinGoalSignsMode": "showGoalMarkers",
          "allGamesButtonMode": "hideButton",
          "rewardsScreenMode": "combined",
          "coinsAmountSeparatorMode": "localeRelated",
          "beEmailValidation": true,
          "newCashoutFlowEnabled":true
        },
        "timestamp": "2023-12-06T10:18:49.521946Z",
        "bitlabInflationMultiplier": 2000
      }
    """.trimIndent()
    assertJsonEquals(expectedResponse, response.response.content, whenIgnoringPaths("timestamp", "nextCashoutTimestamp"))
  }

  @Test
  fun `SHOULD return null newCashoutFlowEnabled ON get user WHEN NOT user is not on experiment`() = withTestApplication(controller()) {
    setupUser()

    val response = handleRequest(method = HttpMethod.Get, uri = "ios/users/$USER_ID") {
      addHeader(IOS_APP_VERSION_HEADER, "90")
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    //language=json
    val expectedResponse = """
      {
        "id": "userId",
        "coins": 0,
        "coinsBalance": 0,
        "coinGoal": 4000000,
        "cashoutAvailable": true,
        "cashoutAmount": "$10.01",
        "nextCashoutTimestamp": "2023-12-06T22:18:49.521946Z",
        "useAmplitudeAnalytics": true,
        "configuration": {
          "cashoutForm": "doubleEmail",
          "cashoutConfirmationMode":"oneClickCashout",
          "moreGamesMode" : "horizontalBig",
          "coinGoalSignsMode": "showGoalMarkers",
          "allGamesButtonMode": "hideButton",
          "rewardsScreenMode": "combined",
          "coinsAmountSeparatorMode": "localeRelated",
          "beEmailValidation": true,
          "newCashoutFlowEnabled":true
        },  
        "timestamp": "2023-12-06T10:18:49.521946Z",
        "bitlabInflationMultiplier": 2000
      }
    """.trimIndent()
    assertJsonEquals(expectedResponse, response.response.content, whenIgnoringPaths("timestamp", "nextCashoutTimestamp"))
  }

  @Test
  fun `SHOULD return response with desired version in header ON user call WHEN ios AND request app version is lower than desired`() =
    withTestApplication(controller()) {
      userService.mock({ loadCoinGoalUser(USER_ID) }, user.copy(userId = USER_ID))
      cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)
      cashoutService.mock(
        { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) },
        UserCurrencyEarnings(BigDecimal("10.019"), Currency.getInstance("USD"), BigDecimal("10.019"))
      )
      cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriod)
      cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)
      rewardingFacade.mock({ inflatingCoinsMultiplier(USER_ID) }, 2000)

      val response = handleRequest(
        method = HttpMethod.Get,
        uri = "/ios/users/$USER_ID"
      ) {
        addHeader("Content-Type", "application/json")
        addHeader(IOS_APP_VERSION_HEADER, "${getDesiredAppVersion(IOS) - 1}")
        addHeader(COUNTRY_HEADER, "US")
        addHeader(IP_HEADER, "***********")
      }

      assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
      assertThat(response.response.headers[UserController.DESIRED_APP_VERSION_HEADER]).isEqualTo("${getDesiredAppVersion(IOS)}")
    }

  @Test
  fun `SHOULD return value for preGameScreenMode ON get user WHEN iOS version ge 16`() = withTestApplication(controller()) {
    setupUser()

    val response = handleRequest(method = HttpMethod.Get, uri = "ios/users/$USER_ID") {
      addHeader(IOS_APP_VERSION_HEADER, "91")
      addHeader(HttpHeaders.UserAgent, "JustPlay/1.2.3 (com.gimica.justplay; build:92; iOS 16.1) Alamofire/5.6.2")
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    //language=json
    val expectedResponse = """
      {
        "id": "userId",
        "coins": 0,
        "coinsBalance": 0,
        "coinGoal": 4000000,
        "cashoutAvailable": true,
        "cashoutAmount": "$10.01",
        "nextCashoutTimestamp": "2023-12-06T22:18:49.521946Z",
        "useAmplitudeAnalytics": true,
        "timestamp": "2023-12-06T10:18:49.521946Z",
        "configuration": {
          "cashoutForm": "doubleEmail",
          "cashoutConfirmationMode":"oneClickCashout",
          "moreGamesMode" : "horizontalBig",
          "coinGoalSignsMode": "showGoalMarkers",
          "allGamesButtonMode": "hideButton",
          "rewardsScreenMode": "combined",
          "coinsAmountSeparatorMode": "localeRelated",
          "beEmailValidation": true,
          "newCashoutFlowEnabled":true,
          "preGameScreenMode": "storeController"
        },
        "bitlabInflationMultiplier": 2000
      }
    """.trimIndent()
    assertJsonEquals(expectedResponse, response.response.content, whenIgnoringPaths("timestamp", "nextCashoutTimestamp"))
  }

  @Test
  fun `SHOULD return top bar config ON top-running-bar`(): Unit = withTestApplication(controller()) {
    val expected = TopRunningBarDto(
      values = listOf("some", "bar", "text"),
      textColor = "some color",
      backgroundColor = "some background color"
    )
    topRunningBarService.mock({ getTopRunningBarConfig(USER_ID) }, expected)

    handleRequest(
      method = HttpMethod.Get,
      uri = "ios//users/$USER_ID/top-running-bar"
    ) {
      addHeader(IOS_APP_VERSION_HEADER, "91")
    }
      .also {
        assertThat(it.response.status()).isEqualTo(HttpStatusCode.OK)
        assertThat(defaultJsonConverter.decodeFromString<TopRunningBarDto>(it.response.content!!)).isEqualTo(expected)
      }
  }

  @Test
  fun `SHOULD return 404 WHEN no config for user`(): Unit = withTestApplication(controller()) {
    topRunningBarService.mock({ getTopRunningBarConfig(USER_ID) }, null)

    handleRequest(
      method = HttpMethod.Get,
      uri = "ios/users/${USER_ID}/top-running-bar"
    ) {
      addHeader(IOS_APP_VERSION_HEADER, "91")
    }
      .also {
        assertThat(it.response.status()).isEqualTo(HttpStatusCode.NotFound)
        assertEquals("No bar configuration for user", it.response.content!!)
      }
  }

  @Test
  fun `SHOULD return value for interviewParticipant ON get user WHEN user is on experiment`() = withTestApplication(controller()) {
    setupUser()
    abTestingService.mock({ isUserExperimentParticipant(USER_ID, IOS_USERS_INTERVIEW) }, true)

    val response = handleRequest(method = HttpMethod.Get, uri = "ios/users/$USER_ID") {
      addHeader(IOS_APP_VERSION_HEADER, "110")
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    //language=json
    val expectedResponse = """
      {
        "id": "userId",
        "coins": 0,
        "coinsBalance": 0,
        "coinGoal": 4000000,
        "cashoutAvailable": true,
        "cashoutAmount": "$10.01",
        "nextCashoutTimestamp": "2023-12-06T22:18:49.521946Z",
        "useAmplitudeAnalytics": true,
        "timestamp": "2023-12-06T10:18:49.521946Z",
        "configuration": {
          "cashoutForm": "doubleEmail",
          "cashoutConfirmationMode":"oneClickCashout",
          "moreGamesMode" : "horizontalBig",
          "coinGoalSignsMode": "showGoalMarkers",
          "allGamesButtonMode": "hideButton",
          "rewardsScreenMode": "combined",
          "coinsAmountSeparatorMode": "localeRelated",
          "beEmailValidation": true,
          "newCashoutFlowEnabled":true,
          "interviewParticipant": true
        },
        "bitlabInflationMultiplier": 2000
      }
    """.trimIndent()
    assertJsonEquals(expectedResponse, response.response.content, whenIgnoringPaths("timestamp", "nextCashoutTimestamp"))
  }

  @Test
  fun `SHOULD return online users WHEN online called`(): Unit = withTestApplication(controller()) {
    whenever(iosOnlineUsersService.getActiveUsers()) doReturn 1000

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "ios/users/online"
    )
      .also {
        assertThat(it.response.status()).isEqualTo(HttpStatusCode.OK)
      }.response

    assertJsonEquals("""{"online":1000}""", response.content)
  }

  @Test
  fun `SHOULD claim reward ON claim-game-reward call`() = withTestApplication(controller()) {
    gameCoinGoalClaimService.mock({ claimReward(USER_ID, IOS) }, 1000)
    rewardingFacade.mock({ inflatingCoinsMultiplier(USER_ID) }, 2)

    val response = handleRequest(method = HttpMethod.Post, uri = "ios/users/$USER_ID/claim-goal-coins") {
      addHeader(IOS_APP_VERSION_HEADER, "1")
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertJsonEquals(
      """
      {"coins":2000}
    """.trimIndent(), response.response.content
    )
  }

  @Test
  fun `SHOULD return url WHEN user is invited to interview`() = withTestApplication(controller()) {
    userInterviewService.mock(
      { getInterviewInvitationInfo(USER_ID) },
      iosInterviewApiDtoStub.copy(popup = iosInterviewApiDtoStub.popup?.copy(confirmButtonText = "yes", cancelButtonText = "no"))
    )
    //language=JSON
    val expectedResponse = """
      {
        "url": "https://calendly.com/userfeedback-justplayapps/user-interview",
        "popup": {
          "title": "Interested in sharing your gaming experience?",
          "description": "Click “Yes” to schedule 30 minutes interview to talk about JustPlay games and as a gratitude for your time, JustPlay would be happy to offer you a <b>$50</b> Amazon gift card!",
          "confirmButtonText": "yes",
          "cancelButtonText": "no"
        }
      }
    """.trimIndent()

    val response = handleRequest(method = HttpMethod.Get, uri = "ios/users/$USER_ID/interview") {
      addHeader(IOS_APP_VERSION_HEADER, "1")
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    assertJsonEquals(expectedResponse, response.response.content)
  }

  @Test
  fun `SHOULD return 404 WHEN user is not invited to interview`() = withTestApplication(controller()) {
    userInterviewService.mock({ getInterviewInvitationInfo(USER_ID) }, null)

    val response = handleRequest(method = HttpMethod.Get, uri = "ios/users/$USER_ID/interview") {
      addHeader(IOS_APP_VERSION_HEADER, "1")
    }

    assertThat(response.response).all {
      transform { it.status() }.isEqualTo(HttpStatusCode.NotFound)
      transform { it.content }.isNullOrEmpty()
    }
  }

  @ParameterizedTest
  @EnumSource(IosInterviewDecisionApiDto.Decision::class)
  fun `SHOULD save user decision WHEN POST interview called`(decision: IosInterviewDecisionApiDto.Decision) = withTestApplication(controller()) {
    val response = handleRequest(method = HttpMethod.Post, uri = "ios/users/$USER_ID/interview") {
      addHeader(IOS_APP_VERSION_HEADER, "1")
      addHeader(HttpHeaders.ContentType, ContentType.Application.Json.toString())
      setBody("""{"decision":"${decision.name}"}""")
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userInterviewService) { saveInterviewDecision(USER_ID, decision) }
  }

  @Test
  fun `SHOULD return data ON achievements call`() = withTestApplication(controller()) {
    val data = UserAchievementsApiDto(
      achievements = listOf(
        UserAchievementApiDto(
          id = AchievementType.PLAYED_GAMES_1H,
          title = "conclusionemque",
          icon = "rhoncus",
          description = "descr 1",
          completedAt = null,
        ),
        UserAchievementApiDto(
          id = AchievementType.INSTALLED_JP_APP,
          title = "dapibus",
          icon = "porttitor",
          description = "descr 2",
          completedAt = 1725995923,
        ),
        UserAchievementApiDto(
          id = AchievementType.PLAYED_GAMES_5H,
          title = "dapibus",
          icon = "porttitor",
          description = null,
          completedAt = 1725995923,
        )
      )
    )

    achievementService.mock({ getUserAchievements(USER_ID, Locale.ENGLISH) }, data)

    val response = handleRequest(method = HttpMethod.Get, uri = "ios/users/$USER_ID/achievements") {
      //language=json
      setBody(
        """
        {
          "achievements": [
            {
              "id": "PLAYED_GAMES_1H",
              "title": "conclusionemque",
              "icon": "rhoncus",
              "description": "descr 1"
            }, 
            {
              "id": "INSTALLED_JP_APP",
              "title": "dapibus",
              "icon": "porttitor",
              "description": "descr 2",
              "completedAt": 1725995923
            }, 
            {
              "id": "PLAYED_GAMES_5H",
              "title": "dapibus",
              "icon": "porttitor",
              "completedAt": 1725995923
            }
          ]
        }
      """.trimIndent()
      )
      addHeader("Content-Type", "application/json")
      addHeader(IOS_APP_VERSION_HEADER, "115")
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(achievementService) { getUserAchievements(USER_ID, Locale.ENGLISH) }
  }

  @Test
  fun `SHOULD process feedback ON feedback-news post call`() = withTestApplication(controller()) {
    val actual = handleRequest(
      method = HttpMethod.Post,
      uri = "/ios/users/${USER_ID}/feedback/news"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(IOS_APP_VERSION_HEADER, getUserCreationMinAppVersion(IOS).toString())
      //language=json
      setBody(
        """
        {
          "featureSurveyId": "surveyTreasureMasterKnives",
          "isLiked": true
        }
      """.trimIndent()
      )
    }

    assertThat(actual.response.status()).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD process feedback ON feedback-propose-game-feature post call`() = withTestApplication(controller()) {
    val actual = handleRequest(
      method = HttpMethod.Post,
      uri = "/ios/users/${USER_ID}/feedback/propose-game-feature"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(IOS_APP_VERSION_HEADER, getUserCreationMinAppVersion(IOS).toString())
      //language=json
      setBody(
        """
        {
          "bundleId": "com.gimica.tilematchpro",
          "proposalText": "Make tilematchpro great again"
        }
      """.trimIndent()
      )
    }

    assertThat(actual.response.status()).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD process feedback ON feedback-game post call`() = withTestApplication(controller()) {
    val actual = handleRequest(
      method = HttpMethod.Post,
      uri = "/ios/users/${USER_ID}/feedback/game"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(IOS_APP_VERSION_HEADER, getUserCreationMinAppVersion(IOS).toString())
      //language=json
      setBody(
        """
        {
          "bundleId": "com.gimica.tilematchpro",
          "isLiked": false
        }
      """.trimIndent()
      )
    }

    assertThat(actual.response.status()).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD save user notifications status preferences ON post user-notifications-state`(): Unit = withTestApplication(controller()) {
    val request = UserNotificationsStatusApiDto(enabled = true)
    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/ios/users/${USER_ID}/user-notifications-state"
    ) {
      addHeader(IOS_APP_VERSION_HEADER, "142")
      addHeader("Content-Type", "application/json")
      setBody(defaultJsonConverter.encodeToString(request))
    }

    verifyBlocking(userNotificationStatusService) { setUserNotificationsStatus(USER_ID, true) }
    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD update consent data ON update user consent endpoint call`() = withTestApplication(controller()) {
    val actual = handleRequest(
      method = HttpMethod.Post,
      uri = "ios/users/${USER_ID}/consent"
    ) {
      addHeader(IOS_APP_VERSION_HEADER, "142")
      addHeader("Content-Type", "application/json")
      setBody(
        defaultJsonConverter.encodeToString(
          ConsentApiDto(hasConsentedToAnalytics = true, hasConsentedToTargetedAdvertisement = true, librariesConsent = null)
        )
      )
    }

    assertThat(actual.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userService) {
      updateUserConsent(
        USER_ID,
        ConsentApiDto(hasConsentedToAnalytics = true, hasConsentedToTargetedAdvertisement = true, librariesConsent = null)
      )
    }
  }

  private fun setupUser() {
    userService.mock({ loadCoinGoalUser(USER_ID) }, user.copy(userId = USER_ID))
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)
    cashoutService.mock(
      { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) },
      UserCurrencyEarnings(BigDecimal("10.019"), Currency.getInstance("USD"), BigDecimal("10.019"))
    )
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriod)
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)
    rewardingFacade.mock({ inflatingCoinsMultiplier(USER_ID) }, 2000)
    amplitudeEventEffectHandler.mock({ shouldUseAmplitudeAnalytics(USER_ID) }, true)
    rewardingFacade.mock({ getUserCurrentCoinsBalance(USER_ID, IOS) }, UserCurrentCoinsGoalBalance(0, 0, 0))
  }
}
