package com.moregames.playtime.notifications.android

import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.abtesting.variations.AndroidCashout2xOfferVariation
import com.moregames.base.abtesting.variations.SpecialCashoutOffersVariation
import com.moregames.base.app.PaymentProviderType.PAYPAL
import com.moregames.base.dto.CashoutPeriodsConfig
import com.moregames.base.junit.MockExtension
import com.moregames.base.junit.TypedVariationSource
import com.moregames.base.messaging.customnotification.*
import com.moregames.base.messaging.customnotification.BackgroundAction.*
import com.moregames.base.messaging.customnotification.ButtonActionName.OPEN_FIRST_FOUND_INSTALLED_GAME
import com.moregames.base.messaging.customnotification.CustomNotificationDto.NotificationChannelId
import com.moregames.base.messaging.customnotification.CustomNotificationSize.LARGE
import com.moregames.base.messaging.customnotification.CustomNotificationSize.MEDIUM
import com.moregames.base.messaging.customnotification.OnClickActionName.ROUTE_TO_MAIN
import com.moregames.base.messaging.dto.GenericPushNotificationScheduledEventDto
import com.moregames.base.messaging.dto.PushNotificationDto.PushNotificationType.*
import com.moregames.base.util.*
import com.moregames.playtime.app.ImageService
import com.moregames.playtime.boost.BoostedModeService
import com.moregames.playtime.boost.model.BoostedMode
import com.moregames.playtime.earnings.dto.UserCurrencyEarnings
import com.moregames.playtime.notifications.NotificationsFacade
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.*
import com.moregames.playtime.notifications.PushNotification.CrossPlatformPushNotification.*
import com.moregames.playtime.notifications.UserNotificationConfig
import com.moregames.playtime.notifications.android.cashoutfailed.CashoutFailedAppNotification
import com.moregames.playtime.notifications.android.cashoutprocessed.CashoutProcessedAppNotification
import com.moregames.playtime.notifications.android.ratingprompt.RatingPromptAppNotification
import com.moregames.playtime.notifications.android.readytocashout.ReadyToCashoutAppNotification
import com.moregames.playtime.notifications.android.silentcoinupdate.AndroidSilentCoinUpdateService
import com.moregames.playtime.notifications.android.survey.SurveyAppNotification
import com.moregames.playtime.translations.TranslationResource
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.user.cashout.CashoutPeriodsConfigService
import com.moregames.playtime.user.cashout.HideCashoutAmountExperimentService
import com.moregames.playtime.user.cashout.ReadyToCashoutExperimentConfig
import com.moregames.playtime.user.cashout.ReadyToCashoutExperimentService
import com.moregames.playtime.user.gamerank.GameRank
import com.moregames.playtime.util.DEFAULT_USER_LOCALE
import com.moregames.playtime.utils.ES_LOCALE
import com.moregames.playtime.utils.FR_LOCALE
import com.moregames.playtime.utils.cashoutTransactionStub
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Instant
import java.util.*
import kotlin.time.Duration.Companion.hours

@ExtendWith(MockExtension::class)
class AndroidPushNotificationServiceTest(
  private val translationService: UserTranslationService,
  private val notificationsFacade: NotificationsFacade,
  private val randomGenerator: RandomGenerator,
  private val hideCashoutAmountExperimentService: HideCashoutAmountExperimentService,
  private val readyToCashoutExperimentService: ReadyToCashoutExperimentService,
  private val abTestingService: AbTestingService,
  private val boostedModeService: BoostedModeService,
  private val balanceUpdatedNotificationWithAmountExpService: BalanceUpdatedNotificationWithAmountExpService,
  private val timeService: TimeService,
  private val androidSilentCoinUpdateService: AndroidSilentCoinUpdateService,
  private val cashoutPeriodsConfigService: CashoutPeriodsConfigService,
) {

  private val imageService: ImageService = ImageService()

  private val underTest = AndroidPushNotificationService(
    translationService = translationService,
    notificationsFacade = notificationsFacade,
    randomGenerator = randomGenerator,
    imageService = imageService,
    hideCashoutAmountExperimentService = hideCashoutAmountExperimentService,
    readyToCashoutExperimentService = readyToCashoutExperimentService,
    abTestingService = abTestingService,
    boostedModeService = boostedModeService,
    balanceUpdatedNotificationWithAmountExpService = balanceUpdatedNotificationWithAmountExpService,
    timeService = timeService,
    androidSilentCoinUpdateService = androidSilentCoinUpdateService,
    cashoutPeriodsConfigService = cashoutPeriodsConfigService,
  )

  @BeforeEach
  fun setUp() {
    timeService.mock({ now() }, now)
    translationService.answer({ tryTranslate(any(), any(), any()) }, { it.arguments[0] as String })
    translationService.answer({ translateOrDefault(any(), any(), any()) }, { (it.arguments[0] as TranslationResource).defaultValue })
    translationService.answer(
      { translateOrDefault(any(), eq(FR_LOCALE), any()) },
      { "FR TRANSLATED: " + (it.arguments[0] as TranslationResource).defaultValue })
    randomGenerator.mock({ nextUUID() }, "08ab62f7-2432-4bfa-a624-980f5c9181cc")
    hideCashoutAmountExperimentService.mock({ shouldShowGiftBox(any(), any(), any()) }, false)
    readyToCashoutExperimentService.mock({ getReadyToCashoutExperimentConfig(any(), any()) }, null)
    androidSilentCoinUpdateService.mock({ silentCoinUpdateNotificationSettings(any()) }, AndroidSilentCoinUpdateService.SilentNotificationSettings())
    cashoutPeriodsConfigService.mock({ getCashoutPeriodConfig(any()) }, CashoutPeriodsConfig.defaultConfig)
  }

  @Test
  fun `SHOULD send special challenge earnings added ON sendSpecialChallengeEarningsAdded`() = runTest {
    val input = RewardSpecialChallengesEarningsAddedNotification(
      userId = testUserId,
      locale = FR_LOCALE,
      earnings = UserCurrencyEarnings(BigDecimal("3.407"), Currency.getInstance("CAD"), BigDecimal("4.119")),
    )
    underTest.sendSpecialChallengeEarningsAdded(input)
    verifyBlocking(notificationsFacade) {
      sendMessage(
        testUserId,
        message = CustomNotificationDto(
          id = NotificationChannelId.CUSTOM_NOTIFICATION,
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "FR TRANSLATED: Treasure Bonus Received \uD83D\uDCB5\uD83C\uDF81",
          shortDescription = "FR TRANSLATED: A new cash treasure is waiting for you, collect new keys to win again!",
          icon = "https://storage.googleapis.com/public-playtime/images/treasure%20box.png",
          size = LARGE,
          backgroundColor = "#34C759",
          textColor = "#FFFFFF",
          soundEnabled = true,
          vibrationEnabled = true,
          onClickAction = OnClickActionApiDto.routeToCashout(),
          label = "special-challenge-earnings-added",
          collapseKey = null,
        )
      )
    }
  }


  @Test
  fun `SHOULD send special challenged claimed ON sendSpecialChallengeClaimed`() = runTest {
    val input = SpecialChallengeClaimedNotification(
      userId = testUserId,
      locale = FR_LOCALE,
    )
    underTest.sendSpecialChallengeClaimed(input)
    verifyBlocking(notificationsFacade) {
      sendMessage(
        testUserId,
        message = CustomNotificationDto(
          id = NotificationChannelId.CUSTOM_NOTIFICATION,
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "FR TRANSLATED: Cash Treasure Key Collected \uD83D\uDDDD\uFE0F\uD83C\uDF81",
          shortDescription = "FR TRANSLATED: Collect all keys to unbox your cash treasure!",
          icon = "https://storage.googleapis.com/public-playtime/images/closed%20treasure%20box.png",
          size = LARGE,
          backgroundColor = "#E19E03",
          textColor = "#FFFFFF",
          soundEnabled = true,
          vibrationEnabled = true,
          onClickAction = OnClickActionApiDto.routeToMain(),
          label = "special-challenge-claimed",
          collapseKey = null,
        )
      )
    }
  }

  @Test
  fun `SHOULD send special challenge completed ON sendSpecialChallengeCompleted`() = runTest {
    val input = SpecialChallengeCompletedNotification(
      userId = testUserId,
      locale = FR_LOCALE,
    )
    underTest.sendSpecialChallengeCompleted(input)
    verifyBlocking(notificationsFacade) {
      sendMessage(
        testUserId,
        message = CustomNotificationDto(
          id = NotificationChannelId.CUSTOM_NOTIFICATION,
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "FR TRANSLATED: Quest Completed! Claim Your Key \uD83D\uDDDD\uFE0F",
          shortDescription = "FR TRANSLATED: Collect all keys to unbox your cash treasure!",
          icon = "https://storage.googleapis.com/public-playtime/images/Key.png",
          size = LARGE,
          backgroundColor = "#E19E03",
          textColor = "#FFFFFF",
          soundEnabled = true,
          vibrationEnabled = true,
          onClickAction = OnClickActionApiDto.routeToMain(),
          label = "special-challenge-completed",
          collapseKey = null,
        )
      )
    }
  }

  @Test
  fun `SHOULD create and send notification ON sendFirstCoinsForGameNotification`() {
    val input = FirstCoinsForGamePushNotification(testUserId, gameName = "Balls versus blocks")

    runBlocking { underTest.sendFirstCoinsForGameNotification(input, DEFAULT_USER_LOCALE) }

    verifyBlocking(notificationsFacade) {
      sendGenericPushNotification(
        GenericPushNotificationScheduledEventDto(
          userId = testUserId,
          title = "Keep playing Balls versus blocks!",
          notificationText = "The more you play the more you earn!",
          notificationType = AFTER_FIRST_COINS_FOR_GAME,
          label = "after_first_coins_for_game",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send notification ON sendOnFirstCashoutOfDayNotification`() {
    val input = FirstCashoutOfDayNotification(testUserId)

    runBlocking { underTest.sendOnFirstCashoutOfDayNotification(input, DEFAULT_USER_LOCALE) }

    verifyBlocking(notificationsFacade) {
      sendGenericPushNotification(
        GenericPushNotificationScheduledEventDto(
          userId = testUserId,
          title = "Congratulations on Cashout!",
          notificationText = "Come back and earn even more!",
          notificationType = AFTER_FIRST_CASHOUT_OF_DAY,
          label = "after_first_cashout_of_day",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send notification ON sendXMinutesToCashoutPeriodEndNotification`() {
    val input = XMinutesToCashoutPeriodEndNotification(testUserId)

    runBlocking { underTest.sendXMinutesToCashoutPeriodEndNotification(input, DEFAULT_USER_LOCALE) }

    verifyBlocking(notificationsFacade) {
      sendGenericPushNotification(
        GenericPushNotificationScheduledEventDto(
          userId = testUserId,
          title = "30 minutes to cashout!",
          notificationText = "Keep playing to make the most earnings",
          notificationType = THIRTY_MINUTES_TO_CASHOUT,
          label = "30min_to_cashout",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD set correct content and send notification ON sendReachCoinGoalNotification`(coinGoalReached: Boolean) {
    val input = ReachCoinGoalNotification(testUserId, coinGoalReached = coinGoalReached)
    val expected = if (coinGoalReached) {
      GenericPushNotificationScheduledEventDto(
        userId = testUserId,
        title = "Keep playing to maximize your earnings",
        notificationText = "Keep playing! All the loyalty coins earned over the loyalty coin goal are additional earnings!",
        notificationType = REACH_COIN_GOAL,
        label = "reach_coin_goal",
      )
    } else {
      GenericPushNotificationScheduledEventDto(
        userId = testUserId,
        title = "Reach the loyalty coin goal!",
        notificationText = "Looks like you haven't reached your loyalty coin goal, reach it to maximize your earnings!",
        notificationType = REACH_COIN_GOAL,
        label = "reach_coin_goal",
      )
    }

    runBlocking { underTest.sendReachCoinGoalNotification(input, DEFAULT_USER_LOCALE) }

    verifyBlocking(notificationsFacade) { sendGenericPushNotification(expected) }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send notification ON sendMissedEarningsNotification`() {
    val input = MissedEarningsNotification(testUserId)

    runBlocking { underTest.sendMissedEarningsNotification(input, DEFAULT_USER_LOCALE) }

    verifyBlocking(notificationsFacade) {
      sendGenericPushNotification(
        GenericPushNotificationScheduledEventDto(
          userId = testUserId,
          title = "Just play a few minutes to make money!",
          notificationText = "Oh no you missed out on earnings!",
          notificationType = AFTER_EMPTY_FIRST_CASHOUT_PERIOD,
          label = "missed_earnings",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD set correct content and send notification ON sendInactivityReminder`(haveUserFirstName: Boolean) {
    val input = InactivityReminder(
      userId = testUserId,
      userFirstName = if (haveUserFirstName) "Bart" else null,
      notificationConfig = UserNotificationConfig(
        id = 3,
        title = "title 3",
        message = "message 3",
        personalizedMessage = "\$_translatable_message",
        personalizedTitle = "\$_translatable_title"
      )
    )
    translationService.mock({ tryTranslate("\$_translatable_title", ES_LOCALE, testUserId) }, "Hola, {Name}!")
    translationService.mock({ tryTranslate("\$_translatable_message", ES_LOCALE, testUserId) }, "{Name}, muchos gracias")
    val expected = if (haveUserFirstName) {
      GenericPushNotificationScheduledEventDto(
        userId = testUserId,
        title = "Hola, Bart!",
        notificationText = "Bart, muchos gracias",
        label = "inactivity_reminder_3"
      )
    } else {
      GenericPushNotificationScheduledEventDto(
        userId = testUserId,
        title = "title 3",
        notificationText = "message 3",
        label = "inactivity_reminder_3",
      )
    }

    runBlocking { underTest.sendInactivityReminder(input, ES_LOCALE) }

    verifyBlocking(notificationsFacade) { sendGenericPushNotification(expected) }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send notification ON sendBalanceUpdatedNotification`() {
    balanceUpdatedNotificationWithAmountExpService.mock(
      { getTitleAndBody(testUserId, FR_LOCALE, 100500, 0) },
      "FR TRANSLATED: Balance Update!" to "FR TRANSLATED: You now have 100 500 loyalty coins!"
    )

    val input = BalanceUpdatedNotification(userIdParam = testUserId, coins = 100500)

    runBlocking { underTest.sendBalanceUpdatedNotification(input, FR_LOCALE) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          id = NotificationChannelId.BALANCE_UPDATE,
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "FR TRANSLATED: Balance Update!",
          shortDescription = "FR TRANSLATED: You now have 100 500 loyalty coins!",
          backgroundColor = "#5262FB",
          textColor = "#FFFFFF",
          icon = "https://storage.googleapis.com/public-playtime/images/notification-coin-balance-updated-icon.png",
          size = MEDIUM,
          vibrationEnabled = null,
          soundEnabled = null,
          backgroundActions = listOf(REFRESH_USER, REFRESH_OFFERS),
          collapseKey = CustomNotificationCollapseKey.BALANCE_UPDATE,
          label = "balance_updated",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send notification ON sendBalanceUpdatedNotification WHEN hideCoins`() {
    val input = BalanceUpdatedNotification(userIdParam = testUserId, coins = 100500, hideCoins = true)

    runBlocking { underTest.sendBalanceUpdatedNotification(input, FR_LOCALE) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          id = NotificationChannelId.BALANCE_UPDATE,
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "Progress Update!",
          shortDescription = "Keep collecting your loyalty coins!",
          backgroundColor = "#5262FB",
          textColor = "#FFFFFF",
          icon = "https://storage.googleapis.com/public-playtime/images/notification-coin-balance-updated-icon.png",
          size = MEDIUM,
          vibrationEnabled = null,
          soundEnabled = null,
          backgroundActions = listOf(REFRESH_USER, REFRESH_OFFERS),
          collapseKey = CustomNotificationCollapseKey.BALANCE_UPDATE,
          label = "balance_updated",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send other design notification ON sendBalanceUpdatedNotification WHEN cashout2xOfferActive = true`() {
    val input = BalanceUpdatedNotification(userIdParam = testUserId, coins = 100500)

    runBlocking { underTest.sendBalanceUpdatedNotification(input, DEFAULT_USER_LOCALE, cashout2xOfferActive = true) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          id = NotificationChannelId.BALANCE_UPDATE,
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "Special Offer Coins Collected",
          backgroundColor = "#D442AE",
          textColor = "#FFFFFF",
          icon = "https://storage.googleapis.com/public-playtime/images/notification-coin-balance-updated-icon.png",
          size = LARGE,
          shortDescription = "You now have 100,500 special coins",
          vibrationEnabled = true,
          soundEnabled = true,
          backgroundActions = listOf(REFRESH_USER, REFRESH_OFFERS),
          collapseKey = CustomNotificationCollapseKey.BALANCE_UPDATE,
          label = "balance_updated",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send other design notification ON sendBalanceUpdatedNotification WHEN cashout2xOfferActive AND hideCoins`() {
    val input = BalanceUpdatedNotification(userIdParam = testUserId, coins = 100500, hideCoins = true)

    runBlocking { underTest.sendBalanceUpdatedNotification(input, DEFAULT_USER_LOCALE, cashout2xOfferActive = true) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          id = NotificationChannelId.BALANCE_UPDATE,
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "Special Offer Coins Collected",
          backgroundColor = "#D442AE",
          textColor = "#FFFFFF",
          icon = "https://storage.googleapis.com/public-playtime/images/notification-coin-balance-updated-icon.png",
          size = LARGE,
          shortDescription = "Keep collecting your special coins",
          vibrationEnabled = true,
          soundEnabled = true,
          backgroundActions = listOf(REFRESH_USER, REFRESH_OFFERS),
          collapseKey = CustomNotificationCollapseKey.BALANCE_UPDATE,
          label = "balance_updated",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send notification ON sendCashoutFailedNotification`() {
    val input = CashoutFailedNotification(testUserId, cashoutTransaction = cashoutTransactionStub.copy(userId = testUserId))

    runBlocking { underTest.sendCashoutFailedNotification(input) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CashoutFailedAppNotification(
          cashoutTransactionId = "cashoutTransactionId",
          amountString = "$2.50",
          providerType = PAYPAL,
          label = "cashout_failed",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send notification ON sendCashoutProcessedNotification`() {
    val input = CashoutProcessedNotification(testUserId, cashoutTransaction = cashoutTransactionStub.copy(userId = testUserId))

    runBlocking { underTest.sendCashoutProcessedNotification(input) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CashoutProcessedAppNotification(
          cashoutTransactionId = "cashoutTransactionId",
          amountString = "$2.50",
          providerType = PAYPAL,
          label = "cashout_processed",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send ReadyToCashoutAppNotification ON sendEarningsAddedNotification`() {
    val input = EarningsAddedNotification(
      userId = testUserId,
      earnings = UserCurrencyEarnings(BigDecimal("3.409"), Currency.getInstance("CAD"), BigDecimal("4.119")),
      userHasCashouts = false
    )

    runBlocking { underTest.sendEarningsAddedNotification(input, DEFAULT_USER_LOCALE) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = ReadyToCashoutAppNotification(
          amountString = "$4.11",
          label = "earnings_added",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send notification ON sendEarningsAddedNotification WHEN is hide earnings exp participant`() {
    val input = EarningsAddedNotification(
      userId = testUserId,
      earnings = UserCurrencyEarnings(BigDecimal("3.409"), Currency.getInstance("CAD"), BigDecimal("4.119")),
      userHasCashouts = false
    )
    hideCashoutAmountExperimentService.mock({ shouldShowGiftBox(testUserId, false, BigDecimal("3.409")) }, true)

    runBlocking { underTest.sendEarningsAddedNotification(input, DEFAULT_USER_LOCALE) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          id = NotificationChannelId.READY_TO_CASH_OUT,
          collapseKey = CustomNotificationCollapseKey.EARNINGS_ADDED,
          title = "Thanks for playing! Your cash reward is ready",
          size = MEDIUM,
          backgroundColor = "#3AAF49",
          icon = "https://storage.googleapis.com/public-playtime/images/notification_cashout.png",
          onClickAction = OnClickActionApiDto.routeToCashout(),
          textColor = "#FFFFFF",
          label = "earnings_added",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send custom notification ON sendReadyToCashoutCustomAppNotification`() {
    val notification = EarningsAddedNotification(
      userId = testUserId,
      earnings = UserCurrencyEarnings(BigDecimal("3.409"), Currency.getInstance("CAD"), BigDecimal("4.119")),
      userHasCashouts = false
    )
    readyToCashoutExperimentService.mock(
      { getReadyToCashoutExperimentConfig(any(), any()) },
      ReadyToCashoutExperimentConfig(
        title = "You've Got Money!",
        description = "Open JustPlay to claim your rewards"
      )
    )

    runBlocking {
      underTest.sendEarningsAddedNotification(notification, DEFAULT_USER_LOCALE)

      verifyBlocking(notificationsFacade) {
        sendMessage(
          userId = testUserId,
          message = CustomNotificationDto(
            notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
            id = NotificationChannelId.READY_TO_CASH_OUT,
            title = "You've Got Money!",
            shortDescription = "Open JustPlay to claim your rewards",
            size = MEDIUM,
            backgroundColor = "#3AAF49",
            icon = "https://storage.googleapis.com/public-playtime/images/notification_cashout.png",
            onClickAction = OnClickActionApiDto.routeToCashout(),
            textColor = "#FFFFFF",
            label = "earnings_added",
          )
        )
      }
      verifyNoMoreInteractions(notificationsFacade)
    }
  }

  @Test
  fun `SHOULD create and send notification ON sendRatingPromptCommand`() {
    val input = RatingPromptCommand(testUserId)

    runBlocking { underTest.sendRatingPromptCommand(input) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = RatingPromptAppNotification(
          delayInDisplayMilliseconds = "120000",
          label = "rating_prompt",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send notification ON sendWelcomeCoinsOfferAvailableNotification`() {
    val input = WelcomeCoinsOfferAvailableNotification(testUserId)

    runBlocking { underTest.sendWelcomeCoinsOfferAvailableNotification(input, DEFAULT_USER_LOCALE) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "Your commitment reward is ready to claim!",
          size = MEDIUM,
          backgroundColor = "#A090B0",
          label = "welcome_coins_offer_available",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send notification ON sendGooglePlayOpenedNotification`() {
    val input = GooglePlayOpenedNotification(testUserId, gameName = "Solitaire verse")

    runBlocking { underTest.sendGooglePlayOpenedNotification(input, DEFAULT_USER_LOCALE) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "You're on Track!",
          shortDescription = "This is it! You're one step away from starting to earn money, download Solitaire verse now to start earning!",
          size = MEDIUM,
          backgroundColor = "#A090B0",
          onClickAction = OnClickActionApiDto.discardNotification(),
          label = "google_play_opened",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send notification ON sendSurveyNotification`() {
    val input = SurveyNotification(testUserId, surveyId = "oncashout")

    runBlocking { underTest.sendSurveyNotification(input) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = SurveyAppNotification(
          surveyId = "oncashout",
          label = "android_survey",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send notification ON sendGameUnlockedNotification`() {
    val input = GameUnlockedNotification(testUserId, gameName = "Merge Blast", widgetId = "42")
    val (expectedButtonAction, expectedOnClickAction) =
      ButtonAction.scrollToUnlockedGameAndHideWidget("42") to OnClickActionApiDto.scrollToUnlockedGameAndHideWidget("42")

    runBlocking { underTest.sendGameUnlockedNotification(input, DEFAULT_USER_LOCALE) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "New game unlocked! \uD83C\uDF89",
          shortDescription = "Hooray! you\'ve unlocked Merge Blast. \uD83C\uDF89 Don\'t miss out and try it out now!",
          size = LARGE,
          backgroundColor = "#5262FB",
          textColor = "#FFFFFF",
          backgroundActions = listOf(REFRESH_USER, REFRESH_OFFERS),
          actionButtons = listOf(
            ButtonApiDto(
              name = "Try it Out!",
              textColor = "#5262FB",
              background = "#FFFFFF",
              action = expectedButtonAction,
            )
          ),
          onClickAction = expectedOnClickAction,
          androidBackgroundOnly = true,
          label = "game_unlocked",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send notification ON sendRemindToPlayNotification`() {
    val input = RemindToPlayNotification(testUserId, title = "Play", description = "PLAY!")

    runBlocking { underTest.sendRemindToPlayNotification(input) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "Play",
          shortDescription = "PLAY!",
          size = LARGE,
          backgroundColor = "#5262FB",
          textColor = "#FFFFFF",
          backgroundActions = listOf(REPLACE_PLACEHOLDERS),
          actionButtons = listOf(
            ButtonApiDto(
              name = "Play Now!",
              textColor = "#5262FB",
              background = "#FFFFFF",
              action = ButtonAction(OPEN_FIRST_FOUND_INSTALLED_GAME),
            )
          ),
          onClickAction = OnClickActionApiDto(OnClickActionName.OPEN_FIRST_FOUND_INSTALLED_GAME),
          label = "remind_to_play"
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send notification ON sendGameCoinGoalReachedNotification`() {
    val input = GameCoinGoalReachedNotification(
      testUserId,
      title = "Task is completed, claim your reward",
      text = "Reached"
    )

    runBlocking { underTest.sendGameCoinGoalReachedNotification(input) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "Task is completed, claim your reward",
          shortDescription = "Reached",
          label = "game_coin_goal_reached",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send notification ON sendContinueIncompleteCashoutNotification`() {
    val input = ContinueIncompleteCashoutNotification(
      userId = testUserId,
      earnings = UserCurrencyEarnings(BigDecimal("3.409"), Currency.getInstance("CAD"), BigDecimal("4.119")),
    )

    runBlocking { underTest.sendContinueIncompleteCashoutNotification(input) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "Let's Get You Back on Track!",
          shortDescription = "Finish cashing-out your $4.11 CAD now",
          longDescription = "Finish cashing-out your $4.11 CAD now",
          size = MEDIUM,
          backgroundColor = "#3AAF49",
          textColor = "#FFFFFF",
          vibrationEnabled = true,
          soundEnabled = true,
          onClickAction = OnClickActionApiDto.continueIncompleteCashout(),
          label = "continue_incomplete_cashout",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD create and send notification ON sendInstallGameReminder`(imagesSet: Boolean) {
    val input = InstallGameReminder(
      userId = testUserId,
      title = "title",
      text = "text",
      icon = if (imagesSet) "icon.ico" else null,
      image = if (imagesSet) "background.jpg" else null,
      onClickAction = OnClickActionApiDto.openLinkInBrowser("link"),
      buttonAction = ButtonAction.openLinkInBrowser("link"),
    )

    runBlocking { underTest.sendInstallGameReminder(input) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "title",
          shortDescription = "text",
          backgroundColor = "#3AAF49",
          textColor = "#FFFFFF",
          icon = if (imagesSet) "https://storage.googleapis.com/public-playtime/images/icon.ico" else "",
          image = if (imagesSet) "https://storage.googleapis.com/public-playtime/images/background.jpg" else null,
          size = LARGE,
          onClickAction = OnClickActionApiDto.openLinkInBrowser("link"),
          actionButtons = listOf(
            ButtonApiDto(
              name = "Play",
              textColor = "#3AAF49",
              background = "#FFFFFF",
              action = ButtonAction.openLinkInBrowser("link"),
            )
          ),
          label = "install_game_reminder",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD send notification ON sendUnclaimedEarningNotification`() = runTest {
    val input = UnclaimedEarningNotification(testUserId, "title", "text")

    underTest.sendUnclaimedEarningNotification(input)

    verifyBlocking(notificationsFacade) {
      sendGenericPushNotification(
        GenericPushNotificationScheduledEventDto(
          userId = testUserId,
          title = "title",
          notificationText = "text",
          label = "earnings_added",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send notification ON sendChallengeSpecialOfferActivatedNotification`() {
    val offerDuration = 2.hours.inWholeMilliseconds
    val input = ChallengeSpecialOfferActivatedNotification(
      userId = testUserId,
      offerDuration = offerDuration
    )

    runBlocking { underTest.sendChallengeSpecialOfferActivatedNotification(input) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          id = NotificationChannelId.CHALLENGE_SPECIAL_OFFER_ACTIVATED,
          notificationType = CustomNotificationType.CUSTOM_NOTIFICATION,
          collapseKey = null,
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "Your Special Offer is Active!",
          shortDescription = "Enjoy special coins & more\n$$$ until the timer ends!",
          icon = "https://storage.googleapis.com/public-playtime/images/boosted_mode/notification_special_offer_banner.png",
          size = LARGE,
          backgroundColor = "#BB6BD9",
          textColor = "#FFFFFF",
          soundEnabled = false,
          vibrationEnabled = true,
          countdownTimerTarget = offerDuration.toString(),
          onClickAction = OnClickActionApiDto.routeToMain(),
          label = "challenge_claim_boosted_mode_offer_activated",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD send notification ON sendRewardEarningsAdded`() = runTest {
    val input = RewardEarningsAddedNotification(
      userId = "persecuti",
      earnings = UserCurrencyEarnings(
        amountUsd = BigDecimal("3.14"),
        userCurrency = Currency.getInstance("EUR"),
        userCurrencyAmount = BigDecimal("2.71"),
      ),
      locale = FR_LOCALE,
    )

    underTest.sendRewardEarningsAdded(input)

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = "persecuti",
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          id = NotificationChannelId.READY_TO_CASH_OUT,
          collapseKey = CustomNotificationCollapseKey.EARNINGS_ADDED,
          title = "FR TRANSLATED: 2,71 € Bonus Received \uD83D\uDE80",
          backgroundColor = "#A9203E",
          textColor = "#FFFFFF",
          icon = "https://storage.googleapis.com/public-playtime/images/challenges/challenges_earnings_push_icon.png",
          size = MEDIUM,
          onClickAction = OnClickActionApiDto.routeToCashout(),
          soundEnabled = true,
          vibrationEnabled = true,
          label = "reward_earnings_added",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD send notification ON sendCashout2xOfferActivatedNotification`() {
    val input = Cashout2xOfferActivatedNotification(testUserId, offerDuration = 9900000, variation = AndroidCashout2xOfferVariation.AndroidCashout2xOfferOn)

    runBlocking { underTest.sendCashout2xOfferActivatedNotification(input) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          id = NotificationChannelId.CASHOUT_2X_OFFER_ACTIVATED,
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "3-Hour $$$$ Bonus Active!",
          backgroundColor = "#D442AE",
          textColor = "#FFFFFF",
          icon = "https://storage.googleapis.com/public-playtime/images/Special%20Offer%20Badge.png",
          size = LARGE,
          shortDescription = "Enjoy special coins and more $$$ until the timer ends",
          vibrationEnabled = true,
          soundEnabled = true,
          countdownTimerTarget = "9900000",
          collapseKey = CustomNotificationCollapseKey.CASHOUT_2X_OFFER_ACTIVATED,
          label = "cashout_x2_offer_accepted",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @ParameterizedTest
  @TypedVariationSource(AndroidCashout2xOfferVariation::class, keys = ["androidCashout2xOfferBM1", "androidCashout2xOfferBM1_25"])
  fun `SHOULD send notification with BM title ON sendCashout2xOfferActivatedNotification WHEN variation is BM`(variation: AndroidCashout2xOfferVariation) {
    val input = Cashout2xOfferActivatedNotification(testUserId, offerDuration = 7200000, variation = variation)

    runBlocking { underTest.sendCashout2xOfferActivatedNotification(input) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          id = NotificationChannelId.CASHOUT_2X_OFFER_ACTIVATED,
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "Limited Time Boost Active",
          backgroundColor = "#D442AE",
          textColor = "#FFFFFF",
          icon = "https://storage.googleapis.com/public-playtime/images/Special%20Offer%20Badge.png",
          size = LARGE,
          shortDescription = "Enjoy special coins and more $$$ until the timer ends",
          vibrationEnabled = true,
          soundEnabled = true,
          countdownTimerTarget = "7200000",
          collapseKey = CustomNotificationCollapseKey.CASHOUT_2X_OFFER_ACTIVATED,
          label = "cashout_x2_offer_accepted",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD send notification ON sendChallengeCompleted`() = runTest {
    val input = ChallengeCompletedNotification(
      userId = "persecuti",
      locale = FR_LOCALE,
    )

    underTest.sendChallengeCompleted(input)

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = "persecuti",
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          id = NotificationChannelId.CUSTOM_NOTIFICATION,
          collapseKey = null,
          title = "FR TRANSLATED: Challenge Completed. Claim Your Golden Ticket!",
          backgroundColor = "#CE1662",
          textColor = "#FFFFFF",
          icon = "https://storage.googleapis.com/public-playtime/images/JustPlay%20Golden%20Ticket.png",
          size = MEDIUM,
          onClickAction = OnClickActionApiDto(ROUTE_TO_MAIN),
          soundEnabled = true,
          vibrationEnabled = true,
          label = "challenge-completed",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD send notification ON sendKeepDoingChallenges`() = runTest {
    val input = KeepDoingChallengesNotification(
      userId = "persecuti",
      title = "keep doing title",
      body = "keep doing body",
    )

    underTest.sendKeepDoingChallenges(input)

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = "persecuti",
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          id = NotificationChannelId.KEEP_DOING_CHALLENGES,
          collapseKey = null,
          title = "keep doing title",
          shortDescription = "keep doing body",
          backgroundColor = "#FF1185",
          textColor = "#FFFFFF",
          icon = "https://storage.googleapis.com/public-playtime/images/Challenges%20Icon.png",
          size = LARGE,
          onClickAction = OnClickActionApiDto(ROUTE_TO_MAIN),
          soundEnabled = true,
          vibrationEnabled = true,
          label = "keep-doing-challenges",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD send notification ON sendCashoutOfferStartedNotification`() {
    abTestingService.mockExperimentVariation(testUserId, ClientExperiment.SPECIAL_CASHOUT_OFFERS, SpecialCashoutOffersVariation.ThreeCashoutOffers)
    val input = CashoutOfferStarted(testUserId, gameName = "testGame")

    runBlocking { underTest.sendCashoutOfferStartedNotification(input) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          id = NotificationChannelId.SPECIAL_CASHOUT_OFFER_ACTIVATED,
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "Special Offer Active!",
          backgroundColor = "#D442AE",
          textColor = "#FFFFFF",
          icon = "https://storage.googleapis.com/public-playtime/images/Special%20Offer%20Badge.png",
          size = LARGE,
          shortDescription = "Enjoy special coins and more $$$ from testGame until the timer ends",
          vibrationEnabled = true,
          soundEnabled = true,
          countdownTimerTarget = "3600000",
          collapseKey = CustomNotificationCollapseKey.CUSTOM_NOTIFICATION,
          label = "cashout_offer_activated",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD send notification ON sendCashoutOfferStartedNotification WITH ThreeRandomCashoutOffers variation`() {
    abTestingService.mockExperimentVariation(testUserId, ClientExperiment.SPECIAL_CASHOUT_OFFERS, SpecialCashoutOffersVariation.ThreeRandomCashoutOffers)
    val input = CashoutOfferStarted(testUserId, gameName = "testGame")

    runBlocking { underTest.sendCashoutOfferStartedNotification(input) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          id = NotificationChannelId.SPECIAL_CASHOUT_OFFER_ACTIVATED,
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "1-Hour $$$ Bonus Active!",
          backgroundColor = "#D442AE",
          textColor = "#FFFFFF",
          icon = "https://storage.googleapis.com/public-playtime/images/Special%20Offer%20Badge.png",
          size = LARGE,
          shortDescription = "Enjoy special coins and more $$$ from testGame until the timer ends",
          vibrationEnabled = true,
          soundEnabled = true,
          countdownTimerTarget = "3600000",
          collapseKey = CustomNotificationCollapseKey.CUSTOM_NOTIFICATION,
          label = "cashout_offer_activated",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD send notification ON sendDayStreakRewardReadyNotification`() {
    val input = DayStreakRewardReadyNotification(testUserId)

    runBlocking { underTest.sendDayStreakRewardReadyNotification(input) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          id = NotificationChannelId.CASH_STREAK,
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "Your streak reward is ready!",
          backgroundColor = "#2D9CDB",
          textColor = "#FFFFFF",
          icon = "https://storage.googleapis.com/public-playtime/images/Cash%20Streak%20Gift%20Box%20Closed.png",
          size = LARGE,
          shortDescription = "The reward is waiting for you to be claimed!",
          vibrationEnabled = true,
          soundEnabled = true,
          label = "day_streak_reward_ready",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD send balance update notification ON sendCashoutOfferBalanceUpdatedNotification`() {
    val input = CashoutOfferBalanceUpdate(testUserId, 123456, hideCoins = false)

    runBlocking { underTest.sendCashoutOfferBalanceUpdatedNotification(input, FR_LOCALE) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          id = NotificationChannelId.BALANCE_UPDATE,
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "Special Coins Collected",
          shortDescription = "You now have 123 456 special coins",
          backgroundColor = "#D442AE",
          textColor = "#FFFFFF",
          icon = "https://storage.googleapis.com/public-playtime/images/notification-coin-balance-updated-icon.png",
          size = LARGE,
          vibrationEnabled = true,
          soundEnabled = true,
          label = "specialCashoutOffers_exp_balance_updated",
          collapseKey = CustomNotificationCollapseKey.CASHOUT_OFFER_BALANCE_UPDATE,
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD send balance update notification ON sendCashoutOfferBalanceUpdatedNotification WHEN hideCoins`() {
    val input = CashoutOfferBalanceUpdate(testUserId, 123456, hideCoins = true)

    runBlocking { underTest.sendCashoutOfferBalanceUpdatedNotification(input, FR_LOCALE) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          id = NotificationChannelId.BALANCE_UPDATE,
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "Special Coins Collected",
          shortDescription = "Keep collecting your special coins!",
          backgroundColor = "#D442AE",
          textColor = "#FFFFFF",
          icon = "https://storage.googleapis.com/public-playtime/images/notification-coin-balance-updated-icon.png",
          size = LARGE,
          vibrationEnabled = true,
          soundEnabled = true,
          label = "specialCashoutOffers_exp_balance_updated",
          collapseKey = CustomNotificationCollapseKey.CASHOUT_OFFER_BALANCE_UPDATE,
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD send notification ON sendOfferwallNowAllowedNotification`() {
    val input = OnOfferwallNowAllowedNotification(testUserId)

    runBlocking { underTest.sendOfferwallNowAllowedNotification(input) }

    verifyBlocking(notificationsFacade) {
      sendGenericPushNotification(
        GenericPushNotificationScheduledEventDto(
          userId = testUserId,
          title = "\uD83D\uDD25 You’ve Unlocked High Payouts!",
          notificationText = "Try our special offers now — they’re your ticket to cashing out big. Don’t let this chance slip away!",
          label = "offerwall_now_allowed"
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD not send notification ON sendGameRankUpdated WHEN rank is ZERO`() {
    val input = GameRankUpdatedNotification(
      userId = testUserId,
      gameName = "Solitaire",
      updatedRank = GameRank.ZERO
    )

    runBlocking { underTest.sendGameRankUpdated(input) }

    verifyNoMoreInteractions(notificationsFacade)
  }

  @ParameterizedTest
  @ValueSource(strings = ["ONE", "TWO", "THREE"])
  fun `SHOULD send notification ON sendGameRankUpdated WHEN rank is not ZERO`(rankName: String) {
    val rank = GameRank.valueOf(rankName)
    val expectedStars = when (rank) {
      GameRank.ONE -> "⭐"
      GameRank.TWO -> "⭐⭐"
      GameRank.THREE -> "⭐⭐⭐"
      GameRank.ZERO -> throw IllegalArgumentException("ZERO rank should not be tested here")
    }
    val input = GameRankUpdatedNotification(
      userId = testUserId,
      gameName = "Solitaire",
      updatedRank = rank
    )

    runBlocking { underTest.sendGameRankUpdated(input) }

    verifyBlocking(notificationsFacade) {
      sendGenericPushNotification(
        GenericPushNotificationScheduledEventDto(
          userId = testUserId,
          notificationText = "Your rank in Solitaire is $expectedStars!",
          label = "game_rank_updated",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD send boosted mode CashoutReady push WHEN boosted mode is active`() {
    val input = EarningsAddedNotification(
      userId = testUserId,
      earnings = UserCurrencyEarnings(BigDecimal("3.409"), Currency.getInstance("CAD"), BigDecimal("4.119")),
      userHasCashouts = false
    )
    val uiConfig = mock<BoostedMode.UiConfig> {
      on { this.readyToCashout } doReturn BoostedMode.PushNotificationOverride("titleKey", "descrKey")
    }
    val boostedMode = mock<BoostedMode> {
      on { this.uiConfig } doReturn uiConfig
    }
    boostedModeService.mock({ findCurrentBoostedMode(eq(testUserId), any()) }, boostedMode)
    translationService.mock({ tryTranslate("titleKey", DEFAULT_USER_LOCALE, testUserId) }, "Title")
    translationService.mock({ tryTranslate("descrKey", DEFAULT_USER_LOCALE, testUserId) }, "Description")

    runBlocking { underTest.sendEarningsAddedNotification(input, DEFAULT_USER_LOCALE) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          id = NotificationChannelId.READY_TO_CASH_OUT,
          title = "Title",
          shortDescription = "Description",
          backgroundColor = "#3AAF49",
          textColor = "#FFFFFF",
          icon = "https://storage.googleapis.com/public-playtime/images/notification_cashout.png",
          size = MEDIUM,
          onClickAction = OnClickActionApiDto.routeToCashout(),
          label = "earnings_added",
        )
      )
    }
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD send silent notification ON sendBalanceUpdatedNotification when receiving silent settings`(value: Boolean) {
    androidSilentCoinUpdateService.mock(
      { silentCoinUpdateNotificationSettings(testUserId) },
      AndroidSilentCoinUpdateService.SilentNotificationSettings(vibrationEnabled = value, soundEnabled = !value)
    )
    balanceUpdatedNotificationWithAmountExpService.mock(
      { getTitleAndBody(testUserId, FR_LOCALE, 100500, 0) },
      "FR TRANSLATED: Balance Update!" to "FR TRANSLATED: You now have 100 500 loyalty coins!"
    )

    val input = BalanceUpdatedNotification(userIdParam = testUserId, coins = 100500)

    runBlocking { underTest.sendBalanceUpdatedNotification(input, FR_LOCALE) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          id = NotificationChannelId.BALANCE_UPDATE,
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "FR TRANSLATED: Balance Update!",
          shortDescription = "FR TRANSLATED: You now have 100 500 loyalty coins!",
          backgroundColor = "#5262FB",
          textColor = "#FFFFFF",
          icon = "https://storage.googleapis.com/public-playtime/images/notification-coin-balance-updated-icon.png",
          size = MEDIUM,
          vibrationEnabled = value,
          soundEnabled = !value,
          backgroundActions = listOf(REFRESH_USER, REFRESH_OFFERS),
          collapseKey = CustomNotificationCollapseKey.BALANCE_UPDATE,
          label = "balance_updated",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD send silent notification ON sendCashoutOfferBalanceUpdatedNotification when receiving silent settings`(value: Boolean) {
    androidSilentCoinUpdateService.mock(
      { silentCoinUpdateNotificationSettings(testUserId) },
      AndroidSilentCoinUpdateService.SilentNotificationSettings(vibrationEnabled = value, soundEnabled = !value)
    )

    val input = CashoutOfferBalanceUpdate(testUserId, 123456, hideCoins = false)

    runBlocking { underTest.sendCashoutOfferBalanceUpdatedNotification(input, FR_LOCALE) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          id = NotificationChannelId.BALANCE_UPDATE,
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "Special Coins Collected",
          shortDescription = "You now have 123 456 special coins",
          backgroundColor = "#D442AE",
          textColor = "#FFFFFF",
          icon = "https://storage.googleapis.com/public-playtime/images/notification-coin-balance-updated-icon.png",
          size = LARGE,
          vibrationEnabled = value,
          soundEnabled = !value,
          label = "specialCashoutOffers_exp_balance_updated",
          collapseKey = CustomNotificationCollapseKey.CASHOUT_OFFER_BALANCE_UPDATE,
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD send boosted mode notification ON sendBalanceUpdatedNotification WHEN boosted mode is active and hideCoins is false`() = runTest {
    val uiConfig = mock<BoostedMode.UiConfig> {
      on { this.balanceUpdate } doReturn BoostedMode.BalanceUpdatePushNotificationOverride("bmTitle", "bmDescr", "bmDescrNoCoins")
    }
    val boostedMode = mock<BoostedMode> {
      on { this.uiConfig } doReturn uiConfig
      on { this.coinsCoefficient } doReturn 3.0
    }
    boostedModeService.mock({ findCurrentBoostedMode(eq(testUserId), anyOrNull()) }, boostedMode)
    translationService.mock({ tryTranslate("bmTitle", DEFAULT_USER_LOCALE, testUserId) }, "BM Title")
    translationService.mock({ tryTranslate("bmDescr", DEFAULT_USER_LOCALE, testUserId) }, "BM Description %s")
    translationService.mock({ tryTranslate("bmDescrNoCoins", DEFAULT_USER_LOCALE, testUserId) }, "BM No Coins")

    val input = BalanceUpdatedNotification(userIdParam = testUserId, coins = 50, hideCoins = false)

    underTest.sendBalanceUpdatedNotification(input, DEFAULT_USER_LOCALE)

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          id = NotificationChannelId.BALANCE_UPDATE,
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "BM Title",
          shortDescription = "BM Description 150",
          backgroundColor = "#5262FB",
          textColor = "#FFFFFF",
          icon = "https://storage.googleapis.com/public-playtime/images/notification-coin-balance-updated-icon.png",
          size = MEDIUM,
          backgroundActions = listOf(REFRESH_USER, REFRESH_OFFERS),
          label = "balance_updated",
          collapseKey = CustomNotificationCollapseKey.BALANCE_UPDATE,
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD send boosted mode notification ON sendBalanceUpdatedNotification WHEN boosted mode is active and hideCoins is true`() = runTest {
    val uiConfig = mock<BoostedMode.UiConfig> {
      on { this.balanceUpdate } doReturn BoostedMode.BalanceUpdatePushNotificationOverride("bmTitle", "bmDescr", "bmDescrNoCoins")
    }
    val boostedMode = mock<BoostedMode> {
      on { this.uiConfig } doReturn uiConfig
      on { this.coinsCoefficient } doReturn 2.0
    }
    boostedModeService.mock({ findCurrentBoostedMode(eq(testUserId), anyOrNull()) }, boostedMode)
    translationService.mock({ tryTranslate("bmTitle", DEFAULT_USER_LOCALE, testUserId) }, "BM Title")
    translationService.mock({ tryTranslate("bmDescr", DEFAULT_USER_LOCALE, testUserId) }, "BM Description %s")
    translationService.mock({ tryTranslate("bmDescrNoCoins", DEFAULT_USER_LOCALE, testUserId) }, "BM No Coins")

    val input = BalanceUpdatedNotification(userIdParam = testUserId, coins = 100, hideCoins = true)

    underTest.sendBalanceUpdatedNotification(input, DEFAULT_USER_LOCALE)

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          id = NotificationChannelId.BALANCE_UPDATE,
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          title = "BM Title",
          shortDescription = "BM No Coins",
          backgroundColor = "#5262FB",
          textColor = "#FFFFFF",
          icon = "https://storage.googleapis.com/public-playtime/images/notification-coin-balance-updated-icon.png",
          size = MEDIUM,
          backgroundActions = listOf(REFRESH_USER, REFRESH_OFFERS),
          label = "balance_updated",
          collapseKey = CustomNotificationCollapseKey.BALANCE_UPDATE,
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  companion object {
    private val now = Instant.now()
    private val testUserId: String = UUID.randomUUID().toString()
  }
}
