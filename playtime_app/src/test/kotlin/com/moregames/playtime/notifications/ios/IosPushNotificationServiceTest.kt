package com.moregames.playtime.notifications.ios

import com.moregames.base.messaging.customnotification.CustomNotificationDto
import com.moregames.base.messaging.customnotification.CustomNotificationType.*
import com.moregames.base.messaging.customnotification.OnClickActionApiDto
import com.moregames.base.util.RandomGenerator
import com.moregames.playtime.earnings.dto.UserCurrencyEarnings
import com.moregames.playtime.notifications.NotificationsFacade
import com.moregames.playtime.notifications.PushNotification.CrossPlatformPushNotification.*
import com.moregames.playtime.notifications.PushNotification.IosPushNotification.PlayFirstGameReminder
import com.moregames.playtime.notifications.PushNotification.IosPushNotification.ShareYourExperienceNotification
import com.moregames.playtime.translations.TranslationResource
import com.moregames.playtime.translations.UserTranslationService
import com.moregames.playtime.util.DEFAULT_USER_LOCALE
import com.moregames.playtime.utils.cashoutTransactionStub
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.text.NumberFormat
import java.util.*

class IosPushNotificationServiceTest {
  private val translationService: UserTranslationService = mock {
    onBlocking { translateOrDefault(any(), any(), any()) } doAnswer { (it.arguments[0] as TranslationResource).defaultValue }
  }
  private val notificationsFacade: NotificationsFacade = mock()
  private val randomGenerator: RandomGenerator = mock {
    on { nextUUID() } doReturn "08ab62f7-2432-4bfa-a624-980f5c9181cc"
  }
  private val testUserId: String = UUID.randomUUID().toString()

  private val underTest: IosPushNotificationService = IosPushNotificationService(translationService, notificationsFacade, randomGenerator)

  @Test
  fun `SHOULD create and send notification ON sendPlayFirstGameReminder`() {
    val input = PlayFirstGameReminder(testUserId)

    runBlocking { underTest.sendPlayFirstGameReminder(input) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          notificationType = IOS_REMIND_TO_PLAY_NOTIFICATION,
          shortDescription = "To collect your first Justplay points try out one of those games",
          label = "play_first_game",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @ParameterizedTest
  @CsvSource(
    delimiter = '|', value = [
      "132|You now have 100,500 loyalty points!",
      "130|You now have 100,500 loyalty points!",
      "133|You now have 100,500 testing experience points!",
      "134|You now have 100,500 testing experience points!",
    ]
  )
  fun `SHOULD create and send notification ON sendBalanceUpdatedNotification WHEN depending app version`(
    appVersion: Int,
    expectedShortDescription: String
  ) {
    val input = BalanceUpdatedNotification(userIdParam = testUserId, coins = 100500)


    runBlocking { underTest.sendBalanceUpdatedNotification(input, DEFAULT_USER_LOCALE, appVersion) }

    verifyBlocking(notificationsFacade, times(1)) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          notificationType = IOS_COINS_BALANCE_UPDATED,
          title = "JustPlay",
          shortDescription = expectedShortDescription,
          label = "balance_updated",
        )
      )
    }
  }

  @ParameterizedTest
  @CsvSource(
    delimiter = '|', value = [
      "132|Keep collecting your loyalty coins!",
      "130|Keep collecting your loyalty coins!",
      "133|Keep collecting your testing experience points!",
      "134|Keep collecting your testing experience points!",
    ]
  )
  fun `SHOULD create and send notification ON sendBalanceUpdatedNotification WHEN hideCoins`(
    appVersion: Int,
    expectedShortDescription: String
  ) {
    val input = BalanceUpdatedNotification(userIdParam = testUserId, coins = 100500, hideCoins = true)

    runBlocking { underTest.sendBalanceUpdatedNotification(input, DEFAULT_USER_LOCALE, appVersion) }

    verifyBlocking(notificationsFacade, times(1)) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          notificationType = IOS_COINS_BALANCE_UPDATED,
          title = "JustPlay",
          shortDescription = expectedShortDescription,
          label = "balance_updated",
        )
      )
    }
  }

  @Test
  fun `SHOULD create and send notification ON sendBalanceUpdatedNotification WHEN it is ios boosted game coins`() {
    val input = BalanceUpdatedNotification(userIdParam = testUserId, coins = 100500, isIosBoostedGameCoins = true)
    val formatedCoins = NumberFormat.getNumberInstance(DEFAULT_USER_LOCALE).format(100500)

    runBlocking { underTest.sendBalanceUpdatedNotification(input, DEFAULT_USER_LOCALE, 133) }

    verifyBlocking(notificationsFacade, times(1)) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          notificationType = IOS_COINS_BALANCE_UPDATED,
          title = "JustPlay",
          shortDescription = "Boosted Game of the Day!!! \uD83D\uDD25\nYou have $formatedCoins coins!",
          label = "balance_updated",
        )
      )
    }
  }

  @Test
  fun `SHOULD create and send notification ON sendCashoutProcessedNotification`() {
    val input = CashoutProcessedNotification(testUserId, cashoutTransaction = cashoutTransactionStub)

    runBlocking { underTest.sendCashoutProcessedNotification(input, DEFAULT_USER_LOCALE) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          notificationType = IOS_CASHOUT_PROCESSED,
          title = "Congratulations!",
          shortDescription = "Your reward is ready!",
          label = "cashout_processed",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send notification ON sendShareYourExperienceNotification`() {
    val input = ShareYourExperienceNotification(testUserId)

    runBlocking { underTest.sendShareYourExperienceNotification(input) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          notificationType = IOS_SHARE_YOUR_EXPERIENCE,
          title = "Share your opinion for a reward",
          shortDescription = "Tell us about your gaming experience and get a \$50 Amazon gift card. Tap to check it out!",
          label = "share_your_experience",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send notification ON sendEarningsAddedNotification`() {
    val input = EarningsAddedNotification(
      userId = testUserId,
      earnings = UserCurrencyEarnings(BigDecimal("3.409"), Currency.getInstance("CAD"), BigDecimal("4.119")),
      userHasCashouts = false
    )

    runBlocking { underTest.sendEarningsAddedNotification(input, DEFAULT_USER_LOCALE) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          notificationType = IOS_EARNINGS_ADDED,
          title = "$4.11 CAD added to your JustPlay balance",
          shortDescription = "Congratulations! Now you can withdraw your JustPlay reward!",
          onClickAction = OnClickActionApiDto.routeToCashout(),
          label = "earnings_added",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send notification ON sendRatingPromptCommand`() {
    val input = RatingPromptCommand(testUserId)

    runBlocking { underTest.sendRatingPromptCommand(input) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          notificationType = IOS_RATING_PROMPT,
          label = "rating_prompt",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD create and send notification ON sendGameCoinGoalReachedNotification`() {
    val input = GameCoinGoalReachedNotification(testUserId, text = "Reached", title = "Title")

    runBlocking { underTest.sendGameCoinGoalReachedNotification(input) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          notificationType = IOS_REACH_GAME_COIN_GOAL,
          title = "Title",
          shortDescription = "Reached",
          label = "game_coin_goal_reached",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }

  @Test
  fun `SHOULD send notification ON sendUnclaimedEarningNotification`() {
    val input = UnclaimedEarningNotification(testUserId, title = "title", text = "text")

    runBlocking { underTest.sendUnclaimedEarningNotification(input) }

    verifyBlocking(notificationsFacade) {
      sendMessage(
        userId = testUserId,
        message = CustomNotificationDto(
          notificationId = "08ab62f7-2432-4bfa-a624-980f5c9181cc",
          notificationType = IOS_EARNINGS_ADDED,
          title = "title",
          shortDescription = "text",
          label = "earnings_added",
        )
      )
    }
    verifyNoMoreInteractions(notificationsFacade)
  }
}