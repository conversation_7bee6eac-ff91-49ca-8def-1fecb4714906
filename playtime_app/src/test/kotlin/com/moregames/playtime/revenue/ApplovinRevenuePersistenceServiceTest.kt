package com.moregames.playtime.revenue

import assertk.assertThat
import assertk.assertions.containsOnly
import assertk.assertions.isEqualByComparingTo
import assertk.assertions.isEqualTo
import com.justplayapps.service.rewarding.earnings.table.ApplovinRevenuesTable
import com.moregames.base.table.CurrentGenericRevenueTable
import com.moregames.base.table.DatabaseExtension
import com.moregames.base.table.GenericRevenueTotalsTable
import com.moregames.base.table.UserApplovinRevenueByPeriodsTable
import com.moregames.base.util.TEST_IO_SCOPE
import com.moregames.playtime.revenue.applovin.ApplovinRevenuePersistenceService
import com.moregames.playtime.revenue.applovin.table.ReportedApplovinRevenuesTable
import com.moregames.playtime.user.prepareUser
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.setMain
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import java.util.*

@ExtendWith(DatabaseExtension::class)
class ApplovinRevenuePersistenceServiceTest(
  private val database: Database,
) {

  lateinit var service: ApplovinRevenuePersistenceService

  init {
    Dispatchers.setMain(StandardTestDispatcher())
  }

  private val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)

  @BeforeEach
  fun before() {
    service = ApplovinRevenuePersistenceService(
      database = database,
      coroutineScope = { TEST_IO_SCOPE },
    )

    transaction(database) {
      CurrentGenericRevenueTable.deleteAll()
      ReportedApplovinRevenuesTable.deleteAll()
      ApplovinRevenuesTable.deleteAll()
      UserApplovinRevenueByPeriodsTable.deleteAll()
    }
  }

  @Test
  internal fun `SHOULD return application IDs ON loadImportedApplovinApplicationIds`() {
    transaction(database) {
      ReportedApplovinRevenuesTable.insert {
        it[adUnitId] = "ad"
        it[googleAdId] = "gaid"
        it[revenueUsd] = BigDecimal("5")
        it[day] = LocalDate.now()
        it[applicationId] = "app1"
      }
    }

    val actual = runBlocking {
      service.loadImportedApplovinApplicationIds()
    }

    assertThat(actual).containsOnly("app1")
  }

  @Test
  internal fun `SHOULD return revenue by application IDs ON loadApplovinRevenueByApplicationId`() {
    val now = LocalDate.now()
    transaction(database) {

      ApplovinRevenuesTable.insert {
        it[userId] = database.prepareUser()
        it[revenueUsd] = BigDecimal("5")
        it[day] = now
        it[adUnitId] = "ad"
        it[applicationId] = "app1"
      }
      ApplovinRevenuesTable.insert {
        it[userId] = database.prepareUser()
        it[revenueUsd] = BigDecimal("3")
        it[day] = now
        it[adUnitId] = "ad"
        it[applicationId] = "app2"
      }
    }

    val actual = runBlocking {
      service.loadApplovinRevenueByApplicationId(now)
    }

    assertThat(actual.keys).containsOnly("app1", "app2")
    assertThat(actual["app1"]!!.compareTo(BigDecimal(5))).isEqualTo(0)
    assertThat(actual["app2"]!!.compareTo(BigDecimal(3))).isEqualTo(0)
  }

  @Test
  fun `SHOULD add generic total revenue for user ON addGenericRevenueTotals`() {
    val userId1 = database.prepareUser()
    addGenericTotalRevenue(userId1, BigDecimal("1.00"))

    transaction(database) {
      GenericRevenueTotalsTable
        .select { GenericRevenueTotalsTable.userId eq userId1 }
        .first()
        .let { assertThat(it[GenericRevenueTotalsTable.revenueAmount]).isEqualByComparingTo(BigDecimal("1.00")) }
    }
  }

  @Test
  fun `SHOULD transfer data from external table to applovin revenues ON transferApplovinRevenuesFromExternal`() {
    val usersAmount = 10
    val googleAdIds = (0 until usersAmount).map { UUID.randomUUID().toString() }
    val userIds = (0 until usersAmount).map { database.prepareUser(googleAdId = googleAdIds[it]) }

    googleAdIds.forEach {
      addReportedApplovinRevenue(googleAdId = it, revenueUsd = BigDecimal("2"))
    }

    runBlocking {
      service.transferApplovinRevenuesFromExternal()
    }

    transaction(database) {
      ApplovinRevenuesTable
        .select { ApplovinRevenuesTable.userId inList (userIds) }
        .map { assertThat(it[ApplovinRevenuesTable.revenueUsd]).isEqualByComparingTo(BigDecimal("2")) }
        .count()
        .let { assertThat(it).equals(usersAmount) }
    }

    transaction(database) {
      ReportedApplovinRevenuesTable
        .selectAll()
        .count()
        .let { assertThat(it).isEqualTo(0) }
    }

  }

  private fun addReportedApplovinRevenue(googleAdId: String, revenueUsd: BigDecimal) =
    transaction(database) {
      ReportedApplovinRevenuesTable.insert {
        it[adUnitId] = UUID.randomUUID().toString()
        it[ReportedApplovinRevenuesTable.googleAdId] = googleAdId
        it[applicationId] = "com.relaxingbraintraining.hexapuzzle"
        it[ReportedApplovinRevenuesTable.revenueUsd] = revenueUsd
        it[day] = LocalDate.now()
      }
    }

  private fun addGenericTotalRevenue(
    userId: String,
    amount: BigDecimal,
    ofwAmount: BigDecimal = BigDecimal.ZERO,
    day2Amount: BigDecimal = BigDecimal.ZERO,
    day0Amount: BigDecimal = BigDecimal.ZERO
  ) =
    transaction(database) {
      GenericRevenueTotalsTable
        .insert {
          it[GenericRevenueTotalsTable.userId] = userId
          it[revenueAmount] = amount
          it[offerwallRevenue] = ofwAmount
          it[day2Revenue] = day2Amount
          it[day0Revenue] = day0Amount
        }
    }
}
