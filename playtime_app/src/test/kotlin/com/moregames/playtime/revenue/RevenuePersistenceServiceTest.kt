package com.moregames.playtime.revenue

import assertk.assertThat
import assertk.assertions.isEqualByComparingTo
import assertk.assertions.isEqualTo
import assertk.assertions.isNull
import com.justplayapps.service.rewarding.earnings.table.ApplovinRevenuesTable
import com.justplayapps.service.rewarding.earnings.table.GenericRevenueDailyTotalsMk2Table
import com.justplayapps.service.rewarding.earnings.table.MetaUserEarningsTable
import com.justplayapps.service.rewarding.revenue.RevenuePersistenceService
import com.moregames.base.applovin.JP_AD_ITEM_ID_ANDROID
import com.moregames.base.applovin.JP_AD_ITEM_ID_IOS
import com.moregames.base.messaging.dto.RevenueReceivedEventDto.RevenueSource.APPLOVIN
import com.moregames.base.messaging.dto.RevenueReceivedEventDto.RevenueSource.IRON_SOURCE
import com.moregames.base.table.CurrentGenericRevenueTable
import com.moregames.base.table.DatabaseExtension
import com.moregames.base.table.GenericRevenueTotalsTable
import com.moregames.base.table.UserApplovinRevenueByPeriodsTable
import com.moregames.base.util.TimeService
import com.moregames.base.util.prepareGame
import com.moregames.base.util.prepareVideoAdUnitGame
import com.moregames.base.util.toBeginningOf5MinInterval
import com.moregames.playtime.revenue.applovin.table.ReportedApplovinRevenuesTable
import com.moregames.playtime.user.addCurrentGenericRevenue
import com.moregames.playtime.user.addRevenueByPeriods
import com.moregames.playtime.user.prepareUser
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.setMain
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.math.BigDecimal
import java.time.Duration
import java.time.Instant
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import java.util.*

@ExtendWith(DatabaseExtension::class)
class RevenuePersistenceServiceTest(
  private val database: Database,
) {

  lateinit var service: RevenuePersistenceService

  init {
    Dispatchers.setMain(StandardTestDispatcher())
  }

  private val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
  private val timeService: TimeService = mock()

  @BeforeEach
  fun before() {
    whenever(timeService.now()).thenReturn(now)
    service = RevenuePersistenceService(
      database = database,
      timeService = timeService
    )

    transaction(database) {
      CurrentGenericRevenueTable.deleteAll()
      ReportedApplovinRevenuesTable.deleteAll()
      ApplovinRevenuesTable.deleteAll()
      UserApplovinRevenueByPeriodsTable.deleteAll()
    }
  }

  @Test
  fun `SHOULD return user revenue on calculateTotalUserRevenue`() {
    val userId = database.prepareUser()
    addGenericTotalRevenue(userId, BigDecimal.TEN, BigDecimal("5.15"), BigDecimal("1.25"))

    val (revenue, ofwRevenue, day2Revenue) = runBlocking {
      service.getRevenueTotals(userId)
    }!!

    assertThat(revenue).isEqualByComparingTo(BigDecimal("10"))
    assertThat(ofwRevenue).isEqualByComparingTo(BigDecimal("5.15"))
    assertThat(day2Revenue).isEqualByComparingTo(BigDecimal("1.25"))
  }

  @Test
  fun `SHOULD add generic total revenue for user ON addGenericRevenueTotals`() {
    val userId1 = database.prepareUser()
    addGenericTotalRevenue(userId1, BigDecimal("1.00"))

    transaction(database) {
      GenericRevenueTotalsTable
        .select { GenericRevenueTotalsTable.userId eq userId1 }
        .first()
        .let { assertThat(it[GenericRevenueTotalsTable.revenueAmount]).isEqualByComparingTo(BigDecimal("1.00")) }
    }
  }

  @Test
  fun `SHOULD return latest revenue time ON getLatestRevenueTime`() {
    val userId = database.prepareUser()
    val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)

    transaction(database) {
      GenericRevenueTotalsTable.insert {
        it[revenueAmount] = BigDecimal.ONE
        it[GenericRevenueTotalsTable.userId] = userId
        it[updatedAt] = now.minusSeconds(100)
      }
    }

    val actual = runBlocking { service.getLatestRevenueTime(userId) }

    assertThat(actual).isEqualTo(now.minusSeconds(100))
  }

  @Test
  fun `SHOULD return null ON getLatestRevenueTime WHEN no revenue for user`() {
    val userId = database.prepareUser()

    val actual = runBlocking { service.getLatestRevenueTime(userId) }

    assertThat(actual).isNull()
  }

  @Test
  fun `SHOULD return null ON getLatestRevenueTime WHEN revenue for user is zero`() {
    val userId = database.prepareUser()

    transaction(database) {
      GenericRevenueTotalsTable.insert {
        it[GenericRevenueTotalsTable.userId] = userId
        it[revenueAmount] = BigDecimal.ZERO
      }
    }

    val actual = runBlocking { service.getLatestRevenueTime(userId) }

    assertThat(actual).isNull()
  }

  @Test
  fun `SHOULD mark revenue with fake meta id ON resetCurrentRevenue`() {
    val userId = database.prepareUser()
    val userIdOther = database.prepareUser()

    addCurrentGenericRevenue(userId, amount = BigDecimal("3.14"))
    addCurrentGenericRevenue(userId, amount = BigDecimal("2.71"))
    addCurrentGenericRevenue(userIdOther, amount = BigDecimal("5.5"))

    transaction(database) {
      MetaUserEarningsTable.insert { it[id] = -10 }

      CurrentGenericRevenueTable
        .update({ (CurrentGenericRevenueTable.userId eq userId) and (CurrentGenericRevenueTable.revenueAmount eq BigDecimal("2.71")) }) {
          it[metaUserEarningsId] = -10
        }
    }

    runBlocking { service.resetCurrentRevenue(userId) }

    val metas = transaction(database) {
      CurrentGenericRevenueTable
        .select { CurrentGenericRevenueTable.userId eq userId }
        .orderBy(CurrentGenericRevenueTable.metaUserEarningsId)
        .map { it[CurrentGenericRevenueTable.metaUserEarningsId] }
    }

    assertThat(metas).isEqualTo(listOf(-10, -1))
    transaction(database) {
      CurrentGenericRevenueTable
        .select { CurrentGenericRevenueTable.userId eq userIdOther }
        .first()
        .let {
          assertThat(it[CurrentGenericRevenueTable.metaUserEarningsId]).isNull()
        }
    }
  }

  @Test
  fun `SHOULD remove old daily revenue totals ON removeDailyRevenueOldTotalsBatch`() {
    transaction(database) {
      GenericRevenueDailyTotalsMk2Table.deleteAll()
    }

    val userIds = (1..5).map { database.prepareUser() }
    val longAgo = LocalDate.of(2021, 1, 1)

    for (userId in userIds) {
      addGenericRevenueDailyTotals(userId, BigDecimal("1.917"), "APPLOVIN")
      addGenericRevenueDailyTotals(userId, BigDecimal("1.519"), "APPLOVIN", longAgo)
    }

    runBlocking {
      service.removeDailyRevenueOldTotalsBatch(8)
    }.let { assertThat(it).isEqualTo(5) }

    transaction(database) {
      GenericRevenueDailyTotalsMk2Table
        .select { GenericRevenueDailyTotalsMk2Table.userId inList userIds }
        .sumOf {
          assertThat(it[GenericRevenueDailyTotalsMk2Table.revenueAmount]).isEqualByComparingTo(BigDecimal("1.917"))
          1L
        }.let { assertThat(it).isEqualTo(5) }

    }
  }

  @Test
  fun `SHOULD get applovin inter revenue ON getApplovinInterRevenue`() {
    val user = database.prepareUser()
    val game = database.prepareGame()

    addApplovinRevByPeriods(user, game, Instant.parse("2025-01-15T12:15:00Z"), BigDecimal("1.1")) // too early
    addApplovinRevByPeriods(user, game, Instant.parse("2025-01-15T12:20:00Z"), BigDecimal("2.2"))
    addApplovinRevByPeriods(user, game, Instant.parse("2025-01-15T12:25:00Z"), BigDecimal("4.4"))
    addApplovinRevByPeriods(user, game, Instant.parse("2025-01-15T12:30:00Z"), BigDecimal("8.8")) // too late

    runBlocking {
      service.getApplovinInterRevenue(
        user,
        from = Instant.parse("2025-01-15T12:17:00Z"),
        to = Instant.parse("2025-01-15T12:28:00Z")
      )
    }.let { result ->
      assertThat(result).isEqualByComparingTo(BigDecimal("6.6"))
    }
  }

  @Test
  fun `SHOULD get zero applovin inter revenue ON getApplovinInterRevenue WHEN no data`() {
    val user = database.prepareUser()
    val game = database.prepareGame()

    addApplovinRevByPeriods(user, game, Instant.parse("2025-01-15T12:15:00Z"), BigDecimal("1.1")) // too early
    addApplovinRevByPeriods(user, game, Instant.parse("2025-01-15T12:30:00Z"), BigDecimal("8.8")) // too late

    runBlocking {
      service.getApplovinInterRevenue(
        user,
        from = Instant.parse("2025-01-15T12:17:00Z"),
        to = Instant.parse("2025-01-15T12:28:00Z")
      )
    }.let { result ->
      assertThat(result).isEqualTo(BigDecimal.ZERO)
    }
  }

  @Test
  fun `SHOULD count periods from revenue by periods by games on getApplovinRevenue5minPeriodsCount`() {
    val sinceDate = now.minus(2, ChronoUnit.HOURS)
    val userId = database.prepareUser()
    val userId2 = database.prepareUser()
    val gameId = database.prepareGame()
    val gameId2 = database.prepareGame()

    database.addRevenueByPeriods(userId, gameId, sinceDate.minusSeconds(360)) // out of range
    database.addRevenueByPeriods(userId, gameId, sinceDate.plusSeconds(60)) // in range game1, h1
    database.addRevenueByPeriods(userId, gameId2, sinceDate.plusSeconds(120)) // in range game2. h1
    database.addRevenueByPeriods(userId, gameId, sinceDate.plus(1, ChronoUnit.HOURS).plusSeconds(15)) // in range game1, h2
    database.addRevenueByPeriods(userId, gameId, sinceDate.plus(1, ChronoUnit.HOURS).plusSeconds(1115)) // in range game1, h2
    database.addRevenueByPeriods(userId, gameId, sinceDate.plus(1, ChronoUnit.HOURS).plusSeconds(2115)) // in range game1, h2
    database.addRevenueByPeriods(userId, gameId, sinceDate.plus(2, ChronoUnit.HOURS)) // in range game1, h3
    database.addRevenueByPeriods(userId2, gameId, sinceDate.plusSeconds(60)) // other user
    database.addRevenueByPeriods(userId2, gameId, sinceDate.minusSeconds(60)) // other user + out of range


    runBlocking {
      service.getApplovinRevenue5minPeriodsCount(userId, sinceDate)
    }.let {
      assertThat(it[gameId]).isEqualTo(5)
      assertThat(it[gameId2]).isEqualTo(1)
    }
  }

  @Test
  fun `SHOULD return 24 periods on getApplovinRevenue5minPeriodsCount WHEN user is playing without rest`() {
    val sinceDate = now.minus(2, ChronoUnit.HOURS)
    val userId = database.prepareUser()
    val gameId = database.prepareGame()

    (1..50).forEach {
      database.addRevenueByPeriods(userId, gameId, now.minusSeconds(300L * it).toBeginningOf5MinInterval())
    }

    runBlocking {
      service.getApplovinRevenue5minPeriodsCount(userId, sinceDate)
    }.let {
      assertThat(it[gameId]).isEqualTo(24)
    }
  }

  @Test
  fun `SHOULD calculate user revenue totals for period ON getUserApplovinRevenueByPeriod`() {
    transaction(database) { UserApplovinRevenueByPeriodsTable.deleteAll() }

    val periodStart = Instant.now().minus(18, ChronoUnit.DAYS)
    val periodEnd = periodStart.plus(3, ChronoUnit.HOURS)
    val periodIn = periodStart.plus(5, ChronoUnit.MINUTES)
    val periodIn2 = periodStart.plus(4, ChronoUnit.MINUTES)
    val periodOut = periodStart.minus(5, ChronoUnit.MINUTES)

    val gameId = database.prepareGame()
    val videoAdUnitGameAndroid = database.prepareVideoAdUnitGame(JP_AD_ITEM_ID_ANDROID)
    val videoAdUnitGameIos = database.prepareVideoAdUnitGame(JP_AD_ITEM_ID_IOS)
    val userId = database.prepareUser()
    val userId2 = database.prepareUser()

    database.addCurrentGenericRevenue(userId, gameId, periodIn, APPLOVIN)// other user
    database.addCurrentGenericRevenue(userId2, gameId, periodIn, IRON_SOURCE) // not applovin
    database.addCurrentGenericRevenue(userId2, videoAdUnitGameAndroid, periodIn, APPLOVIN) // jp video ad
    database.addCurrentGenericRevenue(userId2, videoAdUnitGameIos, periodIn, APPLOVIN) // jp video ad
    database.addCurrentGenericRevenue(userId2, gameId, periodOut, APPLOVIN) // out of period
    database.addCurrentGenericRevenue(userId2, gameId, periodIn, APPLOVIN)
    database.addCurrentGenericRevenue(userId2, gameId, periodIn2, APPLOVIN)

    runBlocking {
      service.getUserGameRevenueByPeriod(userId2, periodStart, periodEnd)
    }.let { assertThat(it).isEqualByComparingTo(BigDecimal("2.0")) }
  }

  @Test
  fun `SHOULD return 0 if no revenue ON getUserNonBannerRevenueByPeriod`() {
    transaction(database) { UserApplovinRevenueByPeriodsTable.deleteAll() }

    runBlocking {
      service.getUserGameRevenueByPeriod(UUID.randomUUID().toString(), Instant.now(), Instant.now())
    }.let { assertThat(it).isEqualByComparingTo(BigDecimal.ZERO) }
  }

  @Test
  fun `SHOULD calculate user revenue totals for period ON getApplovinRevenueByPeriod`() {
    val periodStart = Instant.now().minus(18, ChronoUnit.DAYS)
    val periodEnd = periodStart.plus(3, ChronoUnit.HOURS)
    val periodIn1 = periodStart.plus(5, ChronoUnit.MINUTES)
    val periodIn2 = periodStart.plus(4, ChronoUnit.MINUTES)

    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()
    val gameId1 = database.prepareGame()
    val gameId2 = database.prepareGame()

    transaction(database) { CurrentGenericRevenueTable.deleteAll() }

    database.addCurrentGenericRevenue(userId1, gameId1, periodIn1, APPLOVIN)
    database.addCurrentGenericRevenue(userId1, gameId2, periodIn2, APPLOVIN)
    database.addCurrentGenericRevenue(userId2, gameId1, periodIn1, APPLOVIN)

    runBlocking { service.getGameRevenueByPeriod(periodStart, periodEnd) }
      .let { assertThat(it).isEqualByComparingTo(BigDecimal("3.0")) }
  }

  @Test
  fun `SHOULD return 0 ON getApplovinRevenueByPeriod WHEN no appropriate data`() {
    val periodStart = Instant.now().minus(18, ChronoUnit.DAYS)
    val periodIn = periodStart.plus(5, ChronoUnit.MINUTES)
    val periodOutBefore = periodStart.minus(5, ChronoUnit.MINUTES)
    val periodOutAfter = periodStart.plus(6, ChronoUnit.MINUTES)

    val gameId = database.prepareGame()
    val videoAdUnitGameAndroid = database.prepareVideoAdUnitGame(JP_AD_ITEM_ID_ANDROID)
    val videoAdUnitGameIos = database.prepareVideoAdUnitGame(JP_AD_ITEM_ID_ANDROID)
    val userId = database.prepareUser()

    transaction(database) { CurrentGenericRevenueTable.deleteAll() }

    database.addCurrentGenericRevenue(userId, gameId, periodIn, IRON_SOURCE) // not applovin
    database.addCurrentGenericRevenue(userId, videoAdUnitGameAndroid, periodIn, APPLOVIN) // jp video ad
    database.addCurrentGenericRevenue(userId, videoAdUnitGameIos, periodIn, APPLOVIN) // jp video ad
    database.addCurrentGenericRevenue(userId, gameId, periodOutBefore, APPLOVIN) // out of period before
    database.addCurrentGenericRevenue(userId, gameId, periodOutAfter, APPLOVIN) // out of period after

    runBlocking { service.getGameRevenueByPeriod(Instant.now(), Instant.now()) }
      .let { assertThat(it).isEqualByComparingTo(BigDecimal.ZERO) }
  }

  @Test
  fun `SHOULD fetch data ON fetchRetentionCalculationData`() {
    val now = Instant.now()
    val user = database.prepareUser()
    val otherUser = database.prepareUser()
    val game2 = database.prepareGame(applicationId = "com.gimica.coolgame")
    val ancientPast = now.minus(Duration.ofHours(4))

    transaction(database) {
      // old
      UserApplovinRevenueByPeriodsTable.insert {
        it[userId] = user
        it[periodStart] = ancientPast
        it[revenue] = BigDecimal("4.0")
        it[gameId] = game2
        it[revenueTransactionsCount] = 123
      }

      // another user
      UserApplovinRevenueByPeriodsTable.insert {
        it[userId] = otherUser
        it[periodStart] = now.minusSeconds(60)
        it[revenue] = BigDecimal("5.0")
        it[gameId] = game2
        it[revenueTransactionsCount] = 123
      }

      // video ad android
      UserApplovinRevenueByPeriodsTable.insert {
        it[userId] = user
        it[periodStart] = now.minusSeconds(60)
        it[revenue] = BigDecimal("5.5")
        it[gameId] = 1000001
        it[revenueTransactionsCount] = 123
      }

      // video ad ios
      UserApplovinRevenueByPeriodsTable.insert {
        it[userId] = user
        it[periodStart] = now.minusSeconds(60)
        it[revenue] = BigDecimal("5.5")
        it[gameId] = 1000002
        it[revenueTransactionsCount] = 123
      }

      UserApplovinRevenueByPeriodsTable.insert {
        it[userId] = user
        it[periodStart] = now.minusSeconds(60)
        it[revenue] = BigDecimal("6.6")
        it[gameId] = game2
        it[revenueTransactionsCount] = 123
      }
    }

    runBlocking { service.getApplovinInterRevenue(user, now.minus(Duration.ofHours(3))) }.let { actual ->
      assertThat(actual).isEqualTo(BigDecimal("6.600000000000"))
    }
  }

  @Test
  fun `SHOULD return sum revenue by games on getUserPerGameRevenue`() {
    val sinceDate = now.minus(2, ChronoUnit.HOURS)
    val userId = database.prepareUser()
    val userId2 = database.prepareUser()
    val gameId = database.prepareGame()
    val gameId2 = database.prepareGame()

    database.addRevenueByPeriods(userId, gameId, sinceDate.minusSeconds(360))
    database.addRevenueByPeriods(userId, gameId, sinceDate.plusSeconds(60))
    database.addRevenueByPeriods(userId, gameId2, sinceDate.plusSeconds(120))
    database.addRevenueByPeriods(userId, gameId, sinceDate.plus(1, ChronoUnit.HOURS).plusSeconds(15))
    database.addRevenueByPeriods(userId, gameId, sinceDate.plus(1, ChronoUnit.HOURS).plusSeconds(1115))
    database.addRevenueByPeriods(userId, gameId, sinceDate.plus(1, ChronoUnit.HOURS).plusSeconds(2115))
    database.addRevenueByPeriods(userId, gameId, sinceDate.plus(2, ChronoUnit.HOURS))
    database.addRevenueByPeriods(userId2, gameId, sinceDate.plusSeconds(60))
    database.addRevenueByPeriods(userId2, gameId, sinceDate.minusSeconds(60))


    runBlocking {
      service.getUserPerGameRevenue(userId)
    }.let {
      assertThat(it.contains(RevenuePersistenceService.UserGameRevenue(gameId = gameId, revenue = BigDecimal("6.000000000000"))))
      assertThat(it.contains(RevenuePersistenceService.UserGameRevenue(gameId = gameId2, revenue = BigDecimal("1.000000000000"))))
    }
  }

  @Test
  fun `SHOULD return revenue sum for user ON getUserRevenueAfter`() {
    transaction(database) {
      GenericRevenueDailyTotalsMk2Table.deleteAll()
    }

    val userId = database.prepareUser()
    val today = LocalDate.now()
    val yesterday = today.minusDays(1)
    val threeDaysAgo = today.minusDays(3)

    addGenericRevenueDailyTotals(userId, BigDecimal("100.50"), "APPLOVIN", today)
    addGenericRevenueDailyTotals(userId, BigDecimal("50.25"), "TAPJOY", today)
    addGenericRevenueDailyTotals(userId, BigDecimal("200.00"), "APPLOVIN", yesterday)
    addGenericRevenueDailyTotals(userId, BigDecimal("300.00"), "APPLOVIN", threeDaysAgo)

    runBlocking {
      service.getUserRevenueAfter(userId, yesterday)
    }.let { assertThat(it).isEqualByComparingTo(BigDecimal("350.75")) }
  }

  @Test
  fun `SHOULD return zero WHEN no revenue after specified date`() {
    transaction(database) {
      GenericRevenueDailyTotalsMk2Table.deleteAll()
    }

    val userId = database.prepareUser()
    val today = LocalDate.now()
    val yesterday = today.minusDays(1)

    addGenericRevenueDailyTotals(userId, BigDecimal("100.00"), "APPLOVIN", yesterday)

    runBlocking {
      service.getUserRevenueAfter(userId, today)
    }.let { assertThat(it).isEqualByComparingTo(BigDecimal.ZERO) }
  }

  @Test
  fun `SHOULD return zero WHEN user has no revenue`() {
    transaction(database) {
      GenericRevenueDailyTotalsMk2Table.deleteAll()
    }

    val userId = database.prepareUser()

    runBlocking {
      service.getUserRevenueAfter(userId, LocalDate.now())
    }.let { assertThat(it).isEqualByComparingTo(BigDecimal.ZERO) }
  }

  private fun addReportedApplovinRevenue(googleAdId: String, revenueUsd: BigDecimal) =
    transaction(database) {
      ReportedApplovinRevenuesTable.insert {
        it[adUnitId] = UUID.randomUUID().toString()
        it[ReportedApplovinRevenuesTable.googleAdId] = googleAdId
        it[applicationId] = "com.relaxingbraintraining.hexapuzzle"
        it[ReportedApplovinRevenuesTable.revenueUsd] = revenueUsd
        it[day] = LocalDate.now()
      }
    }

  private fun addGenericTotalRevenue(
    userId: String,
    amount: BigDecimal,
    ofwAmount: BigDecimal = BigDecimal.ZERO,
    day2Amount: BigDecimal = BigDecimal.ZERO,
    day0Amount: BigDecimal = BigDecimal.ZERO
  ) =
    transaction(database) {
      GenericRevenueTotalsTable
        .insert {
          it[GenericRevenueTotalsTable.userId] = userId
          it[revenueAmount] = amount
          it[offerwallRevenue] = ofwAmount
          it[day2Revenue] = day2Amount
          it[day0Revenue] = day0Amount
        }
    }

  private fun addCurrentGenericRevenue(userId: String, amount: BigDecimal, source: String = "APPLOVIN", ts: Instant = Instant.now()) {
    transaction(database) {
      CurrentGenericRevenueTable.insert {
        it[id] = UUID.randomUUID().toString()
        it[revenueSource] = source
        it[revenueAmount] = amount
        it[CurrentGenericRevenueTable.userId] = userId
        it[timestamp] = ts
        it[networkId] = -1
      }
    }
  }

  private fun addGenericRevenueDailyTotals(userId: String, amount: BigDecimal, source: String = "APPLOVIN", day: LocalDate = LocalDate.now()) {
    transaction(database) {
      GenericRevenueDailyTotalsMk2Table.insert {
        it[GenericRevenueDailyTotalsMk2Table.userId] = userId
        it[GenericRevenueDailyTotalsMk2Table.day] = day
        it[revenueSource] = source
        it[revenueAmount] = amount
      }
    }
  }

  private fun addApplovinRevByPeriods(
    userId: String,
    gameId: Int,
    periodStart: Instant,
    revenue: BigDecimal,
    revenueTransactionsCount: Int = 0,
  ) = transaction {
    UserApplovinRevenueByPeriodsTable.insert {
      it[UserApplovinRevenueByPeriodsTable.userId] = userId
      it[UserApplovinRevenueByPeriodsTable.gameId] = gameId
      it[UserApplovinRevenueByPeriodsTable.periodStart] = periodStart
      it[UserApplovinRevenueByPeriodsTable.revenue] = revenue
      it[UserApplovinRevenueByPeriodsTable.revenueTransactionsCount] = revenueTransactionsCount
    }
  }
}
