package com.moregames.playtime.revenue.applovin

import com.moregames.base.applovin.dto.ApplovinImpressionEvent
import com.moregames.base.dto.AppPlatform
import com.moregames.base.secret.SecretService
import com.moregames.base.util.Constants.JUSTPLAY_APPLICATION_ID
import com.moregames.base.util.mock
import com.moregames.playtime.games.GamePersistenceService
import com.moregames.playtime.games.GamePersistenceServiceTest.Companion.game
import com.moregames.playtime.revenue.applovin.dto.ApplovinRequest
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset

class DataImportManagerTest {
  private val gamePersistenceService: GamePersistenceService = mock()
  private val applovinRevenuePersistenceService: ApplovinRevenuePersistenceService = mock()
  private val dataFetchingService: DataFetchingService = mock()
  private val secretService: SecretService = mock()

  private val underTest = DataImportManager(
    gamePersistenceService = gamePersistenceService,
    applovinRevenuePersistenceService = applovinRevenuePersistenceService,
    dataFetchingService = dataFetchingService,
    secretService = secretService
  )

  private companion object {
    const val applovinGameApiKey = "applovinGameApiKey"
    const val applovinGimicaApiKey = "applovinGimicaApiKey"
    const val applovinJustPlayApiKey = "applovinJustPlayApiKey"
    val reportDay: LocalDate = LocalDate.now().minusDays(1)

    val event = ApplovinImpressionEvent(
      timestamp = LocalDateTime.parse("2021-04-01T15:59:12").toEpochSecond(ZoneOffset.UTC),
      adUnitId = "af26d13383e2e817",
      adUnitName = "JustPlay rewarded",
      adUnitFormat = "REWARD",
      placement = "",
      countryCode = "us",
      idfa = "fa921cd8-a752-4b50-b904-55759659dee2",
      idfv = "",
      userId = "",
      network = "ADCOLONY_NETWORK",
      revenue = BigDecimal("0.00276"),
      eventId = "2021-04-01T15:59:12fa921cd8-a752-4b50-b9",
      eventToken = "",
      ip = "",
      platform = "",
      packageName = "",
      allRevenue = BigDecimal("0.00276")
    )
  }

  @BeforeEach
  fun before() {
    secretService.mock({ secretValue(ApplovinApiKeys.GAME_API_KEY.secret) }, applovinGameApiKey)
    secretService.mock({ secretValue(ApplovinApiKeys.GIMICA_API_KEY.secret) }, applovinGimicaApiKey)
    secretService.mock({ secretValue(ApplovinApiKeys.JUST_PLAY_API_KEY.secret) }, applovinJustPlayApiKey)
    gamePersistenceService.mock(
      { loadGames(AppPlatform.ANDROID) }, listOf(
        game.copy(applovinApiKey = "game-api-key"),
        game.copy(applovinApiKey = "gimica-api-key"),
        game.copy(applicationId = JUSTPLAY_APPLICATION_ID, applovinApiKey = "just-play-api-key"),
        game.copy(applicationId = "com.gimica.treasuremaster.webgl", applovinApiKey = "gimica-api-key")
      )
    )
    dataFetchingService.mock({ fetchApplovinRevenues(any(), any()) }, emptyList())
  }

  @Test
  fun `SHOULD filter webgl games from applovin requests ON onApplovinRevenuesImportTriggered`() {
    runBlocking {
      underTest.onApplovinRevenuesImportTriggered()
    }

    // webgl game not included
    verifyBlocking(dataFetchingService) {
      fetchApplovinRevenues(ApplovinRequest("applicationId", "applovinGameApiKey"), reportDay)
    }
    verifyBlocking(dataFetchingService) {
      fetchApplovinRevenues(ApplovinRequest("applicationId", "applovinGimicaApiKey"), reportDay)
    }
    verifyBlocking(dataFetchingService) {
      fetchApplovinRevenues(ApplovinRequest("com.justplay.app", "applovinJustPlayApiKey"), reportDay)
    }
  }

}