package com.moregames.playtime.rewarding

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isNull
import com.google.protobuf.Empty
import com.google.protobuf.empty
import com.justplayapps.playtime.rewarding.bonusbank.BonusBankApiGrpc
import com.justplayapps.playtime.rewarding.bonusbank.BonusBankApiGrpcKt
import com.justplayapps.playtime.rewarding.bonusbank.GetBonusCashBarStateResponseKt.BonusCashBarStateKt.bonusCashBarStateMilestone
import com.justplayapps.playtime.rewarding.bonusbank.GetBonusCashBarStateResponseKt.bonusCashBarState
import com.justplayapps.playtime.rewarding.bonusbank.RewardingBonusBank
import com.justplayapps.playtime.rewarding.bonusbank.RewardingBonusBank.GetBonusCashBarStateResponse.BonusCashBarState.BonusCashBarStateMilestone.BonusCashBarStateMilestoneStatus
import com.justplayapps.playtime.rewarding.bonusbank.getBonusCashBarStateResponse
import com.justplayapps.service.rewarding.bonus.proto.UserBonus
import com.justplayapps.service.rewarding.bonus.proto.UserBonusBalanceApiGrpc
import com.justplayapps.service.rewarding.bonus.proto.UserBonusBalanceApiGrpcKt
import com.justplayapps.service.rewarding.bonus.proto.hasBonusForResponse
import com.justplayapps.service.rewarding.earnings.proto.*
import com.moregames.base.dto.AppPlatform
import com.moregames.base.grpc.client.GenericFacade
import com.moregames.base.grpc.testGrpcClient
import com.moregames.base.grpc.withGrpcService
import com.moregames.base.user.RevenueTotals
import com.moregames.base.user.UserBonusBalanceType
import com.moregames.base.util.toProto
import com.moregames.playtime.earnings.dto.UserCurrencyEarnings
import com.moregames.playtime.rewarding.RewardingFacade.Companion.isGameAllowedForEm2Participants
import com.moregames.playtime.user.RevenueByPeriod
import com.moregames.playtime.user.bonuscashbar.dto.BonusCashBar
import com.moregames.playtime.user.bonuscashbar.dto.BonusCashBarMilestone
import com.moregames.playtime.user.bonuscashbar.dto.BonusCashBarMilestoneStatus
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource
import java.math.BigDecimal
import java.time.Instant
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotEquals
import kotlin.test.assertTrue

class RewardingFacadeTest {

  private val now = Instant.now()

  private val stub = RewardingFacade(
    { testGrpcClient { UserBonusBalanceApiGrpc.newStub(it) } },
    { testGrpcClient { EmApiGrpc.newStub(it) } },
    GenericFacade(),
    { testGrpcClient { BonusBankApiGrpc.newStub(it) } },
  )

  @ParameterizedTest
  @ValueSource(strings = ["userId", "userWithBonus"])
  fun addBonusCoins(userId: String) = withGrpcService(mockBonusServer) {
    val added = stub.addBonusCoinsIfNotExists(userId, AppPlatform.ANDROID, 10, UserBonusBalanceType.COIN_GOAL, null)
    when (userId) {
      "userWithBonus" -> assertFalse(added)
      else -> assertTrue(added)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["user1", "userWithoutAmount"])
  fun randomCoinsUsdAmountForEm2(userId: String) = withGrpcService(emApimockServer) {
    val actual = stub.randomCoinsUsdAmountForEm2(userId)
    when (userId) {
      "user1" -> assertThat(actual).isEqualTo(BigDecimal("42"))
      else -> assertThat(actual).isNull()
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["user1", "userWithoutEarnings"])
  fun loadUnpaidUserCurrencyEarnings(userId: String) = withGrpcService(emApimockServer) {
    val actual = stub.loadUnpaidUserCurrencyEarnings(userId)
    when (userId) {
      "user1" -> assertThat(actual).isEqualTo(
        UserCurrencyEarnings(
          userCurrency = Currency.getInstance("CAD"),
          amountUsd = BigDecimal("42"),
          userCurrencyAmount = BigDecimal("142"),
        )
      )

      else -> assertThat(actual).isNull()
    }
  }

  @Test
  fun `SHOULD load unpaid earnings with non boosted amounts ON loadUnpaidUserCurrencyEarnings WHEN non boosted amounts defined`() =
    withGrpcService(emApimockServer) {
      val expected = UserCurrencyEarnings(
        userCurrency = Currency.getInstance("CAD"),
        amountUsd = BigDecimal("42"),
        nonBoostedAmountUsd = BigDecimal("37"),
        userCurrencyAmount = BigDecimal("142"),
        nonBoostedUserCurrencyAmount = BigDecimal("137"),
      )

      val actual = stub.loadUnpaidUserCurrencyEarnings("user-id-with-non-boosted-earnings")

      assertThat(actual).isEqualTo(expected)
    }

  @ParameterizedTest
  @ValueSource(strings = ["user1", "userWithoutEarnings"])
  fun getRevenueTotals(userId: String) = withGrpcService(emApimockServer) {
    val actual = stub.getRevenueTotals(userId)
    when (userId) {
      "user1" -> assertThat(actual).isEqualTo(
        RevenueTotals(
          revenue = BigDecimal("1"),
          offerwallRevenue = BigDecimal.ZERO,
          day0Revenue = BigDecimal("13.757076819420749600"),
          day2Revenue = BigDecimal.ZERO,
        )
      )

      else -> assertThat(actual).isNull()
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["user1", "userWithoutEarnings"])
  fun getLastLowEarningsCashoutPeriodEnd(userId: String) = withGrpcService(emApimockServer) {
    val actual = stub.getLastLowEarningsCashoutPeriodEnd(userId)
    when (userId) {
      "user1" -> assertThat(actual).isEqualTo(now)
      else -> assertThat(actual).isNull()
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["user1", "userWithoutRevenue"])
  fun getUserNonBannerApplovinRevenueTransactionsCountByHours(userId: String) = withGrpcService(emApimockServer) {
    val actual = stub.getUserNonBannerApplovinRevenueTransactionsCountByHours(userId, now)
    when (userId) {
      "user1" -> assertThat(actual).isEqualTo(
        listOf(
          RevenueByPeriod(
            periodStart = now.minusSeconds(10),
            transactionsCount = 10,
          ),
          RevenueByPeriod(
            periodStart = now.minusSeconds(20),
            transactionsCount = 20,
          )
        )
      )

      else -> assertThat(actual.size).isEqualTo(0)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["user1", "userWithoutRevenue"])
  fun get5MinIntervalsWithRevenueByGames(userId: String) = withGrpcService(emApimockServer) {
    val actual = stub.get5MinIntervalsWithRevenueByGames(userId, now)
    when (userId) {
      "user1" -> assertThat(actual).isEqualTo(
        mapOf(
          1 to listOf(now.minusSeconds(10), now.minusSeconds(20)),
          2 to listOf(now.minusSeconds(30), now.minusSeconds(40)),
        )
      )

      else -> assertThat(actual.size).isEqualTo(0)
    }
  }

  @ParameterizedTest()
  @CsvSource(
    "com.gimica.emojiclickers,false",
    "com.gimica.any,true",
    "com.forevergreen.any,true",
    "com.pinmaster.screwpuzzle,true",
    "com.bubblechef.bubbleshooter,true",
    "some.olg.game,false"
  )
  fun `SHOULD return correct result ON isGameAllowedForEm2Participants`(applicationId: String, expected: Boolean) {
    assertEquals(expected, isGameAllowedForEm2Participants(applicationId))
  }

  @Test
  fun `SHOULD return bonus cash bar ON getBonusCashBarState`() = withGrpcService(bonusBankApiMockServer) {
    val expected = BonusCashBar(
      valueToReach = BigDecimal("3.14"),
      currentValue = BigDecimal("2.71"),
      reward = BigDecimal("0.73"),
      readyToClaim = false,
      milestones = listOf(
        BonusCashBarMilestone(
          valueToReach = BigDecimal("13.14"),
          reward = BigDecimal("12.71"),
          status = BonusCashBarMilestoneStatus.READY_TO_CLAIM
        ),
        BonusCashBarMilestone(
          valueToReach = BigDecimal("23.14"),
          reward = BigDecimal("22.71"),
          status = BonusCashBarMilestoneStatus.CLAIMED
        )
      )
    )

    val actual = stub.getBonusCashBarState("userWithBcb", AppPlatform.ANDROID)

    assertThat(actual).isEqualTo(expected)
  }

  @Test
  fun `SHOULD return null ON getBonusCashBarState WHEN NO bcb for user`() = withGrpcService(bonusBankApiMockServer) {
    val actual = stub.getBonusCashBarState("userWithoutBcb", AppPlatform.ANDROID)

    assertThat(actual).isNull()
  }

  private val mockBonusServer = object : UserBonusBalanceApiGrpcKt.UserBonusBalanceApiCoroutineImplBase() {
    override suspend fun hasBonusFor(request: UserBonus.HasBonusForRequest): UserBonus.HasBonusForResponse {
      return when (request.userId) {
        "userWithBonus" -> true
        else -> false
      }.let {
        hasBonusForResponse {
          hasBonus = it
        }
      }
    }

    override suspend fun addBonusCoins(request: UserBonus.AddBonusCoinsRequest): Empty {
      assertNotEquals("userWithBonus", request.userId)
      return Empty.getDefaultInstance()
    }
  }

  private val emApimockServer = object : EmApiGrpcKt.EmApiCoroutineImplBase() {
    override suspend fun getRandomCoinsUsdAmountForEm2(request: EarningModel.GetRandomCoinsUsdAmountForEm2Request): EarningModel.GetRandomCoinsUsdAmountForEm2Response {
      return when (request.userId) {
        "user1" -> getRandomCoinsUsdAmountForEm2Response { this.amount = BigDecimal("42").toProto() }
        else -> EarningModel.GetRandomCoinsUsdAmountForEm2Response.getDefaultInstance()
      }
    }

    override suspend fun loadUnpaidUserCurrencyEarnings(request: EarningModel.LoadUnpaidUserCurrencyEarningsRequest): EarningModel.LoadUnpaidUserCurrencyEarningsResponse {
      return when (request.userId) {
        "user1" -> loadUnpaidUserCurrencyEarningsResponse {
          this.userCurrencyCode = "CAD"
          this.amountUsd = BigDecimal("42").toProto()
          this.userCurrencyAmount = BigDecimal("142").toProto()
        }

        "user-id-with-non-boosted-earnings" -> loadUnpaidUserCurrencyEarningsResponse {
          this.userCurrencyCode = "CAD"
          this.amountUsd = BigDecimal("42").toProto()
          this.nonBoostedAmountUsd = BigDecimal("37").toProto()
          this.userCurrencyAmount = BigDecimal("142").toProto()
          this.nonBoostedUserCurrencyAmount = BigDecimal("137").toProto()
        }

        else -> EarningModel.LoadUnpaidUserCurrencyEarningsResponse.getDefaultInstance()
      }
    }

    override suspend fun getRevenueTotals(request: EarningModel.GetRevenueTotalsRequest): EarningModel.GetRevenueTotalsResponse {
      return when (request.userId) {
        "user1" -> getRevenueTotalsResponse {
          this.revenue = BigDecimal("1").toProto()
          this.day0Revenue = BigDecimal("13.757076819420749600").toProto()
        }

        else -> getRevenueTotalsResponse {}
      }
    }

    override suspend fun getLastLowEarningsCashoutPeriodEnd(request: EarningModel.GetLastLowEarningsCashoutPeriodEndRequest): EarningModel.GetLastLowEarningsCashoutPeriodEndResponse {
      return when (request.userId) {
        "user1" -> getLastLowEarningsCashoutPeriodEndResponse {
          this.periodEnd = now.toProto()
        }

        else -> getLastLowEarningsCashoutPeriodEndResponse {}
      }
    }

    override suspend fun getUserNonBannerApplovinRevenueTransactionsCountByHours(request: EarningModel.GetUserNonBannerApplovinRevenueTransactionsCountByHoursRequest): EarningModel.GetUserNonBannerApplovinRevenueTransactionsCountByHoursResponse {
      return getUserNonBannerApplovinRevenueTransactionsCountByHoursResponse {
        this.revenueByPeriods.add(
          revenueByPeriod {
            this.periodStart = now.minusSeconds(10).toProto()
            this.transactionsCount = 10
          }
        )
        this.revenueByPeriods.add(
          revenueByPeriod {
            this.periodStart = now.minusSeconds(20).toProto()
            this.transactionsCount = 20
          }
        )
        return when (request.userId) {
          "user1" -> getUserNonBannerApplovinRevenueTransactionsCountByHoursResponse {
            this.revenueByPeriods.add(
              revenueByPeriod {
                this.periodStart = now.minusSeconds(10).toProto()
                this.transactionsCount = 10
              }
            )
            this.revenueByPeriods.add(
              revenueByPeriod {
                this.periodStart = now.minusSeconds(20).toProto()
                this.transactionsCount = 20
              }
            )
          }

          else -> getUserNonBannerApplovinRevenueTransactionsCountByHoursResponse {}
        }
      }
    }

    override suspend fun get5MinIntervalsWithRevenueByGames(request: EarningModel.Get5MinIntervalsWithRevenueByGamesRequest): EarningModel.Get5MinIntervalsWithRevenueByGamesResponse {
      return when (request.userId) {
        "user1" -> get5MinIntervalsWithRevenueByGamesResponse {
          this.periods.put(1, intervals { intervals.addAll(listOf(now.minusSeconds(10).toProto(), now.minusSeconds(20).toProto())) })
          this.periods.put(2, intervals { intervals.addAll(listOf(now.minusSeconds(30).toProto(), now.minusSeconds(40).toProto())) })
        }

        else -> get5MinIntervalsWithRevenueByGamesResponse {}
      }
    }
  }

  private val bonusBankApiMockServer = object : BonusBankApiGrpcKt.BonusBankApiCoroutineImplBase() {
    override suspend fun getBonusCashBarState(request: RewardingBonusBank.GetBonusCashBarStateRequest):
      RewardingBonusBank.GetBonusCashBarStateResponse {

      return when (request.userId) {
        "userWithBcb" -> getBonusCashBarStateResponse {
          this.userId = request.userId
          this.enabled = bonusCashBarState {
            this.valueToReach = BigDecimal("3.14").toProto()
            this.currentValue = BigDecimal("2.71").toProto()
            this.reward = BigDecimal("0.73").toProto()
            this.readyToClaim = false
            this.milestone.add(
              bonusCashBarStateMilestone {
                this.valueToReach = BigDecimal("13.14").toProto()
                this.reward = BigDecimal("12.71").toProto()
                this.status = BonusCashBarStateMilestoneStatus.READY_TO_CLAIM
              }
            )
            this.milestone.add(
              bonusCashBarStateMilestone {
                this.valueToReach = BigDecimal("23.14").toProto()
                this.reward = BigDecimal("22.71").toProto()
                this.status = BonusCashBarStateMilestoneStatus.CLAIMED
              }
            )
          }
        }

        else -> getBonusCashBarStateResponse {
          this.userId = request.userId
          this.disabled = empty { }
        }
      }
    }
  }
}