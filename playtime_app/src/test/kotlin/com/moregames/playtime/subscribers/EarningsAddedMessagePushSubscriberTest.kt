package com.moregames.playtime.subscribers

import com.justplayapps.playtime.proto.unpaidUserEarningsAddedEvent
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.ClientExperiment
import com.moregames.base.bus.MessageBus
import com.moregames.base.messaging.dto.EarningsAddedEventDto
import com.moregames.base.util.mock
import com.moregames.playtime.buseffects.CreateThresholdReachedPopupMessageEffect
import com.moregames.playtime.buseffects.PushNotificationEffect
import com.moregames.playtime.earnings.dto.UserCurrencyEarnings
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.ContinueIncompleteCashoutNotification
import com.moregames.playtime.notifications.PushNotification.CrossPlatformPushNotification.EarningsAddedNotification
import com.moregames.playtime.rewarding.toProto
import com.moregames.playtime.user.UserCheckManager
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.CashoutService
import com.moregames.playtime.user.cashout.IncompleteCashoutService
import com.moregames.playtime.utils.EN_LOCALE
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Instant
import java.util.*

class EarningsAddedMessagePushSubscriberTest {
  private val userCheckManager: UserCheckManager = mock()
  private val userService: UserService = mock()
  private val cashoutService: CashoutService = mock()
  private val abTestingService: AbTestingService = mock()
  private val incompleteCashoutService: IncompleteCashoutService = mock()
  private val messageBus: MessageBus = mock()

  private val subscriber = EarningsAddedMessagePushSubscriber(
    userCheckManager = userCheckManager,
    userService = userService,
    cashoutService = cashoutService,
    abTestingService = abTestingService,
    incompleteCashoutService = incompleteCashoutService,
    messageBus = messageBus,
  )

  private companion object {
    const val USER_ID = "userId"
    val earningsEvent = EarningsAddedEventDto(
      metaId = 1,
      userId = USER_ID,
      amount = BigDecimal("2.00"),
      createdAt = Instant.now()
    )
  }

  @BeforeEach
  fun init() {
    userService.mock({ userExists(USER_ID) }, true)
    cashoutService.mock({ userHasCashouts(USER_ID) }, false)
    abTestingService.mock({ isUserExperimentParticipant(USER_ID, ClientExperiment.ANDROID_INCOMPLETE_CASHOUT_RESTORING) }, false)
    incompleteCashoutService.mock({ hasTrackedInitiatedCashouts(USER_ID) }, false)
    userService.mock({ getUser(USER_ID) }, userDtoStub.copy(id = USER_ID, locale = EN_LOCALE))
  }

  @Test
  fun `SHOULD send cashout available message ON processMessage`() = runTest {
    val userCurrencyEarnings = generateUserCurrencyEarnings(amountUsd = "3.40")
    cashoutService.mock(
      { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) },
      userCurrencyEarnings
    )

    subscriber.handle(earningsEvent)

    verifyBlocking(messageBus) {
      publishAsync(
        PushNotificationEffect(
          EarningsAddedNotification(
            userId = USER_ID,
            earnings = userCurrencyEarnings,
            userHasCashouts = false
          )
        )
      )
    }
    verifyBlocking(messageBus, never()) { publishAsync(CreateThresholdReachedPopupMessageEffect(USER_ID, EN_LOCALE)) }
    verifyBlocking(messageBus) {
      publish(unpaidUserEarningsAddedEvent {
        this.userId = USER_ID
        this.earnings = userCurrencyEarnings.toProto()
      })
    }
    verifyBlocking(userCheckManager) { onUserEarningsCalculated(earningsEvent.metaId) }
  }


  @ParameterizedTest
  @ValueSource(booleans = [false, true])
  fun `SHOULD send cashout available message ON processMessage WHEN different existing cashouts`(userHasCashouts: Boolean) = runTest {
    val userCurrencyEarnings = generateUserCurrencyEarnings(amountUsd = "3.40")
    cashoutService.mock(
      { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) },
      userCurrencyEarnings
    )
    cashoutService.mock({ userHasCashouts(USER_ID) }, userHasCashouts)

    subscriber.handle(earningsEvent)

    verifyBlocking(messageBus) {
      publishAsync(
        PushNotificationEffect(
          EarningsAddedNotification(
            userId = USER_ID,
            earnings = userCurrencyEarnings,
            userHasCashouts = userHasCashouts
          )
        )
      )
    }
    verifyBlocking(messageBus, never()) { publishAsync(CreateThresholdReachedPopupMessageEffect(USER_ID, EN_LOCALE)) }
    verifyBlocking(messageBus) {
      publish(unpaidUserEarningsAddedEvent {
        this.userId = USER_ID
        this.earnings = userCurrencyEarnings.toProto()
      })
    }
    verifyBlocking(userCheckManager) { onUserEarningsCalculated(earningsEvent.metaId) }
  }

  @Test
  fun `SHOULD send cashout available message with user currency amount ON processMessage`() = runTest {
    val userCurrencyEarnings = generateUserCurrencyEarnings(amountUsd = "3.409", userCurrency = "CAD", userCurrencyAmount = "4.119")
    cashoutService.mock(
      { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) },
      userCurrencyEarnings
    )

    subscriber.handle(earningsEvent)

    verifyBlocking(messageBus) {
      publishAsync(
        PushNotificationEffect(
          EarningsAddedNotification(
            userId = USER_ID,
            earnings = userCurrencyEarnings,
            userHasCashouts = false
          )
        )
      )
    }
    verifyBlocking(messageBus, never()) { publishAsync(CreateThresholdReachedPopupMessageEffect(USER_ID, EN_LOCALE)) }
  }


  @Test
  fun `SHOULD NOT initiate message ON processMessage WHEN available amount is zero`() = runTest {
    val userCurrencyEarnings = generateUserCurrencyEarnings(amountUsd = "0.00", userCurrency = "USD", userCurrencyAmount = "0.00")
    cashoutService.mock(
      { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) }, userCurrencyEarnings
    )

    subscriber.handle(earningsEvent)

    verifyBlocking(messageBus, never()) { publishAsync(any()) }
    verifyBlocking(userCheckManager) { onUserEarningsCalculated(earningsEvent.metaId) }
  }

  @Test
  fun `SHOULD NOT initiate message ON processMessage WHEN available amount is zero after rounding`() = runTest {
    val userCurrencyEarnings = UserCurrencyEarnings(BigDecimal("0.004"), Currency.getInstance("USD"), BigDecimal("0.00"))
    cashoutService.mock({ getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) }, userCurrencyEarnings)

    subscriber.handle(earningsEvent)

    verifyBlocking(messageBus, never()) { publishAsync(any()) }
    verifyBlocking(userCheckManager) { onUserEarningsCalculated(earningsEvent.metaId) }
  }

  @Test
  fun `SHOULD do nothing ON processMessage WHEN user is not exists`() = runTest {
    userService.mock({ userExists(USER_ID) }, false)

    subscriber.handle(earningsEvent)

    verifyBlocking(userService) { userExists(USER_ID) }
    verifyNoMoreInteractions(userService)

    verifyNoInteractions(userCheckManager)
    verifyNoInteractions(cashoutService)
    verifyNoInteractions(abTestingService)
    verifyNoInteractions(incompleteCashoutService)
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD send continueIncompleteCashout if user is ANDROID_INCOMPLETE_CASHOUT_RESTORING participant AND has initiated cashout ON processMessage`() =
    runTest {
      val userCurrencyEarnings = generateUserCurrencyEarnings(amountUsd = "3.409", userCurrency = "CAD", userCurrencyAmount = "4.119")
      cashoutService.mock(
        { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) },
        userCurrencyEarnings
      )
      abTestingService.mock({ isUserExperimentParticipant(USER_ID, ClientExperiment.ANDROID_INCOMPLETE_CASHOUT_RESTORING) }, true)
      incompleteCashoutService.mock({ hasTrackedInitiatedCashouts(USER_ID) }, true)

      subscriber.handle(earningsEvent)

      verifyBlocking(messageBus) {
        publish(unpaidUserEarningsAddedEvent {
          this.userId = USER_ID
          this.earnings = userCurrencyEarnings.toProto()
        })
      }
      verifyBlocking(messageBus) { publishAsync(PushNotificationEffect(ContinueIncompleteCashoutNotification(USER_ID, earnings = userCurrencyEarnings))) }
      verifyNoMoreInteractions(messageBus)
      verifyBlocking(userCheckManager) { onUserEarningsCalculated(earningsEvent.metaId) }
    }

  @Test
  fun `SHOULD send EarningsAddedNotification WHEN user is ANDROID_INCOMPLETE_CASHOUT_RESTORING participant BUT hasn't initiated cashout ON processMessage`() =
    runTest {
      val userCurrencyEarnings = generateUserCurrencyEarnings(amountUsd = "3.409", userCurrency = "CAD", userCurrencyAmount = "4.119")
      cashoutService.mock(
        { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) },
        userCurrencyEarnings
      )
      abTestingService.mock({ isUserExperimentParticipant(USER_ID, ClientExperiment.ANDROID_INCOMPLETE_CASHOUT_RESTORING) }, true)
      incompleteCashoutService.mock({ hasTrackedInitiatedCashouts(USER_ID) }, false)

      subscriber.handle(earningsEvent)

      verifyBlocking(messageBus) {
        publish(unpaidUserEarningsAddedEvent {
          this.userId = USER_ID
          this.earnings = userCurrencyEarnings.toProto()
        })
      }
      verifyBlocking(messageBus) {
        publishAsync(
          PushNotificationEffect(
            EarningsAddedNotification(
              userId = USER_ID,
              earnings = userCurrencyEarnings,
              userHasCashouts = false
            )
          )
        )
      }
      verifyNoMoreInteractions(messageBus)
      verifyBlocking(userCheckManager) { onUserEarningsCalculated(earningsEvent.metaId) }
    }

  private fun generateUserCurrencyEarnings(
    amountUsd: String = "1.0",
    userCurrency: String = "USD",
    userCurrencyAmount: String = "1.0",
  ) = UserCurrencyEarnings(
    BigDecimal(amountUsd),
    Currency.getInstance(userCurrency),
    BigDecimal(if (userCurrency == "USD") amountUsd else userCurrencyAmount)
  )
}