package com.moregames.playtime.tracking

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isNull
import com.moregames.base.app.BuildVariant
import com.moregames.base.dto.AppPlatform
import com.moregames.base.http.MockedHandler
import com.moregames.base.http.mockUrl
import com.moregames.base.http.mockedHttpClient
import com.moregames.base.secret.SecretService
import com.moregames.base.util.mock
import com.moregames.playtime.app.PlaytimeSecrets
import com.moregames.playtime.app.messaging.dto.EventType
import com.moregames.playtime.app.messaging.dto.MolocoInAppEvent
import com.moregames.playtime.tracking.MolocoApiClient.Companion.API_KEY_HEADER
import com.moregames.playtime.utils.Json.jsonConverterWithoutDefaults
import com.moregames.playtime.utils.withMockedUUID
import io.github.resilience4j.circuitbreaker.CallNotPermittedException
import io.github.resilience4j.circuitbreaker.CircuitBreaker.State
import io.ktor.client.*
import io.ktor.client.engine.mock.*
import io.ktor.client.features.*
import io.ktor.http.*
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.Mockito.mock
import java.math.BigDecimal
import java.time.Instant
import kotlin.test.Test
import kotlin.test.assertFailsWith

class MolocoApiClientTest {
  private val httpClient: HttpClient
  private val jsonConverter = jsonConverterWithoutDefaults

  private val handler: MockedHandler = mock()
  private val secretService: SecretService = mock()

  private val client: MolocoApiClient

  companion object {
    val now = Instant.now()
    val molocoAuthApiKey = "apiKey"
    val molocoAndroidEndpoint = "https://ingestfnt.adsmoloco.com/app-event/v1/apps/com.justplay.app/events"
    val molocoIosEndpoint = "https://ingestfnt.adsmoloco.com/app-event/v1/apps/**********/events"
    val molocoInAppEventStubDroid = MolocoInAppEvent(
      ipAddress = "127.0.0.1",
      userAgent = "ua",
      idfa = "googleAdId",
      idfv = null,
      platform = AppPlatform.ANDROID,
      amount = BigDecimal("2.73"),
      timestamp = 1727186629828,
      type = EventType.SC_WITH_REVENUE
    )
    val molocoInAppEventStubIos = MolocoInAppEvent(
      ipAddress = "127.0.0.1",
      userAgent = "ua",
      idfa = null,
      idfv = "idfv",
      platform = AppPlatform.IOS,
      amount = BigDecimal("2.99"),
      timestamp = 1727186629828,
      type = EventType.SC_WITH_REVENUE
    )
    val molocoInAppEventStubIosWeb = molocoInAppEventStubIos.copy(platform = AppPlatform.IOS_WEB)
  }

  init {
    httpClient = mockedHttpClient(handler, jsonConverter)
    client = MolocoApiClient(
      httpClient = httpClient,
      secretService = secretService,
      buildVariant = BuildVariant.PRODUCTION
    )

    secretService.mock({ secretValue(PlaytimeSecrets.MOLOCO_S2S_AUTH) }, molocoAuthApiKey)
  }

  @BeforeEach
  fun setup() {
    MolocoApiClient.circuitBreaker.reset()
  }

  @Test
  fun `SHOULD send event successfully ON sendInAppEvent WHEN params OK`() {
    handler.mockUrl(molocoAndroidEndpoint) { request, scope ->
      assertThat(request.method).isEqualTo(HttpMethod.Post)
      assertThat(request.body.contentType).isEqualTo(ContentType.Application.Json)
      assertThat(request.headers[API_KEY_HEADER]).isEqualTo(molocoAuthApiKey)
      assertThat(runBlocking {
        request.body.toByteReadPacket().readText()
      }).isEqualTo(
        """{"id":"abcandroid","app":{"id":"com.justplay.app"},"device":{"ip":"127.0.0.1","ua":"ua","os":"ANDROID","ifa":"googleAdId"},"timestamp":1727186629828,"event_name":"sc_with_revenue","event_type":"PURCHASE","event_props":{"revenue":{"currency":"USD","amount":2.73}}}"""
      )
      scope.respondOk()
    }

    runBlocking {
      withMockedUUID("abcandroid") {
        client.sendInAppEvent(
          molocoInAppEventStubDroid
        )
      }
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["IOS", "IOS_WEB"])
  fun `SHOULD send event successfully ON sendInAppEvent WHEN iOS`(platform: AppPlatform) {
    handler.mockUrl(molocoIosEndpoint) { request, scope ->
      assertThat(request.method).isEqualTo(HttpMethod.Post)
      assertThat(request.body.contentType).isEqualTo(ContentType.Application.Json)
      assertThat(request.headers[API_KEY_HEADER]).isEqualTo(molocoAuthApiKey)
      assertThat(runBlocking {
        request.body.toByteReadPacket().readText()
      }).isEqualTo(
        """{"id":"abcios","app":{"id":"**********"},"device":{"ip":"127.0.0.1","ua":"ua","os":"IOS","ifv":"idfv"},"timestamp":1727186629828,"event_name":"sc_with_revenue","event_type":"PURCHASE","event_props":{"revenue":{"currency":"USD","amount":2.99}}}"""
      )
      scope.respondOk()
    }

    runBlocking {
      withMockedUUID("abcios") {
        client.sendInAppEvent(
          molocoInAppEventStubIos.takeIf { platform == AppPlatform.IOS } ?: molocoInAppEventStubIosWeb
        )
      }
    }
  }

  @Test
  fun `SHOULD do nothing ON sendInAppEvent WHEN env is not PRODUCTION`() {
    val testClient = MolocoApiClient(
      httpClient = httpClient,
      secretService = secretService,
      buildVariant = BuildVariant.TEST
    )

    val result = runBlocking {
      testClient.sendInAppEvent(
        molocoInAppEventStubDroid
      )
    }
    assertThat(result).isNull()
  }

  @Nested
  inner class CircuitBreaker {
    @BeforeEach
    fun setUp() {
      MolocoApiClient.circuitBreaker.reset()
    }


    @Test
    fun `SHOULD open circuit breaker WHEN 5 of 10 calls fail`() {
      repeat(5) {
        failingCall()
      }
      repeat(5) {
        successCall()
      }

      handler.mockUrl(molocoIosEndpoint) { request, scope ->
        assertThat(request.method).isEqualTo(HttpMethod.Post)
        assertThat(request.body.contentType).isEqualTo(ContentType.Application.Json)
        assertThat(request.headers[API_KEY_HEADER]).isEqualTo(molocoAuthApiKey)
        assertThat(runBlocking {
          request.body.toByteReadPacket().readText()
        }).isEqualTo(
          """{"id":"abcios","app":{"id":"**********"},"device":{"ip":"127.0.0.1","ua":"ua","os":"IOS","ifv":"idfv"},"timestamp":1727186629828,"event_name":"sc_with_revenue","event_type":"PURCHASE","event_props":{"revenue":{"currency":"USD","amount":2.99}}}"""
        )
        scope.respondOk()
      }

      assertFailsWith<CallNotPermittedException> {
        runBlocking {
          withMockedUUID("abcios") {
            client.sendInAppEvent(
              molocoInAppEventStubIos
            )
          }
        }
      }

      assertThat(MolocoApiClient.circuitBreaker.state).isEqualTo(State.OPEN)
    }

    @Test
    fun `SHOULD recover circuit breaker WHEN started with half open state and 5 calls succeed`() {
      MolocoApiClient.circuitBreaker.transitionToOpenState()
      MolocoApiClient.circuitBreaker.transitionToHalfOpenState()
      repeat(5) {
        successCall()
      }

      handler.mockUrl(molocoAndroidEndpoint) { request, scope ->
        assertThat(request.method).isEqualTo(HttpMethod.Post)
        assertThat(request.body.contentType).isEqualTo(ContentType.Application.Json)
        assertThat(request.headers[API_KEY_HEADER]).isEqualTo(molocoAuthApiKey)
        scope.respondOk()
      }

      runBlocking {
        client.sendInAppEvent(
          molocoInAppEventStubDroid
        )
      }

      assertThat(MolocoApiClient.circuitBreaker.state).isEqualTo(State.CLOSED)
    }

    @Test
    fun `SHOULD leave circuit breaker in open state WHEN started with half open state and one of calls failed`() {
      MolocoApiClient.circuitBreaker.transitionToOpenState()
      MolocoApiClient.circuitBreaker.transitionToHalfOpenState()
      repeat(3) {
        failingCall()
      }
      repeat(2) {
        successCall()
      }

      handler.mockUrl(molocoIosEndpoint) { request, scope ->
        assertThat(request.method).isEqualTo(HttpMethod.Post)
        assertThat(request.body.contentType).isEqualTo(ContentType.Application.Json)
        assertThat(request.headers[API_KEY_HEADER]).isEqualTo(molocoAuthApiKey)
        assertThat(runBlocking {
          request.body.toByteReadPacket().readText()
        }).isEqualTo(
          """{"id":"abcios","app":{"id":"**********"},"device":{"ip":"127.0.0.1","ua":"ua","os":"IOS","ifv":"idfv"},"timestamp":1727186629828,"event_name":"sc_with_revenue","event_type":"PURCHASE","event_props":{"revenue":{"currency":"USD","amount":2.99}}}"""
        )
        scope.respondOk()
      }

      assertFailsWith<CallNotPermittedException> {
        runBlocking {
          withMockedUUID("abcios") {
            client.sendInAppEvent(
              molocoInAppEventStubIos
            )
          }
        }
      }

      assertThat(MolocoApiClient.circuitBreaker.state).isEqualTo(State.OPEN)
    }


    private fun successCall() {
      handler.mockUrl(molocoAndroidEndpoint) { request, scope ->
        assertThat(request.method).isEqualTo(HttpMethod.Post)
        assertThat(request.body.contentType).isEqualTo(ContentType.Application.Json)
        assertThat(request.headers[API_KEY_HEADER]).isEqualTo(molocoAuthApiKey)
        scope.respondOk()
      }

      val actual = runBlocking {
        client.sendInAppEvent(
          molocoInAppEventStubDroid
        )
      }

      assertThat(actual!!.status).isEqualTo(HttpStatusCode.OK)
    }

    private fun failingCall() {
      handler.mockUrl(molocoAndroidEndpoint) { request, scope ->
        assertThat(request.method).isEqualTo(HttpMethod.Post)
        assertThat(request.body.contentType).isEqualTo(ContentType.Application.Json)
        assertThat(request.headers[API_KEY_HEADER]).isEqualTo(molocoAuthApiKey)
        scope.respond(
          content = """{"code":7, "message":"forbidden"}""",
          status = HttpStatusCode.Forbidden,
          headers = headersOf("Content-Type", ContentType.Application.Json.toString())
        )
      }

      assertThrows<ClientRequestException> {
        runBlocking {
          client.sendInAppEvent(
            molocoInAppEventStubDroid
          )
        }
      }

    }
  }
}