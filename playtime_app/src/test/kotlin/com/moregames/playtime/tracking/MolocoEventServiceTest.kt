package com.moregames.playtime.tracking

import com.moregames.base.bus.MessageBus
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.app.messaging.dto.EventType
import com.moregames.playtime.app.messaging.dto.MolocoInAppEvent
import com.moregames.playtime.buseffects.SendMolocoInAppEventEffect
import com.moregames.playtime.user.UserPersistenceService
import com.moregames.playtime.user.dto.UserExternalIds
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoInteractions
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit

class MolocoEventServiceTest {
  private val timeService: TimeService = mock()
  private val userPersistenceService: UserPersistenceService = mock()
  private val adjustService: AdjustService = mock()
  private val messageBus: MessageBus = mock()

  val now: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)
  private val underTest = MolocoEventService(timeService, userPersistenceService, adjustService, messageBus)

  companion object {
    const val USER_ID = "userId"
    const val USER_IP = "***********"
    const val USER_AGENT = "Dalvik/2.1.0 (Linux; U; Android 11; U696CL Build/UMX_U696CL_V11.01.01.07)"
  }

  @BeforeEach
  fun setUp() {
    timeService.mock({ now() }, now)
    adjustService.mock({ getUserIp(USER_ID) }, USER_IP)
    adjustService.mock({ getUserAgent(USER_ID) }, USER_AGENT)
  }

  @Test
  fun `SHOULD send event to moloco`() {
    userPersistenceService.mock(
      { fetchExternalIds(USER_ID) },
      UserExternalIds(USER_ID, "googleAdId", "idfa", "adjustId", "firebaseAppId", userDtoStub.trackingData)
    )

    runBlocking {
      underTest.sendEvent(USER_ID, BigDecimal("0.5"), EventType.OW_WITH_REVENUE)
    }

    verifyBlocking(messageBus) {
      publishAsync(
        effect = SendMolocoInAppEventEffect(
          event =
            MolocoInAppEvent(
              ipAddress = "***********",
              userAgent = "Dalvik/2.1.0 (Linux; U; Android 11; U696CL Build/UMX_U696CL_V11.01.01.07)",
              idfa = "b19860d2-ccf9-472b-a0f6-c2e517bd841a",
              idfv = null,
              platform = userDtoStub.appPlatform,
              amount = BigDecimal("0.5"),
              timestamp = now.toEpochMilli(),
              type = EventType.OW_WITH_REVENUE
            )
        )
      )
    }
  }

  @Test
  fun `SHOULD not send event to moloco`() {
    userPersistenceService.mock(
      { fetchExternalIds(USER_ID) },
      UserExternalIds(USER_ID, "googleAdId", "idfa", "adjustId", "firebaseAppId", null)
    )

    runBlocking {
      underTest.sendEvent(USER_ID, BigDecimal("0.5"), EventType.OW_WITH_REVENUE)
    }

    verifyNoInteractions(messageBus)
  }
}