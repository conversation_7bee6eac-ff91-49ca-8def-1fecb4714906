package com.moregames.playtime.tracking

import com.moregames.base.dto.AppPlatform
import com.moregames.base.junit.MockExtension
import com.moregames.base.util.ApplicationId
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.user.UserService
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.mockito.kotlin.eq
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoInteractions
import java.time.Instant

@ExtendWith(MockExtension::class)
class ProgressEventsServiceTest(
  private val adMarketService: AdMarketService,
  private val userService: UserService,
  private val timeService: TimeService,
) {

  private val underTest = ProgressEventsService(
    adMarketService = adMarketService,
    userService = userService,
    timeService = timeService,
  )

  @BeforeEach
  fun setUp() {
    userService.mock({ getUser(USER_ID, includingDeleted = true) }, userDtoStub.copy(createdAt = now, appPlatform = AppPlatform.ANDROID))
    timeService.mock( { now()}, now)
  }

  @ParameterizedTest
  @CsvSource(
    "0, ${ApplicationId.TREASURE_MASTER_APP_ID}",
    "3, ${ApplicationId.TREASURE_MASTER_APP_ID}",
    "5, ${ApplicationId.SOLITAIRE_VERSE_APP_ID}",
    "10, ${ApplicationId.WATER_SORTER_APP_ID}",
    "15, ${ApplicationId.SPACE_CONNECT_APP_ID}",
    "20, ${ApplicationId.MAD_SMASH_APP_ID}",
    "25, ${ApplicationId.BLOCKBUSTER_APP_ID}",
    "30, ${ApplicationId.PUZZLE_POP_BLASTER_APP_ID}",
    "35, ${ApplicationId.WORD_SEEKER_APP_ID}",
    "40, ${ApplicationId.TANGRAM_APP_ID}",
    "45, ${ApplicationId.TILE_MATCH_PRO_APP_ID}",
    "50, untracked",
  )
  fun `SHOULD create and send market events`(progress: Int, applicationId: String) {
    runBlocking { underTest.createAndSendProgressEvents(userId = USER_ID, applicationId = applicationId, progress = progress) }

    val triggeredEvents = when {
      applicationId == ApplicationId.TREASURE_MASTER_APP_ID && progress == 0 -> emptyList()
      applicationId == ApplicationId.TREASURE_MASTER_APP_ID && progress == 3 -> listOf("action_beat_3_stages_treasure_master")
      applicationId == ApplicationId.SOLITAIRE_VERSE_APP_ID && progress == 5 -> listOf("action_win_3_games_solitaire_verse", "action_win_5_games_solitaire_verse")
      applicationId == ApplicationId.WATER_SORTER_APP_ID && progress == 10 -> listOf("action_complete_3_lvls_water_sorter", "action_complete_5_lvls_water_sorter", "action_complete_10_lvls_water_sorter")
      applicationId == ApplicationId.SPACE_CONNECT_APP_ID && progress == 15 -> listOf("action_complete_3_lvls_space_connect", "action_complete_5_lvls_space_connect", "action_complete_10_lvls_space_connect", "action_complete_15_lvls_space_connect")
      applicationId == ApplicationId.MAD_SMASH_APP_ID && progress == 20 -> listOf("action_complete_3_lvls_mad_smash", "action_complete_5_lvls_mad_smash", "action_complete_10_lvls_mad_smash", "action_complete_15_lvls_mad_smash", "action_complete_20_lvls_mad_smash")
      applicationId == ApplicationId.BLOCKBUSTER_APP_ID && progress == 25 -> listOf("action_win_3_games_in_block_buster", "action_win_5_games_in_block_buster", "action_win_10_games_in_block_buster", "action_win_15_games_in_block_buster", "action_win_20_games_in_block_buster")
      applicationId == ApplicationId.PUZZLE_POP_BLASTER_APP_ID && progress == 30 -> listOf("action_complete_3_lvls_puzzle_pop", "action_complete_5_lvls_puzzle_pop", "action_complete_10_lvls_puzzle_pop", "action_complete_15_lvls_puzzle_pop", "action_complete_20_lvls_puzzle_pop")
      applicationId == ApplicationId.WORD_SEEKER_APP_ID && progress == 35 -> listOf("action_complete_3_lvls_word_seeker", "action_complete_5_lvls_word_seeker", "action_complete_10_lvls_word_seeker", "action_complete_15_lvls_word_seeker", "action_complete_20_lvls_word_seeker")
      applicationId == ApplicationId.TANGRAM_APP_ID && progress == 40 -> listOf("action_complete_3_lvls_tangram_heaven", "action_complete_5_lvls_tangram_heaven", "action_complete_10_lvls_tangram_heaven", "action_complete_15_lvls_tangram_heaven", "action_complete_20_lvls_tangram_heaven")
      applicationId == ApplicationId.TILE_MATCH_PRO_APP_ID && progress == 45 -> listOf("action_complete_3_lvls_title_match", "action_complete_5_lvls_title_match", "action_complete_10_lvls_title_match", "action_complete_15_lvls_title_match", "action_complete_20_lvls_title_match")
      applicationId == "untracked" && progress == 50 -> emptyList()
      else -> emptyList()
    }

    if (triggeredEvents.isEmpty()) {
      verifyNoInteractions(adMarketService)
    } else {
      verifyBlocking(adMarketService) { sendMarketEvents(eq(USER_ID), eq(triggeredEvents)) }
    }
  }

  private companion object {
    const val USER_ID = "userId"
    val now: Instant = Instant.parse("2025-01-22T09:58:00.000Z")
  }
}