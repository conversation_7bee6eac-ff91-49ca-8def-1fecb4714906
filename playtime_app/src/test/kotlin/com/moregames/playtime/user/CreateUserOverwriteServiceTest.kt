package com.moregames.playtime.user

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.google.inject.Provider
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.dto.Experiment
import com.moregames.base.abtesting.dto.Variation
import com.moregames.base.app.BuildVariant
import com.moregames.base.app.OfferWallType.ADJOE
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.MessageBus
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.base.dto.AppVersionDto
import com.moregames.base.messaging.dto.GameProgressBqEventDto
import com.moregames.base.messaging.dto.RevenueReceivedEventDto
import com.moregames.base.user.dto.*
import com.moregames.base.util.ClientVersionsSupport.getUserCreationMinAppVersion
import com.moregames.base.util.RandomGenerator
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.administration.qa.QaUserSetting.*
import com.moregames.playtime.administration.qa.QaUserSettingsService
import com.moregames.playtime.checks.ExaminationService
import com.moregames.playtime.games.GamePersistenceService
import com.moregames.playtime.user.attestation.UserAttestationPersistenceService
import com.moregames.playtime.user.fraudscore.FraudScorePersistenceService
import com.moregames.playtime.user.offer.AndroidOfferwallPersistenceService
import com.moregames.playtime.user.verification.VerificationPersistenceService
import com.moregames.playtime.user.verification.dto.VerificationSessionDto
import com.moregames.playtime.utils.androidGameOfferStub
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoMoreInteractions
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*

@ExperimentalCoroutinesApi
class CreateUserOverwriteServiceTest {
  private val buildVariantProvider: Provider<BuildVariant> =
    mock { on { get() } doReturn (BuildVariant.TEST) }
  private val androidOfferwallPersistenceService: AndroidOfferwallPersistenceService = mock()
  private val verificationPersistenceService: VerificationPersistenceService = mock()
  private val timeService: TimeService = mock()
  private val fraudScorePersistenceService: FraudScorePersistenceService = mock()
  private val randomUUIDGenerator: RandomGenerator = mock()
  private val gamePersistenceService: GamePersistenceService = mock()
  private val examinationService: ExaminationService = mock()
  private val abTestingService: AbTestingService = mock()
  private val qaUserSettingsService: QaUserSettingsService = mock()
  private val userAttestationPersistenceService: UserAttestationPersistenceService = mock()
  private val messageBus: MessageBus = mock()
  private val mocks = arrayOf(
    androidOfferwallPersistenceService, verificationPersistenceService,
    fraudScorePersistenceService, messageBus, gamePersistenceService,
    examinationService, abTestingService, userAttestationPersistenceService,
  )
  private val applicationConfig: ApplicationConfig = mock {
    on { justplayMarket } doReturn "test-market"
  }

  private val bigQueryEventPublisher: BigQueryEventPublisher = mock()

  private val service = CreateUserOverwriteService(
    buildVariantProvider = buildVariantProvider,
    androidOfferwallPersistenceService = androidOfferwallPersistenceService,
    verificationPersistenceService = verificationPersistenceService,
    timeService = timeService,
    fraudScorePersistenceService = fraudScorePersistenceService,
    messageBus = messageBus,
    bigQueryEventPublisher = bigQueryEventPublisher,
    randomUUIDGenerator = randomUUIDGenerator,
    gamePersistenceService = gamePersistenceService,
    examinationService = examinationService,
    abTestingService = abTestingService,
    qaUserSettingsService = qaUserSettingsService,
    applicationConfig = applicationConfig,
    userAttestationPersistenceService = userAttestationPersistenceService
  )

  companion object {
    const val USER_ID = "userId"
    const val COUNTRY_CODE = "US"
    const val IP = "***********"
    val now = Instant.now()!!
    val uuid = UUID.randomUUID().toString()
    val experiments = setOf(
      ExperimentVariationKey("exp1", "key1"),
      ExperimentVariationKey("exp1", "key2"),
      ExperimentVariationKey("exp2", "key1"),
    )
  }


  @BeforeEach
  fun before() {
    timeService.mock({ now() }, now)
    gamePersistenceService.mock({ loadVisibleGames("en", ANDROID) }, listOf(androidGameOfferStub))
    randomUUIDGenerator.mock({ nextUUID() }, uuid)
    Dispatchers.setMain(StandardTestDispatcher())
  }

  @Test
  fun `SHOULD throw exception ON redefineCreateUserData WHEN in Production`() = runTest {
    buildVariantProvider.mock({ get() }, BuildVariant.PRODUCTION)

    assertThrows<IllegalStateException> {
      service.redefineCreateUserData(buildCreateUserData())
    }.also { assertThat(it.message).isEqualTo("Not allowed in production mode") }
  }

  @Test
  fun `SHOULD throw exception ON postUserCreatedActions WHEN in Production`() = runTest {
    buildVariantProvider.mock({ get() }, BuildVariant.PRODUCTION)

    assertThrows<IllegalStateException> {
      runBlocking { service.postUserCreatedActions(buildCreateUserData(), USER_ID) }
    }.also { assertThat(it.message).isEqualTo("Not allowed in production mode") }
  }

  @Test
  fun `SHOULD throw exception ON afterGettingUserIdActions WHEN in Production`() = runTest {
    buildVariantProvider.mock({ get() }, BuildVariant.PRODUCTION)

    assertThrows<IllegalStateException> {
      runBlocking { service.afterGettingUserIdActions(UserDefinitionRules(), USER_ID) }
    }.also { assertThat(it.message).isEqualTo("Not allowed in production mode") }
  }

  @Test
  fun `SHOULD not change user data ON redefineCreateUserData WHEN no overwrite rules`() = runTest {
    val createUserData = buildCreateUserData()
    val expected = createUserData.copy()

    service.redefineCreateUserData(createUserData)
      .also { assertThat(it).isEqualTo(expected) }
  }

  @ParameterizedTest
  @CsvSource(
    "true,true,true,true,true",
    "true,false,false,false,false",
    "false,true,false,false,false",
    "false,false,true,false,false",
    "false,false,false,true,false",
    "false,false,false,false,true",
    "false,false,false,false,false",
  )
  fun `SHOULD change user data ON redefineCreateUserData WHEN overwrite rules defined`(
    changeCountryCode: Boolean,
    changeIp: Boolean,
    changeSimCountry: Boolean,
    changeNetworkCountry: Boolean,
    changeDeviceLocale: Boolean,
  ) = runTest {
    val createUserData = buildCreateUserData(
      UserDefinitionRules(
        countryCode = if (changeCountryCode) "BG" else null,
        ip = if (changeIp) "*******" else null,
        simCountry = if (changeSimCountry) "IE" else null,
        networkCountry = if (changeNetworkCountry) "PL" else null,
        deviceLocale = if (changeDeviceLocale) "DE" else null,
      )
    )
    val metadata = createUserData.userRequestMetadata
    val requestDto = createUserData.userRequestDto!!

    val expected = createUserData.copy(
      userRequestMetadata = metadata.copy(
        countryCode = if (changeCountryCode) "BG" else metadata.countryCode,
        ip = if (changeIp) "*******" else metadata.ip,
        forwardedIp = if (changeIp) "*******" else metadata.forwardedIp,
      ),
      userRequestDto = requestDto.copy(
        simCountry = if (changeSimCountry) "IE" else requestDto.simCountry,
        networkCountry = if (changeNetworkCountry) "PL" else requestDto.networkCountry,
        deviceLocale = if (changeDeviceLocale) "DE" else requestDto.deviceLocale,
        simInfoList = requestDto.simInfoList.takeIf { !changeSimCountry && !changeNetworkCountry }
      )
    )

    service.redefineCreateUserData(createUserData)
      .also { assertThat(it).isEqualTo(expected) }
  }

  @Test
  fun `SHOULD mark user as passed device attestation ON postUserCreatedActions WHEN deviceAttestationPassed rule is set`() = runTest {

    val createUserData = buildCreateUserData(
      UserDefinitionRules(
        deviceAttestationPassed = true
      )
    )

    service.postUserCreatedActions(createUserData, USER_ID)

    verifyBlocking(examinationService) {
      markUserAsPassedDeviceAttestation(USER_ID)
    }

    verifyNoMoreInteractions(*mocks)
  }

  @Test
  fun `SHOULD mark user as passed device attestation AND passed strong attestation ON postUserCreatedActions WHEN strongAttestationPassed rule is set`() =
    runTest {

      val createUserData = buildCreateUserData(
        UserDefinitionRules(
          strongAttestationPassed = true
        )
      )

      service.postUserCreatedActions(createUserData, USER_ID)

      verifyBlocking(examinationService) {
        markUserAsPassedDeviceAttestation(USER_ID)
      }
      verifyBlocking(userAttestationPersistenceService) { saveUserPassedStrongAttestation(USER_ID) }
      verifyNoMoreInteractions(*mocks)
    }

  @Test
  fun `SHOULD mark user as passed facetec check ON postUserCreatedActions WHEN facetecVerificationSessionId rule is set`() = runTest {
    val facetecSessionId = "appDefinedFacetecSessionId"

    val createUserData = buildCreateUserData(
      UserDefinitionRules(
        facetecVerificationSessionId = facetecSessionId
      )
    )

    service.postUserCreatedActions(createUserData, USER_ID)

    verifyBlocking(verificationPersistenceService) {
      createSession(
        VerificationSessionDto(
          sessionId = facetecSessionId,
          userId = USER_ID,
          expiredAt = now.plus(60, ChronoUnit.MINUTES),
          verification = listOf(
            VerificationSessionDto.VerificationStep(
              type = VerificationSessionDto.VerificationType.FACE,
              status = VerificationSessionDto.VerificationStatus.VERIFIED,
              order = 1
            )
          )
        )
      )
    }
    verifyNoMoreInteractions(*mocks)
  }

  @Test
  fun `SHOULD set specific frozen FS to user ON postUserCreatedActions WHEN freezeFraudScore rule is set`() = runTest {
    val fsValue = -747

    val createUserData = buildCreateUserData(
      UserDefinitionRules(
        frozenFraudScore = fsValue
      )
    )

    service.postUserCreatedActions(createUserData, USER_ID)

    verifyBlocking(fraudScorePersistenceService) { setFrozenFraudScore(USER_ID, (fsValue).toDouble()) }
    verifyNoMoreInteractions(*mocks)
  }

  @Test
  fun `SHOULD add coins to user balance ON postUserCreatedActions WHEN coinsToAdd rule is set`() = runTest {
    val coinsValue = 99

    val createUserData = buildCreateUserData(
      UserDefinitionRules(
        coinsToAdd = coinsValue
      )
    )

    service.postUserCreatedActions(createUserData, USER_ID)

    verifyBlocking(bigQueryEventPublisher) {
      publish(
        GameProgressBqEventDto(
          eventId = uuid,
          userId = USER_ID,
          gameId = androidGameOfferStub.id,
          coins = coinsValue,
          method = "REPLACE",
          applicationIdParam = androidGameOfferStub.applicationId,
          market = "test-market",
          platform = createUserData.appVersion.platform.name,
          createdAt = now,
        )
      )
    }
    verifyBlocking(gamePersistenceService) { loadVisibleGames("en", ANDROID) }
    verifyNoMoreInteractions(*mocks)
  }

  @Test
  fun `SHOULD add revenue to user balance ON postUserCreatedActions WHEN revenueToAdd rule is set`() = runTest {

    val createUserData = buildCreateUserData(
      UserDefinitionRules(
        revenueToAdd = BigDecimal.ONE
      )
    )

    service.postUserCreatedActions(createUserData, USER_ID)

    verifyBlocking(messageBus) {
      publish(
        RevenueReceivedEventDto(
          eventId = uuid,
          userId = USER_ID,
          timestamp = now,
          source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
          amount = BigDecimal.ONE,
          networkId = -1,
          gameId = null,
          createdAt = now
        )
      )
    }
    verifyNoMoreInteractions(*mocks)
  }

  @Test
  fun `SHOULD set up offerwall type ON postUserCreatedActions WHEN explicit offerwall type rule is set`() = runTest {
    androidOfferwallPersistenceService.mock({ trackOfferwallTypesReturnResult(USER_ID, listOf(ADJOE)) }, true)

    val createUserData = buildCreateUserData(
      UserDefinitionRules(
        offerWallType = ADJOE
      )
    )

    service.postUserCreatedActions(createUserData, USER_ID)

    verifyBlocking(androidOfferwallPersistenceService) { trackOfferwallTypesReturnResult(USER_ID, listOf(ADJOE)) }
    verifyNoMoreInteractions(*mocks)
  }

  @Test
  fun `SHOULD throw exception ON applyExperiments WHEN in production`() = runTest {
    buildVariantProvider.mock({ get() }, BuildVariant.PRODUCTION)

    assertThrows<IllegalStateException> {
      runBlocking {
        service.applyExperiments(USER_ID, experiments)
      }
    }
      .also { assertThat(it.message).isEqualTo("Not allowed in production mode") }
  }

  @ParameterizedTest
  @ValueSource(strings = ["null", "empty"])
  fun `SHOULD do nothing ON applyExperiments WHEN no data`(option: String) = runTest {
    val emptyExperiments = when (option) {
      "null" -> null
      "empty" -> emptySet<ExperimentVariationKey>()
      else -> throw IllegalStateException()
    }

    service.applyExperiments(USER_ID, emptyExperiments)

    verifyNoInteractions(abTestingService)
  }

  @Test
  fun `SHOULD throw ON applyExperiments WHEN unknown experiments passed`() = runTest {
    val variation = buildSimpleVariation("exp2", "key1")

    val expectedUnknown = setOf(
      ExperimentVariationKey("exp1", "key1"),
      ExperimentVariationKey("exp1", "key2"),
    )

    abTestingService.mock({ loadAllExpVariations() }, listOf(variation))

    assertThrows<IllegalStateException> {
      runBlocking { service.applyExperiments(USER_ID, experiments) }
    }
      .also {
        assertThat(it.message).isEqualTo("Variation(s) unknown or client-dependant for $USER_ID: $expectedUnknown")
      }

    verifyBlocking(abTestingService) { loadAllExpVariations() }
  }

  @Test
  fun `SHOULD create participants ON applyExperiments`() = runTest {
    val variation1 = buildSimpleVariation("exp1", "key1")
    val variation2 = buildSimpleVariation("exp1", "key2")
    val variation3 = buildSimpleVariation("exp2", "key1")
    val knownVariations = listOf(variation1, variation2, variation3)

    abTestingService.mock({ loadAllExpVariations() }, knownVariations)

    service.applyExperiments(USER_ID, experiments)

    verifyBlocking(abTestingService) { loadAllExpVariations() }
    verifyBlocking(abTestingService) { createOrUpdateParticipant(USER_ID, variation1, activated = false) }
    verifyBlocking(abTestingService) { createOrUpdateParticipant(USER_ID, variation2, activated = false) }
    verifyBlocking(abTestingService) { createOrUpdateParticipant(USER_ID, variation3, activated = false) }
  }

  @Test
  fun `SHOULD save user settings ON afterGettingUserIdActions`() = runTest {

    service.afterGettingUserIdActions(
      UserDefinitionRules(
        skipOnboarding = true,
        speedUpCashoutPeriodEnd = true,
        restrictBan = true,
        useUsualCashoutPeriod = true
      ),
      USER_ID
    )


    verifyBlocking(qaUserSettingsService) {
      setUserSettings(USER_ID, listOf(SKIP_ONBOARDING, SPEED_UP_CASHOUT_PERIOD_END, RESTRICT_BAN, USE_USUAL_CASHOUT_PERIODS))
    }
  }

  @Test
  fun `SHOULD not save user setting ON afterGettingUserIdActions WHEN false was provided`() = runTest {
    service.afterGettingUserIdActions(UserDefinitionRules(skipOnboarding = true, speedUpCashoutPeriodEnd = false), USER_ID)

    verifyBlocking(qaUserSettingsService) {
      setUserSettings(USER_ID, listOf(SKIP_ONBOARDING))
    }
  }

  @Test
  fun `SHOULD not save user setting ON afterGettingUserIdActions WHEN null was provided`() = runTest {

    service.afterGettingUserIdActions(UserDefinitionRules(skipOnboarding = true, speedUpCashoutPeriodEnd = null), USER_ID)

    verifyBlocking(qaUserSettingsService) {
      setUserSettings(USER_ID, listOf(SKIP_ONBOARDING))
    }
  }

  @Test
  fun `SHOULD trigger saving user settings with an empty list ON afterGettingUserIdActions WHEN no settings were provided`() = runTest {

    service.afterGettingUserIdActions(UserDefinitionRules(), USER_ID)

    verifyBlocking(qaUserSettingsService) {
      setUserSettings(USER_ID, listOf())
    }
  }

  private fun buildSimpleVariation(experimentKey: String, variationKey: String): Variation =
    Variation(
      key = variationKey, id = 0, allocation = BigDecimal.ZERO,
      experiment = Experiment(
        key = experimentKey,
        id = 0, isActive = false,
        minimumAppVersion = null, startedAt = now.minusSeconds(1000), finishedAt = null, gameVersion = null,
      )
    )

  private fun buildCreateUserData(userDefinitionRules: UserDefinitionRules? = null) =
    CreateUserData(
      appVersion = AppVersionDto(ANDROID, getUserCreationMinAppVersion(ANDROID)),
      userRequestMetadata = UserRequestMetadata(
        ip = IP,
        forwardedIp = null,
        countryCode = COUNTRY_CODE,
        loadBalancerCountryData = COUNTRY_CODE,
        forwardedIpRaw = IP,
        region = "region",
        city = "city",
      ),
      userRequestDto = CreateUserRequestDto(
        networkCountry = "JP",
        networkOperatorName = "verizon",
        simCountry = "CN",
        simOperatorName = "Verizon Japan",
        deviceLocale = "ko",
        deviceLanguageTag = "ko-KR",
        installedFromStore = true,
        userDefinitionRules = userDefinitionRules,
        simInfoList = listOf(SimInfo("jp", "net1", "jp", "net2", 0))
      ),
      userRequestDtoSignature = null,
      definedExperimentVariations = null,
    )
}
