package com.moregames.playtime.user

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.justplayapps.service.rewarding.earnings.UserEarnings
import com.moregames.base.app.PaymentProviderType
import com.moregames.base.dto.AppPlatform
import com.moregames.base.junit.MockExtension
import com.moregames.base.user.RevenueTotals
import com.moregames.base.util.*
import com.moregames.playtime.app.messaging.dto.EventType
import com.moregames.playtime.games.GamePersistenceService
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.general.TrackedEventsPersistenceService
import com.moregames.playtime.general.dto.TrackedEvent
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.tracking.AdMarketService
import com.moregames.playtime.tracking.MolocoEventService
import com.moregames.playtime.user.UserCheckManager.Companion.MAX_DUPLICATE_CASHOUT_EMAIL_COUNT
import com.moregames.playtime.user.UserCheckManager.Companion.totalCashoutLimit
import com.moregames.playtime.user.attestation.UserAttestationPersistenceService
import com.moregames.playtime.user.cashout.CashoutPeriodCounters
import com.moregames.playtime.user.cashout.CashoutPeriodsPersistenceService
import com.moregames.playtime.user.cashout.CashoutPersistenceService
import com.moregames.playtime.user.cashout.dto.CashoutDemand
import com.moregames.playtime.user.fraudscore.FraudScoreService
import com.moregames.playtime.utils.cashoutTransactionStub
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit

@ExperimentalCoroutinesApi
@ExtendWith(MockExtension::class)
class UserCheckManagerTest(
  private val userService: UserService,
  private val userSuspicionService: UserSuspicionService,
  private val rewardingFacade: RewardingFacade,
  private val cashoutPersistenceService: CashoutPersistenceService,
  private val fraudScoreService: FraudScoreService,
  private val timeService: TimeService,
  private val trackedEventsPersistenceService: TrackedEventsPersistenceService,
  private val testScope: TestScope,
  private val adMarketService: AdMarketService,
  private val cashoutPeriodsPersistenceService: CashoutPeriodsPersistenceService,
  private val gamePersistenceService: GamePersistenceService,
  private val gamesService: GamesService,
  private val userAttestationPersistenceService: UserAttestationPersistenceService,
  private val molocoEventService: MolocoEventService
) {
  private val userCheckManager = UserCheckManager(
    userService = userService,
    userSuspicionService = userSuspicionService,
    rewardingFacade = rewardingFacade,
    cashoutPersistenceService = cashoutPersistenceService,
    fraudScoreService = fraudScoreService,
    timeService = timeService,
    trackedEventsPersistenceService = trackedEventsPersistenceService,
    coroutineScope = { testScope.default() },
    adMarketService = adMarketService,
    cashoutPeriodsPersistenceService = cashoutPeriodsPersistenceService,
    gamePersistenceService = gamePersistenceService,
    userAttestationPersistenceService = userAttestationPersistenceService,
    gamesService = gamesService,
    molocoEventService = molocoEventService
  )

  private val cashoutDemand = CashoutDemand(
    provider = PaymentProviderType.PAYPAL,
    countryCode = "US",
    encryptedName = "Encrypted Test Name",
    encryptedAddress = "Encrypted Test Address",
    encryptedEmail = "<EMAIL>",
    emailHash = "emailHash",
    isBonusRequest = false,
    userIp = "ip1"
  )
  private val userId = "u1"
  private val now = Instant.now()
  private val midnight = now.truncatedTo(ChronoUnit.DAYS)
  private val userStub = userDtoStub.copy(id = userId, createdAt = now)

  @BeforeEach
  fun before() {
    userService.mock({ getUser(userId) }, userStub)
    userService.mock({ userExists(userId) }, true)
    timeService.mock({ now() }, midnight.plus(12, ChronoUnit.HOURS))
    rewardingFacade.mock({ getRevenueTotals(any()) }, RevenueTotals(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(0, 0, 0))
    rewardingFacade.mock(
      { loadUserEarningsForMetaId(1) },
      UserEarnings(userId, 1, BigDecimal("1.0"), null)
    )
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    rewardingFacade.mock({ getTotalUsdEarningsForUser(userId) }, BigDecimal(0.1))
  }

  @Test
  fun `create suspicion ON successful cashout WHEN user cashout more than revenue`() {
    initSuccessfulCashoutMocks()
    cashoutPersistenceService.mock({ calculateTotalUsdCashoutsForUser(userId) }, BigDecimal("5.00"))
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("4.00"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))

    testScope.runTest {
      userCheckManager.onSuccessfulCashout(
        cashoutTransactionStub.copy(
          userId = userId,
          emailHash = cashoutDemand.emailHash,
          userIp = cashoutDemand.userIp!!,
          provider = cashoutDemand.provider
        ),
      )
    }

    verifyBlocking(userSuspicionService) {
      userSuspicionService.createUserSuspicion(eq(userId), eq(UserSuspicions.CASHOUT_MORE_THAN_REVENUE), any())
    }
    verifyNoMoreInteractions(userSuspicionService)
  }

  @Test
  fun `NOT create suspicion ON successful cashout WHEN NOT user cashout more than revenue`() {
    initSuccessfulCashoutMocks()
    cashoutPersistenceService.mock({ calculateTotalUsdCashoutsForUser(userId) }, BigDecimal("5.00"))
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("6.00"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))

    testScope.runTest {
      userCheckManager.onSuccessfulCashout(
        cashoutTransactionStub.copy(
          userId = userId,
          emailHash = cashoutDemand.emailHash,
          userIp = cashoutDemand.userIp!!,
          provider = cashoutDemand.provider
        ),
      )
    }

    verifyNoMoreInteractions(userSuspicionService)
  }

  @Test
  fun `NOT create suspicion ON successful cashout WHEN we do not have precalculated revenue for user`() {
    initSuccessfulCashoutMocks()
    cashoutPersistenceService.mock({ calculateTotalUsdCashoutsForUser(userId) }, BigDecimal("100500.00"))
    rewardingFacade.mock({ getRevenueTotals(userId) }, null)

    testScope.runTest {
      userCheckManager.onSuccessfulCashout(
        cashoutTransactionStub.copy(
          userId = userId,
          emailHash = cashoutDemand.emailHash,
          userIp = cashoutDemand.userIp!!,
          provider = cashoutDemand.provider
        ),
      )
    }

    verifyNoMoreInteractions(userSuspicionService)
  }

  @Test
  fun `create suspicion ON successful cashout WHEN email cashout more than total limit`() {
    initSuccessfulCashoutMocks()
    cashoutPersistenceService.mock({ calculateTotalCashoutsForEmail(cashoutDemand.emailHash) }, totalCashoutLimit + BigDecimal.TEN)

    testScope.runTest {
      userCheckManager.onSuccessfulCashout(
        cashoutTransactionStub.copy(
          userId = userId,
          emailHash = cashoutDemand.emailHash,
          userIp = cashoutDemand.userIp!!,
          provider = cashoutDemand.provider
        ),
      )
    }

    verifyBlocking(userSuspicionService) {
      userSuspicionService.createUserSuspicion(eq(userId), eq(UserSuspicions.CASHOUT_MORE_THAN_MAX_AMOUNT), any())
    }
    verifyNoMoreInteractions(userSuspicionService)
  }

  @Test
  fun `NOT create suspicion ON successful cashout WHEN NOT email cashout more than total limit`() {
    initSuccessfulCashoutMocks()
    cashoutPersistenceService.mock({ calculateTotalCashoutsForEmail(cashoutDemand.emailHash) }, totalCashoutLimit - BigDecimal.TEN)

    testScope.runTest {
      userCheckManager.onSuccessfulCashout(
        cashoutTransactionStub.copy(
          userId = userId,
          emailHash = cashoutDemand.emailHash,
          userIp = cashoutDemand.userIp!!,
          provider = cashoutDemand.provider
        ),
      )
    }

    verifyNoMoreInteractions(userSuspicionService)
  }

  @Test
  fun `create suspicion and call onNewUserEmail ON successful cashout WHEN users cashout with duplicate email`() {
    initSuccessfulCashoutMocks()
    cashoutPersistenceService.mock({ countUsersWithSameEmail(userId, cashoutDemand.emailHash) }, MAX_DUPLICATE_CASHOUT_EMAIL_COUNT + 1)

    testScope.runTest {
      userCheckManager.onSuccessfulCashout(
        cashoutTransactionStub.copy(
          userId = userId,
          emailHash = cashoutDemand.emailHash,
          userIp = cashoutDemand.userIp!!,
          provider = cashoutDemand.provider
        ),
      )
    }

    verifyBlocking(userSuspicionService) {
      userSuspicionService.createUserSuspicion(eq(userId), eq(UserSuspicions.DUPLICATE_CASHOUT_EMAIL), any())
    }
    verifyNoMoreInteractions(userSuspicionService)
    verifyBlocking(fraudScoreService) { onSuccessfulCashout(userId, cashoutDemand.provider) }
    verifyBlocking(fraudScoreService) { freezeFraudScore(userId, hasSuccessfulCashout = true) }
    verifyNoMoreInteractions(fraudScoreService)
  }

  @Test
  fun `NOT create suspicion and NOT call onNewUserEmail ON successful cashout WHEN NOT so many users cashout with duplicate email`() {
    initSuccessfulCashoutMocks()
    cashoutPersistenceService.mock({ countUsersWithSameEmail(userId, cashoutDemand.emailHash) }, MAX_DUPLICATE_CASHOUT_EMAIL_COUNT - 1)

    testScope.runTest {
      userCheckManager.onSuccessfulCashout(
        cashoutTransactionStub.copy(
          userId = userId,
          emailHash = cashoutDemand.emailHash,
          userIp = cashoutDemand.userIp!!,
          provider = cashoutDemand.provider
        ),
      )
    }

    verifyNoMoreInteractions(userSuspicionService)
    verifyBlocking(fraudScoreService) { onSuccessfulCashout(userId, cashoutDemand.provider) }
    verifyBlocking(fraudScoreService) { freezeFraudScore(userId, hasSuccessfulCashout = true) }
    verifyNoMoreInteractions(fraudScoreService)
  }

  @Test
  fun `create suspicion ON successful cashout WHEN cashout above limit with same ip`() {
    initSuccessfulCashoutMocks()
    cashoutPersistenceService.mock({ countCashoutSumWithSameIp(cashoutDemand.userIp!!) }, totalCashoutLimit + BigDecimal.TEN)

    testScope.runTest {
      userCheckManager.onSuccessfulCashout(
        cashoutTransactionStub.copy(
          userId = userId,
          emailHash = cashoutDemand.emailHash,
          userIp = cashoutDemand.userIp!!,
          provider = cashoutDemand.provider
        ),
      )
    }

    verifyBlocking(userSuspicionService) {
      userSuspicionService.createUserSuspicion(eq(userId), eq(UserSuspicions.CASHOUT_MORE_THAN_MAX_AMOUNT_FOR_IP), any())
    }
    verifyNoMoreInteractions(userSuspicionService)
  }

  @Test
  fun `NOT create suspicion ON successful cashout WHEN NOT cashout above limit with same ip`() {
    initSuccessfulCashoutMocks()
    cashoutPersistenceService.mock({ countCashoutSumWithSameIp(cashoutDemand.userIp!!) }, totalCashoutLimit - BigDecimal.TEN)

    testScope.runTest {
      userCheckManager.onSuccessfulCashout(
        cashoutTransactionStub.copy(
          userId = userId,
          emailHash = cashoutDemand.emailHash,
          userIp = cashoutDemand.userIp!!,
          provider = cashoutDemand.provider
        ),
      )
    }

    verifyNoMoreInteractions(userSuspicionService)
  }

  @Test
  fun `SHOULD trigger fraudScoreService_onUserFaceVerified ON onUserFaceVerified`() {
    testScope.runTest {
      userCheckManager.onUserFaceVerified(userId)
    }

    verifyBlocking(fraudScoreService) {
      onUserFaceVerified(userId)
    }
  }

  @Test
  fun `SHOULD trigger fraudScoreService_onUserFaceUniquenessCheckFailed ON onUserFaceUniquenessCheckFailed`() {
    testScope.runTest {
      userCheckManager.onUserFaceUniquenessCheckFailed(userId)
    }

    verifyBlocking(fraudScoreService) {
      onUserFaceUniquenessCheckFailed(userId)
    }
  }

  @ParameterizedTest
  @ValueSource(ints = [1, 2, 3, 4, 5])
  fun `SHOULD create tracked events ON onUserEarningsCalculated WHEN user earn 1-4$ in 2 days`(revenue: Int) {
    val userRevenue = BigDecimal(revenue)
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))

    testScope.runTest {
      userCheckManager.onUserEarningsCalculated(1)
    }
    val trackedEvents = mutableListOf<TrackedEvent>()
    trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_01", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
    trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_02", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
    trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_05", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
    trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_075", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))

    repeat(4.coerceAtMost(revenue)) {
      addTrackedEventsForAllPlatforms(userId = userId, eventName = "cashout_2_reached_${it + 1}", trackedEventList = trackedEvents)
    }
    trackedEvents.add(TrackedEvent(userId = userId, eventName = "fb_mobile_level_achieved", platformsToTrack = TrackedEvent.EventTrackingPlatform.FACEBOOK))
    val adMarketEvents = trackedEvents
      .filter { it.platformsToTrack == TrackedEvent.EventTrackingPlatform.FIREBASE }
      .map(TrackedEvent::eventName)

    verifyBlocking(trackedEventsPersistenceService) { addTrackedEvents(trackedEvents) }
    verifyBlocking(adMarketService) { sendMarketEvents(userId, adMarketEvents) }

    verifyNoMoreInteractions(trackedEventsPersistenceService)
  }

  @ParameterizedTest
  @ValueSource(longs = [3, 4, 5])
  fun `SHOULD NOT create tracked events ON onUserEarningsCalculated WHEN user earn 4$ in 3+ days`(days: Long) {
    val userRevenue = BigDecimal("3.0")
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(days, ChronoUnit.DAYS)))

    testScope.runTest {
      userCheckManager.onUserEarningsCalculated(1)
    }

    verifyBlocking(adMarketService) { sendMarketEvents(userId, emptyList()) }
    verifyNoInteractions(trackedEventsPersistenceService)
  }

  @ParameterizedTest
  @ValueSource(longs = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
  fun `SHOULD create tracked events ON onUserEarningsCalculated WHEN user earn 8$ in 1-10 days`(days: Long) {
    val userRevenue = BigDecimal("8.0")
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(days, ChronoUnit.DAYS)))

    testScope.runTest {
      userCheckManager.onUserEarningsCalculated(1)
    }

    val trackedEvents = mutableListOf<TrackedEvent>()
    val adMarketEvents = mutableListOf<String>()
    if (days <= 2) {
      trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_01", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_02", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_05", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_075", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      repeat(4) {
        addTrackedEventsForAllPlatforms(userId = userId, eventName = "cashout_2_reached_${it + 1}", trackedEventList = trackedEvents)
      }
      trackedEvents.add(TrackedEvent(userId = userId, eventName = "fb_mobile_level_achieved", platformsToTrack = TrackedEvent.EventTrackingPlatform.FACEBOOK))
    }
    if (days <= 8) {
      addTrackedEventsForAllPlatforms(userId = userId, eventName = "cashout_8_reached_8", trackedEventList = trackedEvents)
    }
    if (days in 5..6) {
      addTrackedEventsForAllPlatforms(userId = userId, eventName = "rev_on_day_6_reached_6_AND_day_5_retention", trackedEventList = trackedEvents)
      addTrackedEventsForAllPlatforms(userId = userId, eventName = "rev_on_day_6_reached_8_AND_day_5_retention", trackedEventList = trackedEvents)
    }

    adMarketEvents.addAll(
      trackedEvents
        .filter { it.platformsToTrack == TrackedEvent.EventTrackingPlatform.FIREBASE }
        .map(TrackedEvent::eventName))

    if (trackedEvents.size > 0) {
      verifyBlocking(trackedEventsPersistenceService) { addTrackedEvents(trackedEvents) }
      verifyBlocking(adMarketService) { sendMarketEvents(userId, adMarketEvents) }
    }

    verifyNoMoreInteractions(trackedEventsPersistenceService)
  }

  @ParameterizedTest
  @ValueSource(longs = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
  fun `SHOULD create tracked events ON onUserEarningsCalculated WHEN user earn 13$ in 1-10 days`(days: Long) {
    val userRevenue = BigDecimal("13.0")
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(days, ChronoUnit.DAYS)))

    testScope.runTest {
      userCheckManager.onUserEarningsCalculated(1)
    }
    val trackedEvents = mutableListOf<TrackedEvent>()
    val adMarketEvents = mutableListOf<String>()
    if (days <= 2) {
      trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_01", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_02", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_05", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_075", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      repeat(4) {
        addTrackedEventsForAllPlatforms(userId = userId, eventName = "cashout_2_reached_${it + 1}", trackedEventList = trackedEvents)
      }
      trackedEvents.add(TrackedEvent(userId = userId, eventName = "fb_mobile_level_achieved", platformsToTrack = TrackedEvent.EventTrackingPlatform.FACEBOOK))
    }

    if (days <= 8) {
      addTrackedEventsForAllPlatforms(userId = userId, eventName = "cashout_8_reached_8", trackedEventList = trackedEvents)
      addTrackedEventsForAllPlatforms(userId = userId, eventName = "cashout_8_reached_13", trackedEventList = trackedEvents)
    }

    if (days in 5..6) {
      addTrackedEventsForAllPlatforms(userId = userId, eventName = "rev_on_day_6_reached_6_AND_day_5_retention", trackedEventList = trackedEvents)
      addTrackedEventsForAllPlatforms(userId = userId, eventName = "rev_on_day_6_reached_8_AND_day_5_retention", trackedEventList = trackedEvents)
    }

    adMarketEvents.addAll(
      trackedEvents
        .filter { it.platformsToTrack == TrackedEvent.EventTrackingPlatform.FIREBASE }
        .map(TrackedEvent::eventName))

    if (trackedEvents.size > 0) {
      verifyBlocking(trackedEventsPersistenceService) { addTrackedEvents(trackedEvents) }
      verifyBlocking(adMarketService) { sendMarketEvents(userId, adMarketEvents) }
    }
    verifyNoMoreInteractions(trackedEventsPersistenceService)
  }

  @ParameterizedTest
  @ValueSource(longs = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
  fun `SHOULD NOT create tracked events ON onUserEarningsCalculated WHEN user is banned but earn 13$ in 1-10 days`(days: Long) {
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(days, ChronoUnit.DAYS), isBanned = true))

    testScope.runTest {
      userCheckManager.onUserEarningsCalculated(1)
    }

    verifyNoInteractions(adMarketService)
    verifyNoInteractions(trackedEventsPersistenceService)
  }

  @Test
  fun `SHOULD NOT create tracked events ON onUserEarningsCalculated WHEN user does not exist`() {
    userService.mock({ userExists(userId) }, false)

    testScope.runTest {
      userCheckManager.onUserEarningsCalculated(1)
    }

    verifyBlocking(rewardingFacade) { loadUserEarningsForMetaId(1) }
    verifyNoMoreInteractions(rewardingFacade)
    verifyNoInteractions(trackedEventsPersistenceService)
    verifyNoInteractions(adMarketService)
  }

  @ParameterizedTest
  @ValueSource(longs = [9, 10])
  fun `SHOULD NOT create tracked events ON onUserEarningsCalculated WHEN user earn 13$ in 9+ days`(days: Long) {
    val userRevenue = BigDecimal("13.0")
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(days, ChronoUnit.DAYS)))

    testScope.runTest {
      userCheckManager.onUserEarningsCalculated(1)
    }

    verifyBlocking(adMarketService) { sendMarketEvents(userId, emptyList()) }
    verifyNoInteractions(trackedEventsPersistenceService)
  }

  @Test
  fun `SHOULD NOT create tracked events ON onUserEarningsCalculated WHEN user is old`() {
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(10, ChronoUnit.DAYS)))

    testScope.runTest {
      userCheckManager.onUserEarningsCalculated(1)
    }

    verifyBlocking(adMarketService) { sendMarketEvents(userId, emptyList()) }
    verifyNoInteractions(trackedEventsPersistenceService)
  }

  @ParameterizedTest
  @ValueSource(doubles = [0.5, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 15.0, 20.0, 30.0])
  fun `SHOULD create earnings_reached_min_x events ON onUserEarningsCalculated WHEN user is not old`(revenue: Double) {
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(30, ChronoUnit.DAYS)))

    val thresholds = listOf(
      30.0 to "earnings_reached_min_30",
      20.0 to "earnings_reached_min_20",
      15.0 to "earnings_reached_min_15",
      10.0 to "earnings_reached_min_10",
      9.0 to "earnings_reached_min_9",
      8.0 to "earnings_reached_min_8",
      7.0 to "earnings_reached_min_7",
      6.0 to "earnings_reached_min_6",
      5.0 to "earnings_reached_min_5",
      4.0 to "earnings_reached_min_4",
      3.0 to "earnings_reached_min_3",
      2.0 to "earnings_reached_min_2",
      1.0 to "earnings_reached_min_1",
      0.5 to "earnings_reached_min_05"
    )

    rewardingFacade.mock({ getTotalUsdEarningsForUser(userId) }, BigDecimal(revenue))
    val expectedEvents = thresholds
      .filter { (threshold, _) -> revenue >= threshold }
      .map { (_, eventName) -> eventName }

    val actualEventsCaptor = argumentCaptor<List<String>>()

    testScope.runTest {
      userCheckManager.onUserEarningsCalculated(1)
    }

    verifyBlocking(adMarketService) {
      sendMarketEvents(eq(userId), actualEventsCaptor.capture())
    }

    val actualEvents = actualEventsCaptor.firstValue
    assertThat(actualEvents.toSet()).isEqualTo(expectedEvents.toSet())

    verifyNoInteractions(trackedEventsPersistenceService)
  }

  @ParameterizedTest
  @ValueSource(doubles = [0.5, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 15.0, 20.0, 30.0])
  fun `SHOULD not create earnings_reached_min_x events ON onUserEarningsCalculated WHEN user is old`(revenue: Double) {
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(31, ChronoUnit.DAYS)))

    rewardingFacade.mock({ getTotalUsdEarningsForUser(userId) }, BigDecimal(revenue))

    testScope.runTest {
      userCheckManager.onUserEarningsCalculated(1)
    }

    verifyBlocking(adMarketService) {
      sendMarketEvents(userId, emptyList())
    }
    verifyNoInteractions(trackedEventsPersistenceService)
  }

  @ParameterizedTest
  @ValueSource(longs = [1, 2, 3, 4, 5, 6, 7, 8])
  fun `SHOULD create tracked event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user earn 0,1$ in 1,2 day AND EU market`(days: Long) {
    val userRevenue = BigDecimal("0.1")
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(days, ChronoUnit.DAYS)))

    testScope.runTest {
      userCheckManager.onUserEarningsCalculated(1)
    }
    val marketsEvents = mutableListOf<String>()
    val trackedEvents = mutableListOf<TrackedEvent>()
    if (days <= 2) {
      marketsEvents.add("cashout_2_reached_01")
      trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_01", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      verifyBlocking(trackedEventsPersistenceService) { addTrackedEvents(trackedEvents) }
    } else {
      verifyNoInteractions(trackedEventsPersistenceService)
    }
    verifyBlocking(adMarketService) { sendMarketEvents(userId, marketsEvents) }
  }

  @ParameterizedTest
  @ValueSource(longs = [1, 2, 3, 4, 5, 6, 7, 8])
  fun `SHOULD create tracked event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user earn 0,2$ in 1,2 day AND EU market`(days: Long) {
    val userRevenue = BigDecimal("0.2")
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(days, ChronoUnit.DAYS)))

    testScope.runTest {
      userCheckManager.onUserEarningsCalculated(1)
    }
    val marketsEvents = mutableListOf<String>()
    val trackedEvents = mutableListOf<TrackedEvent>()
    if (days <= 2) {
      marketsEvents.add("cashout_2_reached_01")
      marketsEvents.add("cashout_2_reached_02")
      trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_01", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_02", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      verifyBlocking(trackedEventsPersistenceService) { addTrackedEvents(trackedEvents) }
    } else {
      verifyNoInteractions(trackedEventsPersistenceService)
    }
    verifyBlocking(adMarketService) { sendMarketEvents(userId, marketsEvents) }
  }

  @ParameterizedTest
  @ValueSource(longs = [1, 2, 3, 4, 5, 6, 7, 8])
  fun `SHOULD create tracked event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user earn 0,5$ in 1,2 day AND EU market`(days: Long) {
    val userRevenue = BigDecimal("0.5")
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(days, ChronoUnit.DAYS)))

    testScope.runTest {
      userCheckManager.onUserEarningsCalculated(1)
    }
    val marketsEvents = mutableListOf<String>()
    val trackedEvents = mutableListOf<TrackedEvent>()
    if (days <= 2) {
      marketsEvents.add("cashout_2_reached_01")
      marketsEvents.add("cashout_2_reached_02")
      marketsEvents.add("cashout_2_reached_05")
      trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_01", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_02", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_05", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      verifyBlocking(trackedEventsPersistenceService) { addTrackedEvents(trackedEvents) }
    } else {
      verifyNoInteractions(trackedEventsPersistenceService)
    }
    verifyBlocking(adMarketService) { sendMarketEvents(userId, marketsEvents) }
  }

  @ParameterizedTest
  @ValueSource(longs = [1, 2, 3, 4, 5, 6, 7, 8])
  fun `SHOULD create tracked event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user earn 0,75$ in 1,2 day AND EU market`(days: Long) {
    val userRevenue = BigDecimal("0.75")
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(days, ChronoUnit.DAYS)))

    testScope.runTest {
      userCheckManager.onUserEarningsCalculated(1)
    }

    val marketsEvents = mutableListOf<String>()
    val trackedEvents = mutableListOf<TrackedEvent>()
    if (days <= 2) {
      marketsEvents.add("cashout_2_reached_01")
      marketsEvents.add("cashout_2_reached_02")
      marketsEvents.add("cashout_2_reached_05")
      marketsEvents.add("cashout_2_reached_075")
      trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_01", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_02", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_05", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
      trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_075", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))

      verifyBlocking(trackedEventsPersistenceService) {
        addTrackedEvents(trackedEvents)
      }
    } else {
      verifyNoInteractions(trackedEventsPersistenceService)
    }
    verifyBlocking(adMarketService) { sendMarketEvents(userId, marketsEvents) }
  }

  @ParameterizedTest
  @ValueSource(longs = [1, 2])
  fun `SHOULD not create tracked event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user earn 0,09$ in 1,2 day AND EU market`(days: Long) {
    val userRevenue = BigDecimal("0.09")
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(days, ChronoUnit.DAYS)))

    testScope.runTest {
      userCheckManager.onUserEarningsCalculated(1)
    }

    val marketsEvents = mutableListOf<String>()
    verifyNoInteractions(trackedEventsPersistenceService)
    verifyBlocking(adMarketService) { sendMarketEvents(userId, marketsEvents) }
  }

  @Test
  fun `SHOULD not fail ON onUserEarningsCalculated WHEN sendMarketEvents throws`() {
    val userRevenue = BigDecimal(1)
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(userRevenue, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
    adMarketService.throwException({ sendMarketEvents(eq(userId), any()) }, RuntimeException("exception"))

    val trackedEvents = mutableListOf<TrackedEvent>()
    trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_01", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
    trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_02", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
    trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_05", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
    trackedEvents.add(TrackedEvent(userId = userId, eventName = "cashout_2_reached_075", platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE))
    addTrackedEventsForAllPlatforms(userId = userId, eventName = "cashout_2_reached_1", trackedEventList = trackedEvents)
    trackedEvents.add(TrackedEvent(userId = userId, eventName = "fb_mobile_level_achieved", platformsToTrack = TrackedEvent.EventTrackingPlatform.FACEBOOK))

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    verifyBlocking(trackedEventsPersistenceService) { addTrackedEvents(trackedEvents) }

    verifyNoMoreInteractions(trackedEventsPersistenceService)
  }


  @Test
  fun `SHOULD not send ad market events ON onUserEarningsCalculated WHEN work with empty User earnings`() {
    rewardingFacade.mock(
      { loadUserEarningsForMetaId(1) },
      null
    )
    verifyNoInteractions(adMarketService)
    verifyNoInteractions(trackedEventsPersistenceService)
  }

  @Test
  fun `SHOULD trigger freezeFraudScore ON onSuccessfulCashout`() {
    initSuccessfulCashoutMocks()
    cashoutPersistenceService.mock({ calculateTotalUsdCashoutsForUser(userId) }, BigDecimal("2.00"))
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("4.00"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))

    testScope.runTest {
      userCheckManager.onSuccessfulCashout(
        cashoutTransactionStub.copy(
          userId = userId,
          emailHash = cashoutDemand.emailHash,
          userIp = cashoutDemand.userIp!!,
          provider = cashoutDemand.provider
        ),
      )
    }

    verifyBlocking(fraudScoreService) { freezeFraudScore(userId, hasSuccessfulCashout = true) }
  }

  @Test
  fun `SHOULD add user to passed strong attestation table and try to remove FS for sharing IP and deviceLocale violation ON onUserPassedStrongAttestationCheck`() {
    testScope.runTest {
      userCheckManager.onUserPassedStrongAttestationCheck(userId)
    }

    verifyBlocking(userAttestationPersistenceService) { saveUserPassedStrongAttestation(userId) }
    verifyBlocking(fraudScoreService) { checkCountriesAndRemoveFS(userId) }
  }

  @ParameterizedTest
  @CsvSource(
    "US,2.00",
    "GB,1.40",
    "AU,1.40",
    "CH,1.35",
    "DE,1.35",
    "CA,1.30",
    "NZ,1.20",
    "NL,1.10",
    "NO,1.05",
    "DK,1.05",
    "SE,1.00",
    "SG,0.90",
    "FI,0.85",
    "IE,0.70",
    "FR,0.60",
    "PL,0.55",
    "BE,0.50",
    "IT,0.50",
    "ES,0.35",
    "PT,0.30"
  )
  fun `SHOULD create tracked events ON onUserEarningsCalculated WHEN user earned x in 2 days`(countryCode: String, revenueThreshold: BigDecimal) {
    rewardingFacade.mock(
      { loadUserEarningsForMetaId(1) },
      UserEarnings(userId, 1, revenueThreshold, null)
    )
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(revenueThreshold, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
    userService.mock({ getUserCountryCode(userId) }, countryCode)

    testScope.runTest {
      userCheckManager.onUserEarningsCalculated(1)
    }

    verifyBlocking(trackedEventsPersistenceService) {
      addTrackedEvents(check {
        it.any { element -> element.eventName == "cashout_2_reached_x" }
      })
    }
    verifyBlocking(adMarketService) {
      sendMarketEvents(eq(userId), check {
        it.any { element -> element == "cashout_2_reached_x" }
      })
    }

    verifyNoMoreInteractions(trackedEventsPersistenceService)
  }

  @ParameterizedTest
  @CsvSource(
    "US,2.00",
    "GB,1.40",
    "AU,1.40",
    "CH,1.35",
    "DE,1.35",
    "CA,1.30",
    "NZ,1.20",
    "NL,1.10",
    "NO,1.05",
    "DK,1.05",
    "SE,1.00",
    "SG,0.90",
    "FI,0.85",
    "IE,0.70",
    "FR,0.60",
    "PL,0.55",
    "BE,0.50",
    "IT,0.50",
    "ES,0.35",
    "PT,0.30"
  )
  fun `SHOULD NOT create tracked events cashout_2_reached_x ON onUserEarningsCalculated WHEN user did NOT earn x in 2 days`(
    countryCode: String,
    revenueThreshold: BigDecimal
  ) {
    rewardingFacade.mock(
      { loadUserEarningsForMetaId(1) },
      UserEarnings(userId, 1, revenueThreshold, null)
    )
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal.valueOf(0.25), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
    userService.mock({ getUserCountryCode(userId) }, countryCode)

    testScope.runTest {
      userCheckManager.onUserEarningsCalculated(1)
    }

    verifyBlocking(adMarketService) {
      sendMarketEvents(eq(userId), check {
        it.none { element -> element == "cashout_2_reached_x" }
      })
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less day2 revenue", "no 4 day retention"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has got 3$ of revenue by day 2 and day4 retention`(option: String) {
    val eventName = "rev_on_day_2_reached_3_AND_day_4_retention"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("3.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
    when (option) {
      "less day2 revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("2.99"), BigDecimal.ZERO)
      )

      "no 4 day retention" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(3, ChronoUnit.DAYS)))
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = true)
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less day2 revenue", "no 5 day retention"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has got 3$ of revenue by day 2 and day5 retention`(option: String) {
    val eventName = "rev_on_day_2_reached_3_AND_day_5_retention"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("3.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    when (option) {
      "less day2 revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("2.99"), BigDecimal.ZERO)
      )

      "no 5 day retention" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = (option == "less day2 revenue"))
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less day2 revenue", "no 6 day retention"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has got 4$ of revenue by day 2 and day6 retention`(option: String) {
    val eventName = "rev_on_day_2_reached_4_AND_day_6_retention"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("4.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(6, ChronoUnit.DAYS)))
    when (option) {
      "less day2 revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("3.99"), BigDecimal.ZERO)
      )

      "no 6 day retention" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less day2 revenue", "no 5 day retention", "no successful cashouts"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has got 2$ of revenue by day 2 and day5 retention and successful cashout`(
    option: String
  ) {
    val eventName = "rev_on_day_2_reached_2_AND_day_5_retention_AND_successful_cashout"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("2.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less day2 revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("1.99"), BigDecimal.ZERO)
      )

      "no 5 day retention" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(3, ChronoUnit.DAYS)))
      "no successful cashouts" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = option == "no successful cashouts")
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less day2 revenue", "no 4 day retention", "no successful cashouts"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has got 4$ of revenue by day 2 and day4 retention and successful cashout`(
    option: String
  ) {
    val eventName = "rev_on_day_2_reached_4_AND_day_4_retention_AND_successful_cashout"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("5.0"), BigDecimal.ZERO, BigDecimal("4.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less day2 revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("5.0"), BigDecimal.ZERO, BigDecimal("3.99"), BigDecimal.ZERO)
      )

      "no 4 day retention" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(3, ChronoUnit.DAYS)))
      "no successful cashouts" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less day2 revenue", "no 5 day retention", "no successful cashouts"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has got 3$ of revenue by day 2 and day5 retention and successful cashout`(
    option: String
  ) {
    val eventName = "rev_on_day_2_reached_3_AND_day_5_retention_AND_successful_cashout"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("5.0"), BigDecimal.ZERO, BigDecimal("3.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less day2 revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("5.0"), BigDecimal.ZERO, BigDecimal("2.99"), BigDecimal.ZERO)
      )

      "no 5 day retention" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
      "no successful cashouts" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "no 5 day retention", "no successful cashouts", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has got 6$ of revenue by day 6 and day5 retention and successful cashout`(
    option: String
  ) {
    val eventName = "rev_on_day_6_reached_6_AND_day_5_retention_AND_successful_cashout"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("6.0"), BigDecimal.ZERO, BigDecimal("3.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("5.0"), BigDecimal.ZERO, BigDecimal("3.00"), BigDecimal.ZERO)
      )

      "no 5 day retention" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
      "no successful cashouts" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
      "too late" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "no 5 day retention", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has got 6$ of revenue by day 6 and day5 retention`(option: String) {
    val eventName = "rev_on_day_6_reached_6_AND_day_5_retention"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("6.0"), BigDecimal.ZERO, BigDecimal("3.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("5.0"), BigDecimal.ZERO, BigDecimal("3.00"), BigDecimal.ZERO)
      )

      "no 5 day retention" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
      "too late" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "no 5 day retention", "no successful cashouts", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has got 8$ of revenue by day 6 and day5 retention and successful cashout`(
    option: String
  ) {
    val eventName = "rev_on_day_6_reached_8_AND_day_5_retention_AND_successful_cashout"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("8.0"), BigDecimal.ZERO, BigDecimal("3.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("7.99"), BigDecimal.ZERO, BigDecimal("3.00"), BigDecimal.ZERO)
      )

      "no 5 day retention" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
      "too late" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
      "no successful cashouts" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "no 5 day retention", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has got 8$ of revenue by day 6 and day5 retention`(option: String) {
    val eventName = "rev_on_day_6_reached_8_AND_day_5_retention"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("8.0"), BigDecimal.ZERO, BigDecimal("3.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("7.99"), BigDecimal.ZERO, BigDecimal("3.00"), BigDecimal.ZERO)
      )

      "no 5 day retention" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
      "too late" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "no 5 day retention", "no successful cashouts", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has got 4$ of revenue by day 6 and day5 retention and successful cashout`(
    option: String
  ) {
    val eventName = "rev_on_day_6_reached_4_AND_day_5_retention_AND_successful_cashout"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("3.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("3.99"), BigDecimal.ZERO, BigDecimal("3.00"), BigDecimal.ZERO)
      )

      "no 5 day retention" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
      "too late" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
      "no successful cashouts" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less active cps"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has 10 or more active cashout periods`(option: String) {
    val eventName = "10_active_CPs"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("0.01"), BigDecimal.ZERO, BigDecimal("0.01"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 10))
    when (option) {
      "less active cps" -> cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 9))
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = true)
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less active cps", "no successful cashout"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has 10 or more active cashout periods and successful cashout`(option: String) {
    val eventName = "10_active_CPs_AND_successful_cashout"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("0.01"), BigDecimal.ZERO, BigDecimal("0.01"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(5, ChronoUnit.DAYS)))
    cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 10))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less active cps" -> cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 9))
      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less active cps", "no successful cashout", "no day4 retention"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has 10 or more active cashout periods and successful cashout and day4 retention`(
    option: String
  ) {
    val eventName = "10_active_CPs_AND_day_4_retention_AND_successful_cashout"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("0.01"), BigDecimal.ZERO, BigDecimal("0.01"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
    cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 10))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less active cps" -> cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 9))
      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
      "no day4 retention" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(3, ChronoUnit.DAYS)))
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "less active cps", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has got 3$ of revenue by day 2 and 10 active cashout periods`(option: String) {
    val eventName = "rev_on_day_2_reached_3_AND_10_active_CPs"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("3.0"), BigDecimal.ZERO, BigDecimal("2.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
    cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 10))
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("2.99"), BigDecimal.ZERO, BigDecimal("1.99"), BigDecimal.ZERO)
      )

      "less active cps" -> cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 9))
      "too late" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(3, ChronoUnit.DAYS)))
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "less active cps", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has got 4$ of revenue by day 2 and 9 active cashout periods`(option: String) {
    val eventName = "rev_on_day_2_reached_4_AND_9_active_CPs"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("2.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
    cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 9))
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("3.99"), BigDecimal.ZERO, BigDecimal("1.99"), BigDecimal.ZERO)
      )

      "less active cps" -> cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 8))
      "too late" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(3, ChronoUnit.DAYS)))
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = (option == "too late"))
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "less active cps"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has got 6$ of revenue by day 6 and 9 active cashout periods`(option: String) {
    val eventName = "rev_on_day_6_reached_6_AND_9_active_CPs"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("6.0"), BigDecimal.ZERO, BigDecimal("2.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(6, ChronoUnit.DAYS)))
    cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 9))
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("5.99"), BigDecimal.ZERO, BigDecimal("1.99"), BigDecimal.ZERO)
      )

      "less active cps" -> cashoutPeriodsPersistenceService.mock({ getCurrentCashoutPeriodCounters(userId) }, CashoutPeriodCounters(50, 1, 8))
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = (option == "less revenue"))
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "no ofw revenue", "no day3 retention", "no successful cashout", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has got 4$ of revenue by day 6 and some ofw revenue and successful cashout and day3 retention`(
    option: String
  ) {
    val eventName = "rev_on_day_6_reached_4_AND_offerwall_revenue_any_amount_AND_successful_cashout_AND_day_3_retention"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("4.0"), BigDecimal("0.01"), BigDecimal("2.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(6, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("3.99"), BigDecimal("0.01"), BigDecimal("1.99"), BigDecimal.ZERO)
      )

      "no ofw revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("2.0"), BigDecimal.ZERO)
      )

      "no day3 retention" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
      "too late" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "no ofw revenue", "no day3 retention", "no successful cashout", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has got 6$ of revenue by day 6 and some ofw revenue and successful cashout and day3 retention`(
    option: String
  ) {
    val eventName = "rev_on_day_6_reached_6_AND_offerwall_revenue_any_amount_AND_successful_cashout_AND_day_3_retention"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("6.0"), BigDecimal("0.01"), BigDecimal("2.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(6, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("5.99"), BigDecimal("0.01"), BigDecimal("1.99"), BigDecimal.ZERO)
      )

      "no ofw revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("6.0"), BigDecimal.ZERO, BigDecimal("2.0"), BigDecimal.ZERO)
      )

      "no day3 retention" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
      "too late" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "no ofw revenue", "no day3 retention", "no successful cashout", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has got 8$ of revenue by day 6 and some ofw revenue and successful cashout and day3 retention`(
    option: String
  ) {
    val eventName = "rev_on_day_6_reached_8_AND_offerwall_revenue_any_amount_AND_successful_cashout_AND_day_3_retention"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("8.0"), BigDecimal("0.01"), BigDecimal("2.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(6, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("7.99"), BigDecimal("0.01"), BigDecimal("1.99"), BigDecimal.ZERO)
      )

      "no ofw revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("6.0"), BigDecimal.ZERO, BigDecimal("2.0"), BigDecimal.ZERO)
      )

      "no day3 retention" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
      "too late" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "no ofw revenue", "no day3 retention", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has got 4$ of revenue by day 6 and some ofw revenue and day3 retention`(
    option: String
  ) {
    val eventName = "rev_on_day_6_reached_4_AND_offerwall_revenue_any_amount_AND_day_3_retention"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("4.0"), BigDecimal("0.01"), BigDecimal("2.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(6, ChronoUnit.DAYS)))
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("3.99"), BigDecimal("0.01"), BigDecimal("1.99"), BigDecimal.ZERO)
      )

      "no ofw revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("4.0"), BigDecimal.ZERO, BigDecimal("2.0"), BigDecimal.ZERO)
      )

      "no day3 retention" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
      "too late" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = (option == "no ofw revenue"))
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "no successful cashout", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has got 4$ of ofw revenue by day 6 and successful cashout`(option: String) {
    val eventName = "offerwall_revenue_on_day_6_reached_4_AND_successful_cashout"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("4.0"), BigDecimal("4.00"), BigDecimal("2.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(6, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("15.99"), BigDecimal("3.99"), BigDecimal("1.99"), BigDecimal.ZERO)
      )

      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
      "too late" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "less revenue", "too late"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has got 6$ of ofw revenue by day 6`(option: String) {
    val eventName = "offerwall_revenue_on_day_6_reached_6"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("6.0"), BigDecimal("6.00"), BigDecimal("2.0"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(6, ChronoUnit.DAYS)))
    when (option) {
      "less revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("15.99"), BigDecimal("3.99"), BigDecimal("1.99"), BigDecimal.ZERO)
      )

      "too late" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(7, ChronoUnit.DAYS)))
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents)
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "no ofw revenue"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user have got any of ofw revenue`(option: String) {
    val eventName = "offerwall_revenue_any_amount"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("0.01"), BigDecimal("0.01"), BigDecimal("0.01"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(8, ChronoUnit.DAYS)))
    when (option) {
      "no ofw revenue" -> rewardingFacade.mock(
        { getRevenueTotals(userId) },
        RevenueTotals(BigDecimal("0.01"), BigDecimal.ZERO, BigDecimal("0.01"), BigDecimal.ZERO)
      )
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
      verifyBlocking(molocoEventService) { sendEvent(userId, BigDecimal("0.01"), EventType.OW_WITH_REVENUE) }
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = true)
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "no successful cashout", "no day4 retention"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has a successful cashout and day4 retention`(option: String) {
    val eventName = "day_4_retention_AND_successful_cashout"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("0.01"), BigDecimal.ZERO, BigDecimal("0.01"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(4, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
      "no day4 retention" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(3, ChronoUnit.DAYS)))
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = option == "no successful cashout")
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "no successful cashout", "no day3 retention"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has a successful cashout and day3 retention`(option: String) {
    val eventName = "day_3_retention_AND_successful_cashout"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("0.01"), BigDecimal.ZERO, BigDecimal("0.01"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(3, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
      "no day3 retention" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = option == "no successful cashout")
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "no successful cashout", "no day2 retention"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has a successful cashout and day2 retention`(option: String) {
    val eventName = "day_2_retention_AND_successful_cashout"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("0.01"), BigDecimal.ZERO, BigDecimal("0.01"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(2, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
      "no day2 retention" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(1, ChronoUnit.DAYS)))
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = option == "no successful cashout")
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "no successful cashout", "no day1 retention"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has a successful cashout and day1 retention`(option: String) {
    val eventName = "day_1_retention_AND_successful_cashout"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("0.01"), BigDecimal.ZERO, BigDecimal("0.01"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(1, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
      "no day1 retention" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(0, ChronoUnit.DAYS)))
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = option == "no successful cashout")
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["matching", "no successful cashout"])
  fun `SHOULD create new ua event FOR GA and ADJUST ON onUserEarningsCalculated WHEN user has a successful cashout`(option: String) {
    val eventName = "successful_cashout"
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("0.01"), BigDecimal.ZERO, BigDecimal("0.01"), BigDecimal.ZERO))
    userService.mock({ getUser(userId) }, userStub.copy(createdAt = midnight.minus(1, ChronoUnit.DAYS)))
    cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, true)
    when (option) {
      "no successful cashout" -> cashoutPersistenceService.mock({ hasSuccessCashouts(userId) }, false)
    }

    testScope.runTest { userCheckManager.onUserEarningsCalculated(1) }

    val trackedEvents = bothPlatformsEvents(eventName)
    if (option == "matching") {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    } else {
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = true)
      verifySendMarketEventsWasNotTriggeredFor(eventName)
    }
  }

  @ParameterizedTest
  @ValueSource(ints = [0, 1, 45])
  fun `SHOULD do nothing ON onBannersShown WHEN banners shown counter is not from a thresholds list`(bannersShown: Int) {
    val gameId = 1

    testScope.runTest { userCheckManager.onBannersShown(userId, gameId, bannersShown) }

    verifyNoInteractions(gamePersistenceService)
  }

  @ParameterizedTest
  @ValueSource(strings = ["too old", "banned", "not exists", "matching"])
  fun `SHOULD do nothing ON onBannersShown WHEN user does not exist, too old or banned`(option: String) {
    timeService.mock({ now() }, now)
    gamePersistenceService.mock({ getGamesOrderedByFirstTimeBannerShow(userId) }, listOf(1 to now))
    when (option) {
      "too old" -> userService.mock({ getUser(userId) }, userStub.copy(createdAt = now.minus(31, ChronoUnit.DAYS)))
      "banned" -> userService.mock({ getUser(userId) }, userStub.copy(isBanned = true))
      "not exists" -> userService.mock({ userExists(userId) }, false)
    }

    testScope.runTest { userCheckManager.onBannersShown(userId, 1, 120) }

    val eventName = "1st_game_played_2_min"
    val trackedEvents = bothPlatformsEvents(eventName)
    if (option != "matching") {
      verifyNoInteractions(gamePersistenceService)
      verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents, noInteractions = true)
    } else {
      verifyEventsWerePropagatedForSending(trackedEvents, eventName)
    }

  }

  @Test
  fun `SHOULD do not add ad market events ON onBannersShown WHEN it is a 6th played game`() {
    val gameId = 1
    gamePersistenceService.mock(
      { getGamesOrderedByFirstTimeBannerShow(userId) },
      listOf(2 to now, 3 to now, 4 to now, 5 to now, 6 to now, 1 to now)
    ) //game1 is the 6th played game

    testScope.runTest { userCheckManager.onBannersShown(userId, gameId, 120) }

    verifyBlocking(gamePersistenceService) { getGamesOrderedByFirstTimeBannerShow(userId) }
    verifyNoInteractions(trackedEventsPersistenceService, adMarketService)
  }

  @ParameterizedTest
  @CsvSource("120, 1st", "300, 1st", "300, 1st", "120, 3rd", "300, 3rd", "600, 3rd", "1200, 3rd", "1800, 3rd", "2400, 3rd")
  fun `SHOULD add ad market events ON onBannersShown WHEN it is the 1st played game for Android`(timeElapsed: Int, prefix: String) {
    val gameId = if (prefix == "1st") 1 else 3
    gamePersistenceService.mock({ getGamesOrderedByFirstTimeBannerShow(userId) }, listOf(1 to now, 2 to now, 3 to now))

    testScope.runTest { userCheckManager.onBannersShown(userId, gameId, timeElapsed) }

    val eventName = when (timeElapsed) {
      120 -> "${prefix}_game_played_2_min"
      300 -> "${prefix}_game_played_5_min"
      600 -> "${prefix}_game_played_10_min"
      1200 -> "${prefix}_game_played_20_min"
      1800 -> "${prefix}_game_played_30_min"
      2400 -> "${prefix}_game_played_40_min"
      else -> ""
    }
    val trackedEvents = bothPlatformsEvents(eventName)
    verifyEventsWerePropagatedForSending(trackedEvents, eventName)
  }

  @ParameterizedTest
  @CsvSource("120, 1st", "300, 1st", "300, 1st", "120, 3rd", "300, 3rd", "600, 3rd", "1200, 3rd", "1800, 3rd", "2400, 3rd")
  fun `SHOULD add ad market events ON onBannersShown WHEN it is the 1st played game for IOS`(timeElapsed: Int, prefix: String) {
    userService.mock({ getUser(userId) }, userStub.copy(appPlatform = AppPlatform.IOS))
    val gameId = if (prefix == "1st") 1 else 3
    gamePersistenceService.mock({ getGamesOrderedByFirstTimeBannerShow(userId) }, listOf(1 to now, 2 to now, 3 to now))

    testScope.runTest { userCheckManager.onBannersShown(userId, gameId, timeElapsed) }

    val eventName = when (timeElapsed) {
      120 -> "${prefix}_game_played_2_min"
      300 -> "${prefix}_game_played_5_min"
      600 -> "${prefix}_game_played_10_min"
      1200 -> "${prefix}_game_played_20_min"
      1800 -> "${prefix}_game_played_30_min"
      2400 -> "${prefix}_game_played_40_min"
      else -> ""
    }

    when (timeElapsed) {
      120, 300, 600 -> {
        val trackedEvents = bothPlatformsEvents(eventName)
        verifyEventsWerePropagatedForSending(trackedEvents, eventName)
      }

      1200, 1800, 2400 -> {
        verifyNoInteractions(trackedEventsPersistenceService)
        verifyNoInteractions(adMarketService)
      }
    }
  }

  @ParameterizedTest
  @CsvSource(
    "0, ${ApplicationId.SOLITAIRE_VERSE_APP_ID}",
    "300, ${ApplicationId.SOLITAIRE_VERSE_APP_ID}",
    "600, ${ApplicationId.TREASURE_MASTER_APP_ID}",
    "900, ${ApplicationId.MAD_SMASH_APP_ID}",
    "1200, ${ApplicationId.BUBBLE_POP_APP_ID}",
    "1800, ${ApplicationId.WATER_SORTER_APP_ID}",
    "2400, untracked"
  )
  fun `SHOULD add ad market events ON onBannersShown WHEN it is the particular game being played on Android`(timeElapsed: Int, application: String) {
    val gameId = 1
    gamesService.mock({ getGameId(application, userDtoStub.appPlatform) }, gameId)

    gamePersistenceService.mock({ getGamesOrderedByFirstTimeBannerShow(userId) }, listOf(1 to now, 2 to now, 3 to now))

    testScope.runTest { userCheckManager.onBannersShown(userId, gameId, timeElapsed) }

    val triggeredEvents = when {
      application == ApplicationId.SOLITAIRE_VERSE_APP_ID && timeElapsed == 0 -> emptyList()
      application == ApplicationId.SOLITAIRE_VERSE_APP_ID && timeElapsed == 300 -> listOf("1st_game_played_5_min", "play_solitaire_5min")
      application == ApplicationId.TREASURE_MASTER_APP_ID && timeElapsed == 600 -> listOf("1st_game_played_10_min", "play_treasuremaster_10min", "play_treasuremaster_5min")
      application == ApplicationId.MAD_SMASH_APP_ID && timeElapsed == 900 -> listOf("play_madsmash_15min", "play_madsmash_10min", "play_madsmash_5min")
      application == ApplicationId.BUBBLE_POP_APP_ID && timeElapsed == 1200 -> listOf("1st_game_played_20_min", "play_bubblepop_15min", "play_bubblepop_10min", "play_bubblepop_5min")
      application == ApplicationId.WATER_SORTER_APP_ID && timeElapsed == 1800 -> listOf("1st_game_played_30_min", "play_watersorter_15min", "play_watersorter_10min", "play_watersorter_5min")
      application == "untracked" && timeElapsed == 2400 -> listOf("1st_game_played_40_min")
      else -> emptyList()
    }

    if (triggeredEvents.isEmpty()) {
      verifyNoInteractions(trackedEventsPersistenceService, adMarketService)
    } else {
      val trackedEvents = triggeredEvents.flatMap { bothPlatformsEvents(it) }
      verifyEventsWerePropagatedForSending(trackedEvents, triggeredEvents)
    }
  }

  private fun verifyAddTrackedEventsWasNotTriggeredFor(trackedEvents: List<TrackedEvent>, noInteractions: Boolean = false) {
    if (noInteractions) {
      verifyBlocking(trackedEventsPersistenceService, never()) { addTrackedEvents(any()) }
    } else {
      verifyBlocking(trackedEventsPersistenceService) { addTrackedEvents(argThat { this.none { it in trackedEvents } }) }
    }
  }


  private fun verifySendMarketEventsWasNotTriggeredFor(eventName: String) {
    verifyBlocking(adMarketService) { sendMarketEvents(eq(userId), argThat { this.none { it == eventName } }) }
  }

  private fun verifyEventsWerePropagatedForSending(trackedEvents: List<TrackedEvent>, eventName: String) {
    verifyEventsWerePropagatedForSending(trackedEvents, listOf(eventName))
  }

  private fun verifyEventsWerePropagatedForSending(trackedEvents: List<TrackedEvent>, eventNames: List<String>) {
    verifyBlocking(trackedEventsPersistenceService) { addTrackedEvents(argThat { containsAll(trackedEvents) }) }
    verifyBlocking(adMarketService) { sendMarketEvents(eq(userId), argThat { containsAll(eventNames) }) }
  }

  private fun bothPlatformsEvents(eventName: String) = listOf(
    TrackedEvent(userId = userId, eventName = eventName, platformsToTrack = TrackedEvent.EventTrackingPlatform.FIREBASE),
    TrackedEvent(userId = userId, eventName = eventName, platformsToTrack = TrackedEvent.EventTrackingPlatform.FACEBOOK)
  )

  private fun initSuccessfulCashoutMocks() {
    cashoutPersistenceService.mock({ calculateTotalUsdCashoutsForUser(userId) }, BigDecimal("5.00"))
    rewardingFacade.mock({ getRevenueTotals(userId) }, RevenueTotals(BigDecimal("6.00"), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO))
    cashoutPersistenceService.mock({ calculateTotalCashoutsForEmail(cashoutDemand.emailHash) }, BigDecimal("5.00"))
    cashoutPersistenceService.mock({ countUsersWithSameEmail(userId, cashoutDemand.emailHash) }, 0L)
    cashoutPersistenceService.mock({ countCashoutSumWithSameIp(cashoutDemand.userIp!!) }, BigDecimal("5.00"))
  }

  private fun addTrackedEventsForAllPlatforms(
    @Suppress("SameParameterValue") userId: String,
    eventName: String,
    trackedEventList: MutableList<TrackedEvent>
  ) {
    trackedEventList.addAll(
      TrackedEvent.EventTrackingPlatform.entries.map { platform ->
        TrackedEvent(
          userId = userId,
          eventName = eventName,
          platformsToTrack = platform
        )
      }
    )
  }

}
