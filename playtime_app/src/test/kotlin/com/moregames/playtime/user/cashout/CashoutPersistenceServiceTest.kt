package com.moregames.playtime.user.cashout

import assertk.assertThat
import assertk.assertions.*
import com.moregames.base.app.PaymentProviderType
import com.moregames.base.app.PaymentProviderType.*
import com.moregames.base.app.UserIdentifierType
import com.moregames.base.config.CfgCashoutStatusProvidersImagesTable
import com.moregames.base.table.DatabaseExtension
import com.moregames.base.table.PaymentProviderTable
import com.moregames.base.table.UserCashoutTransactionsTable
import com.moregames.base.table.UserCashoutTransactionsTable.Status.*
import com.moregames.base.table.UserCashoutTransactionsTable.operationalWithholdAmountUsd
import com.moregames.base.table.UserCashoutTransactionsTable.userCurrencyAmount
import com.moregames.base.table.UserCashoutTransactionsTable.userCurrencyCode
import com.moregames.base.user.UserPersonals
import com.moregames.base.util.TimeService
import com.moregames.playtime.earnings.dto.UserCurrencyEarnings
import com.moregames.playtime.user.cashout.dto.*
import com.moregames.playtime.user.prepareUser
import com.moregames.playtime.utils.cashoutTransactionStub
import kotlinx.coroutines.runBlocking
import org.apache.commons.lang3.RandomStringUtils.randomAlphabetic
import org.apache.commons.lang3.RandomStringUtils.randomAlphanumeric
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.deleteAll
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.time.temporal.ChronoUnit.HOURS
import java.time.temporal.ChronoUnit.MINUTES
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith
import kotlin.test.assertNull

@ExtendWith(DatabaseExtension::class)
class CashoutPersistenceServiceTest(private val database: Database) {

  private val timeService: TimeService = mock()
  val now: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)
  lateinit var service: CashoutPersistenceService

  @BeforeEach
  fun before() {
    service = CashoutPersistenceService(database, timeService)
    transaction(database) {
      //why, Carl... how can I check the real values now...
      PaymentProviderTable.deleteAll()
    }
    whenever(timeService.now()).thenReturn(now)
  }

  private companion object {
    val provider1 = CashoutProvider(
      displayName = "Amazon",
      url = "https://www.amazon.com",
      videoUrl = "https://www.youtube.com",
      iconFilename = "icon",
      largeIconFilename = "largeIcon",
      smallIconFilename = "smallIcon",
      text = "text",
      shortText = "shortText",
      providerType = AMAZON,
      disclaimer = "disclaimer",
      emailHint = "Amazon account",
      minimumAmount = BigDecimal("0.01"),
      maximumAmount = BigDecimal("2000.00"),
      orderKey = 0,
      identifierType = UserIdentifierType.EMAIL,
      identifierHint = "Amazon account"
    )
    val provider2 = CashoutProvider(
      displayName = "PayPal",
      url = "https://www.paypal.com",
      videoUrl = "https://www.youtube.com",
      iconFilename = "icon2",
      largeIconFilename = "largeIcon2",
      smallIconFilename = "smallIcon2",
      text = "text2",
      shortText = "sortText2",
      providerType = PAYPAL,
      disclaimer = null,
      emailHint = "PayPal account",
      minimumAmount = null,
      maximumAmount = null,
      orderKey = 0,
      identifierType = UserIdentifierType.EMAIL,
      identifierHint = "PayPal account"
    )
    val cashoutProviders = listOf(
      provider1, provider2
    )
  }

  @Test
  fun `SHOULD return user ids ON loadUserIdsForEmail WHEN user email match the request`() {
    runBlocking {
      val userId1 = database.prepareUser()
      val userId2 = database.prepareUser()
      val userId3 = database.prepareUser()

      database.prepareUserCashoutTransaction(userId = userId1, encryptedEmail = "<EMAIL>", emailHash = "emailHash")
      database.prepareUserCashoutTransaction(userId = userId2, encryptedEmail = "<EMAIL>", emailHash = "anotherHash")
      database.prepareUserCashoutTransaction(userId = userId3, encryptedEmail = "<EMAIL>", emailHash = "emailHash")
      database.prepareUserCashoutTransaction(userId = userId1, encryptedEmail = "<EMAIL>", emailHash = "emailHash")

      val actual = service.loadUserIdsForEmails(setOf("emailHash"))

      assertThat(actual.size).isEqualTo(2)
      assertThat(actual).containsOnly(userId1 to "emailHash", userId3 to "emailHash")
    }
  }

  @Test
  fun `SHOULD load all enabled payment providers ON loadPaymentProviders`() {
    cashoutProviders.forEach { database.preparePaymentProvider(it, disabledUntil = null) }
    database.preparePaymentProvider(
      provider1.copy(displayName = "Disabled", providerType = CLEAN_AIR_TASK_FORCE, orderKey = 0),
      enabled = false,
      disabledUntil = null
    )

    val actual = runBlocking {
      service.loadPaymentProviders("US")
    }

    assertThat(actual).isEqualTo(cashoutProviders)
  }

  @Test
  fun `SHOULD load all enabled payment providers for specific country ON loadPaymentProviders when providers were temporarily disabled sometime ago`() {
    cashoutProviders.forEach { database.preparePaymentProvider(it, disabledUntil = Instant.now().minusSeconds(600)) }
    database.preparePaymentProvider(
      provider1.copy(displayName = "Disabled", providerType = CLEAN_AIR_TASK_FORCE, orderKey = 0),
      enabled = false,
      disabledUntil = null
    )
    database.preparePaymentProvider(provider1.copy(displayName = "Canada"), countryCode = "CA")
    database.preparePaymentProvider(provider1.copy(displayName = "Tanzania"), countryCode = "TZ")

    val actual = runBlocking {
      service.loadPaymentProviders("US")
    }

    assertThat(actual).isEqualTo(cashoutProviders)
  }

  @Test
  fun `SHOULD load payment provider for specific country ON loadPaymentProvider WHEN provider is enabled`() {
    val provider = cashoutProviders.first()
    database.preparePaymentProvider(provider, disabledUntil = null)
    database.preparePaymentProvider(provider.copy(displayName = "Uganda"), countryCode = "UG", disabledUntil = null)

    runBlocking {
      val actual = service.loadPaymentProvider(provider.providerType, "US")

      assertThat(actual).isEqualTo(provider)

      val actualUG = service.loadPaymentProvider(provider.providerType, "UG")

      assertThat(actualUG).isEqualTo(provider.copy(displayName = "Uganda"))
    }
  }

  @Test
  fun `SHOULD return null ON loadPaymentProvider WHEN requested provider is disabled`() {
    database.preparePaymentProvider(
      provider = provider1.copy(displayName = "Disabled", providerType = CLEAN_AIR_TASK_FORCE, orderKey = 0),
      enabled = false
    )

    val actual = runBlocking {
      service.loadPaymentProvider(CLEAN_AIR_TASK_FORCE, "US")
    }

    assertThat(actual).isNull()
  }

  @Test
  fun `SHOULD NOT load payment providers ON loadPaymentProviders when they are temporarily disabled`() {
    val disabledProvider = provider1.copy(displayName = "Disabled", providerType = CLEAN_AIR_TASK_FORCE, orderKey = 0)
    cashoutProviders.forEach { database.preparePaymentProvider(it) }
    database.preparePaymentProvider(
      provider = disabledProvider,
      disabledUntil = now.plusSeconds(10)
    )

    val actual = runBlocking {
      service.loadPaymentProviders("US")
    }

    assertThat(actual).isEqualTo(cashoutProviders)
  }

  @Test
  fun `SHOULD return null ON loadPaymentProvider WHEN requested provider is temporarily disabled`() {
    val provider = provider1.copy(displayName = "Disabled", providerType = CLEAN_AIR_TASK_FORCE, orderKey = 0)
    database.preparePaymentProvider(
      provider = provider,
      disabledUntil = now.plusSeconds(10)
    )

    val actual = runBlocking {
      service.loadPaymentProvider(provider.providerType, "US")
    }

    assertThat(actual).isNull()
  }

  @Test
  fun `SHOULD load cashout transaction data ON loadTransaction WHEN amount and currency are not specified`() {
    val transactionId = database.prepareUserCashoutTransaction(cashedOutAmountUsd = null, userCurrencyCode = null, encryptedName = "Vlad")

    val actual = runBlocking { service.loadTransaction(transactionId) }

    transaction(database) {
      assertThat(
        actual
      ).isEqualTo(
        UserCashoutTransactionsTable
          .select { UserCashoutTransactionsTable.id eq transactionId }
          .first()
          .let {
            CashoutTransactionDto(
              cashoutTransactionId = it[UserCashoutTransactionsTable.id].value,
              userId = it[UserCashoutTransactionsTable.userId],
              encryptedEmail = it[UserCashoutTransactionsTable.encryptedEmail].orEmpty(),
              emailHash = it[UserCashoutTransactionsTable.emailHash].orEmpty(),
              userIp = it[UserCashoutTransactionsTable.userIp] ?: "",
              provider = PaymentProviderType.byKey(it[UserCashoutTransactionsTable.provider]),
              amountUsd = it[UserCashoutTransactionsTable.cashedOutAmountUsd] ?: BigDecimal.ZERO,
              userCurrency = Currency.getInstance(it[userCurrencyCode] ?: "USD"),
              userCurrencyAmount = it[userCurrencyAmount] ?: BigDecimal.ZERO,
              status = UserCashoutTransactionsTable.Status.byKey(it[UserCashoutTransactionsTable.status]),
              encryptedUserName = it[UserCashoutTransactionsTable.encryptedName].orEmpty(),
              encryptedAddress = it[UserCashoutTransactionsTable.encryptedAddress].orEmpty(),
            )
          }
      )
    }
  }

  @Test
  fun `SHOULD create demand ON createDemand`() {
    val userId = database.prepareUser()
    val transactionId = UUID.randomUUID().toString()
    val cashoutDemand = CashoutDemand(
      AMAZON,
      "US",
      "encryptedName",
      "encryptedAddress",
      "encryptedEmail",
      "emailHash",
      false,
      "127.0.0.1",
      normalizedEmailHash = "normalizedEmailHash",
      normalizedEncryptedEmail = "normalizedEncryptedEmail"
    )

    runBlocking {
      service.createTransaction(userId, transactionId, cashoutDemand)
    }

    val actual = transaction(database) {
      UserCashoutTransactionsTable.select { UserCashoutTransactionsTable.id eq transactionId }.first()
    }

    assertThat(actual[UserCashoutTransactionsTable.provider]).isEqualTo(cashoutDemand.provider.key)
    assertThat(actual[UserCashoutTransactionsTable.countryCode]).isEqualTo(cashoutDemand.countryCode)
    assertThat(actual[UserCashoutTransactionsTable.encryptedName]).isEqualTo(cashoutDemand.encryptedName)
    assertThat(actual[UserCashoutTransactionsTable.encryptedAddress]).isEqualTo(cashoutDemand.encryptedAddress)
    assertThat(actual[UserCashoutTransactionsTable.encryptedEmail]).isEqualTo(cashoutDemand.encryptedEmail)
    assertThat(actual[UserCashoutTransactionsTable.emailHash]).isEqualTo("emailHash")
    assertThat(actual[UserCashoutTransactionsTable.type]).isEqualTo(UserCashoutTransactionsTable.Type.EARNINGS.key)
    assertThat(actual[UserCashoutTransactionsTable.userIp]).isEqualTo(cashoutDemand.userIp)
    assertThat(actual[UserCashoutTransactionsTable.userHandle]).isNull()
    assertThat(actual[UserCashoutTransactionsTable.normalizedEmailHash]).isEqualTo("normalizedEmailHash")
    assertThat(actual[UserCashoutTransactionsTable.normalizedEncryptedEmail]).isEqualTo("normalizedEncryptedEmail")
  }

  @Test
  fun `SHOULD create demand without email ON createDemand WHEN Venmo`() {
    val userId = database.prepareUser()
    val transactionId = UUID.randomUUID().toString()
    val cashoutDemand =
      CashoutDemand(VENMO, "US", "encryptedName", "address", userHandle = "venmoUserHandle", emailHash = "", isBonusRequest = false, userIp = "127.0.0.1")

    runBlocking {
      service.createTransaction(userId, transactionId, cashoutDemand)
    }

    val actual = transaction(database) {
      UserCashoutTransactionsTable.select { UserCashoutTransactionsTable.id eq transactionId }.first()
    }

    assertThat(actual[UserCashoutTransactionsTable.provider]).isEqualTo(cashoutDemand.provider.key)
    assertThat(actual[UserCashoutTransactionsTable.countryCode]).isEqualTo(cashoutDemand.countryCode)
    assertThat(actual[UserCashoutTransactionsTable.encryptedName]).isEqualTo(cashoutDemand.encryptedName)
    assertThat(actual[UserCashoutTransactionsTable.encryptedAddress]).isEqualTo(cashoutDemand.encryptedAddress)
    assertThat(actual[UserCashoutTransactionsTable.encryptedEmail]).isEqualTo("")
    assertThat(actual[UserCashoutTransactionsTable.emailHash]).isEqualTo("")
    assertThat(actual[UserCashoutTransactionsTable.type]).isEqualTo(UserCashoutTransactionsTable.Type.EARNINGS.key)
    assertThat(actual[UserCashoutTransactionsTable.userIp]).isEqualTo(cashoutDemand.userIp)
    assertThat(actual[UserCashoutTransactionsTable.userHandle]).isEqualTo("venmoUserHandle")
    assertThat(actual[UserCashoutTransactionsTable.normalizedEncryptedEmail]).isEqualTo("")
    assertThat(actual[UserCashoutTransactionsTable.normalizedEmailHash]).isEqualTo("")
  }

  @Test
  fun `SHOULD update demand status ON updateDemandStatus`() {
    val transactionId = database.prepareUserCashoutTransaction(status = INITIAL)

    runBlocking {
      service.updateTransactionStatus(transactionId, FAILED)
    }

    val actual = transaction(database) { UserCashoutTransactionsTable.select { UserCashoutTransactionsTable.id eq transactionId }.first() }
    assertThat(actual[UserCashoutTransactionsTable.status]).isEqualTo(FAILED.key)
  }

  @Test
  fun `SHOULD update demand status AND set processed field ON updateDemandStatus WHEN new status is SUCCESSFUL`() {
    val transactionId = database.prepareUserCashoutTransaction(status = REQUESTED)

    runBlocking {
      service.updateTransactionStatus(transactionId, SUCCESSFUL)
    }

    val actual = transaction(database) { UserCashoutTransactionsTable.select { UserCashoutTransactionsTable.id eq transactionId }.first() }
    assertThat(actual[UserCashoutTransactionsTable.status]).isEqualTo(SUCCESSFUL.key)
    assertThat(actual[UserCashoutTransactionsTable.processedAt]).isEqualTo(now)
  }

  @Test
  fun `SHOULD load transactions related to user for recent 24 hours ON loadRecentCashoutsByUserId`() {
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()
    val transactionId1 = database.prepareUserCashoutTransaction(userId = userId1, createdAt = now)
    val transactionId2 = database.prepareUserCashoutTransaction(userId = userId1, createdAt = now.minus(6, HOURS))
    database.prepareUserCashoutTransaction(userId = userId1, createdAt = now.minus(25, HOURS)) // created earlier
    database.prepareUserCashoutTransaction(userId = userId2, createdAt = now) // another user

    val actual = runBlocking {
      service.loadRecentCashoutsByUserId(userId1)
    }

    assertThat(actual).containsOnly(
      SimplifiedCashoutDto(transactionId1, INITIAL, PAYPAL),
      SimplifiedCashoutDto(transactionId2, INITIAL, PAYPAL)
    )
  }

  @Test
  fun `SHOULD load transactions related to email for recent 24 hours ON loadRecentCashoutsByUserId`() {
    val email = UUID.randomUUID().toString() + "@softbakedapps.com"
    val emailHash = "emailHash_loadRecentCashoutsByUserId"
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()
    val transactionId1 = database.prepareUserCashoutTransaction(userId = userId1, encryptedEmail = email, createdAt = now, emailHash = emailHash)
    val transactionId2 =
      database.prepareUserCashoutTransaction(userId = userId1, encryptedEmail = email, createdAt = now.minus(6, HOURS), emailHash = emailHash)
    database.prepareUserCashoutTransaction(userId = userId1, encryptedEmail = email, createdAt = now.minus(25, HOURS), emailHash = emailHash) // created earlier
    val transactionId4 =
      database.prepareUserCashoutTransaction(userId = userId2, encryptedEmail = email, createdAt = now, emailHash = emailHash) // another user, same email

    val actual = runBlocking {
      service.loadRecentCashoutsByEmail(emailHash)
    }

    assertThat(actual).containsOnly(
      SimplifiedCashoutDto(transactionId1, INITIAL, PAYPAL),
      SimplifiedCashoutDto(transactionId2, INITIAL, PAYPAL),
      SimplifiedCashoutDto(transactionId4, INITIAL, PAYPAL)
    )
  }

  @Test
  fun `SHOULD return amount other users associated with email ON countUsersWithSameEmail`() {
    val email = "<EMAIL>"
    val emailHash = "email hash"
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()
    val userId3 = database.prepareUser()
    val userId4 = database.prepareUser()
    database.prepareUserCashoutTransaction(userId = userId1, encryptedEmail = email, emailHash = emailHash, createdAt = now)
    database.prepareUserCashoutTransaction(
      userId = userId2,
      encryptedEmail = email,
      emailHash = emailHash,
      createdAt = now.minus(35, ChronoUnit.DAYS)
    ) // non-recent usage
    database.prepareUserCashoutTransaction(userId = userId3, encryptedEmail = email, emailHash = emailHash, createdAt = now)
    database.prepareUserCashoutTransaction(userId = userId3, encryptedEmail = email, emailHash = emailHash, createdAt = now) // check deduplication
    database.prepareUserCashoutTransaction(userId = userId4, encryptedEmail = "anotherEmail_cuwse", emailHash = "another hash", createdAt = now)

    runBlocking {
      assertThat(service.countUsersWithSameEmail(userId1, emailHash)).isEqualTo(2)
      assertThat(service.countUsersWithSameEmail(userId2, emailHash)).isEqualTo(2)
      assertThat(service.countUsersWithSameEmail(userId3, emailHash)).isEqualTo(2)
      assertThat(service.countUsersWithSameEmail(userId4, emailHash)).isEqualTo(3)
    }
  }

  @Test
  fun `SHOULD return other users ids recently associated with email ON getRecentUserIdsWithSameEmail`() {
    val email = "<EMAIL>"
    val emailHash = "emailHash_getRecentUserIdsWithSameEmail"
    val userId1 = database.prepareUser()
    val userId2 = database.prepareUser()
    val userId3 = database.prepareUser()
    database.prepareUserCashoutTransaction(userId = userId1, encryptedEmail = email, createdAt = now, emailHash = emailHash)
    database.prepareUserCashoutTransaction(
      userId = userId2,
      encryptedEmail = email,
      createdAt = now.minus(35, ChronoUnit.DAYS),
      emailHash = emailHash
    ) // non-recent usage
    database.prepareUserCashoutTransaction(userId = userId3, encryptedEmail = email, createdAt = now, emailHash = emailHash)
    database.prepareUserCashoutTransaction(userId = userId3, encryptedEmail = email, createdAt = now, emailHash = emailHash) // check deduplication

    runBlocking {
      assertThat(service.getRecentUserIdsWithSameEmail(userId1, emailHash)).containsOnly(userId3)
      assertThat(service.getRecentUserIdsWithSameEmail(userId2, emailHash)).containsOnly(userId1, userId3)
      assertThat(service.getRecentUserIdsWithSameEmail(userId3, emailHash)).containsOnly(userId1)
    }
  }

  @Test
  fun `SHOULD return flag ON hasSuccessCashouts`() {
    runBlocking {
      val userId = database.prepareUser()

      assertThat(service.hasSuccessCashouts(userId)).isFalse()

      database.prepareUserCashoutTransaction(userId = userId, encryptedEmail = "<EMAIL>")

      assertThat(service.hasSuccessCashouts(userId)).isTrue()
    }
  }

  @Test
  fun `SHOULD return true flag ON hasSuccessCashouts WHEN all cashouts are failed`() {
    runBlocking {
      val userId = database.prepareUser()

      assertThat(service.hasSuccessCashouts(userId)).isFalse()

      database.prepareUserCashoutTransaction(userId = userId, encryptedEmail = "<EMAIL>", status = FAILED)
      database.prepareUserCashoutTransaction(userId = userId, encryptedEmail = "<EMAIL>", status = FAILED)

      assertThat(service.hasSuccessCashouts(userId)).isFalse()
    }
  }

  @Test
  fun `SHOULD return flag ON hasCashouts`() {
    runBlocking {
      val userId = database.prepareUser()

      assertThat(service.hasCashouts(userId)).isFalse()

      database.prepareUserCashoutTransaction(userId = userId, encryptedEmail = "<EMAIL>", status = FAILED)

      assertThat(service.hasCashouts(userId)).isTrue()
    }
  }

  @Test
  fun `SHOULD add cashout amount value ON updateCashoutTransactionCashoutAmount`() {
    val transactionId = database.prepareUserCashoutTransaction()

    transaction(database) {
      assertThat(
        UserCashoutTransactionsTable
          .select { UserCashoutTransactionsTable.id eq transactionId }
          .map { it[UserCashoutTransactionsTable.cashedOutAmountUsd] }
          .first()
      ).isNull()
    }

    runBlocking {
      service.updateCashoutTransactionCashoutAmount(
        transactionId = transactionId,
        amountUsd = BigDecimal("5.00"),
        operationalWithholdAmountUsd = BigDecimal("0.05"),
        userCurrency = Currency.getInstance("CAD"),
        userCurrencyAmount = BigDecimal("5.50"),
        operationalWithholdUserCurrencyAmount = BigDecimal("0.04")
      )
    }

    transaction(database) {
      UserCashoutTransactionsTable
        .select { UserCashoutTransactionsTable.id eq transactionId }
        .first()
        .let {
          assertThat(it[UserCashoutTransactionsTable.cashedOutAmountUsd]).isEqualTo(BigDecimal("5.00"))
          assertThat(it[userCurrencyCode]).isEqualTo("CAD")
          assertThat(it[userCurrencyAmount]).isEqualTo(BigDecimal("5.50"))
          assertThat(it[operationalWithholdAmountUsd]).isEqualTo(BigDecimal("0.05"))
          assertThat(it[UserCashoutTransactionsTable.operationalWithholdUserCurrencyAmount]).isEqualTo(BigDecimal("0.04"))
        }
    }
  }

  @Test
  fun `SHOULD load cashout transaction data ON loadTransaction`() {
    val transactionId = database.prepareUserCashoutTransaction(encryptedName = "Alex")

    val actual = runBlocking { service.loadTransaction(transactionId) }

    transaction(database) {
      assertThat(
        actual
      ).isEqualTo(
        UserCashoutTransactionsTable
          .select { UserCashoutTransactionsTable.id eq transactionId }
          .first()
          .let {
            CashoutTransactionDto(
              cashoutTransactionId = it[UserCashoutTransactionsTable.id].value,
              userId = it[UserCashoutTransactionsTable.userId],
              encryptedEmail = it[UserCashoutTransactionsTable.encryptedEmail].orEmpty(),
              emailHash = it[UserCashoutTransactionsTable.emailHash].orEmpty(),
              userIp = it[UserCashoutTransactionsTable.userIp] ?: "",
              provider = PaymentProviderType.byKey(it[UserCashoutTransactionsTable.provider]),
              amountUsd = it[UserCashoutTransactionsTable.cashedOutAmountUsd] ?: BigDecimal.ZERO,
              userCurrency = Currency.getInstance(it[userCurrencyCode] ?: "USD"),
              userCurrencyAmount = it[userCurrencyAmount] ?: BigDecimal.ZERO,
              status = UserCashoutTransactionsTable.Status.byKey(it[UserCashoutTransactionsTable.status]),
              encryptedUserName = it[UserCashoutTransactionsTable.encryptedName].orEmpty(),
              encryptedAddress = it[UserCashoutTransactionsTable.encryptedAddress].orEmpty(),
            )
          }
      )
    }
  }

  @Test
  fun `SHOULD throw error ON loadTransaction WHEN transaction doesn't exist`() {
    assertFailsWith(IllegalArgumentException::class) {
      runBlocking { service.loadTransaction("nonExistTransactionId") }
    }.let {
      assertThat(it.message).isEqualTo("Transaction 'nonExistTransactionId' doesn't exist")
    }
  }

  @Test
  fun `SHOULD count sum of successful cashouts from IP on countCashoutSumWithSameIp`() {
    database.prepareUserCashoutTransaction(cashoutIp = "127.0.0.1", cashedOutAmountUsd = BigDecimal.ONE, status = SUCCESSFUL)
    database.prepareUserCashoutTransaction(cashoutIp = "127.0.0.1", cashedOutAmountUsd = BigDecimal.ONE, status = SUCCESSFUL)
    database.prepareUserCashoutTransaction(cashoutIp = "*********", cashedOutAmountUsd = BigDecimal.ONE, status = SUCCESSFUL)

    val result = runBlocking {
      service.countCashoutSumWithSameIp("127.0.0.1")
    }
    assertThat(result).isEqualByComparingTo(BigDecimal("2"))
  }

  @Test
  fun `SHOULD return zero as a result on countCashoutSumWithSameIp WHEN there were not successful cashouts from this IP`() {
    database.prepareUserCashoutTransaction(cashoutIp = "127.0.0.1", cashedOutAmountUsd = BigDecimal.ONE, status = FAILED)
    database.prepareUserCashoutTransaction(cashoutIp = "*********", cashedOutAmountUsd = BigDecimal.ONE, status = SUCCESSFUL)

    val result = runBlocking {
      service.countCashoutSumWithSameIp("127.0.0.1")
    }
    assertThat(result).isEqualByComparingTo(BigDecimal.ZERO)
  }

  @Test
  fun `SHOULD load payment providers with country filter only ON loadPaymentProvidersNonFiltered`() {
    val provider = cashoutProviders.first().copy(displayName = "someCashoutProvider")
    database.preparePaymentProvider(provider)
    database.preparePaymentProvider(provider, countryCode = "CA")
    database.preparePaymentProvider(provider, countryCode = "TZ")

    val result = runBlocking {
      service.loadPaymentProvidersNonFiltered("TZ")
    }

    assertThat(result).containsOnly(provider)
  }

  @Test
  fun `SHOULD return sum of successful cashout amounts by provider ON calculateTotalCashoutsByProviderForUser`() {
    val userId = database.prepareUser()
    database.preparePaymentProvider(cashoutProviders.first())
    database.prepareUserCashoutTransaction(providerType = AMAZON, userId = userId, cashedOutAmountUsd = BigDecimal("5.05"), status = SUCCESSFUL)
    database.prepareUserCashoutTransaction(providerType = AMAZON, userId = userId, cashedOutAmountUsd = BigDecimal("1.30"), status = SUCCESSFUL)
    database.prepareUserCashoutTransaction(providerType = AMAZON, userId = userId, cashedOutAmountUsd = BigDecimal("1.30"), status = PENDING)
    database.prepareUserCashoutTransaction(providerType = AMAZON, userId = userId, cashedOutAmountUsd = BigDecimal("1.30"), status = REJECTED)
    database.prepareUserCashoutTransaction(providerType = THE_HUNGER_PROJECT, userId = userId, cashedOutAmountUsd = BigDecimal("2.00"), status = SUCCESSFUL)
    database.prepareUserCashoutTransaction(providerType = THE_HUNGER_PROJECT, userId = userId, cashedOutAmountUsd = BigDecimal("2.00"), status = FAILED)

    val result = runBlocking {
      service.calculateTotalCashoutsByProviderForUser(userId)
    }

    assertThat(result).isEqualTo(
      mapOf(
        AMAZON to UserCurrencyEarnings(BigDecimal("7.65"), Currency.getInstance("USD"), BigDecimal("7.65")),
        THE_HUNGER_PROJECT to UserCurrencyEarnings(BigDecimal("2.00"), Currency.getInstance("USD"), BigDecimal("2.00"))
      )
    )
  }

  @Test
  fun `SHOULD return sum of successful cashout amounts by provider WHEN empty currency code ON calculateTotalCashoutsByProviderForUser`() {
    val userId = database.prepareUser()
    database.preparePaymentProvider(cashoutProviders.first())
    database.prepareUserCashoutTransaction(providerType = AMAZON, userId = userId, cashedOutAmountUsd = null, status = SUCCESSFUL, userCurrencyCode = null)
    database.prepareUserCashoutTransaction(providerType = AMAZON, userId = userId, cashedOutAmountUsd = BigDecimal("5.05"), status = SUCCESSFUL)
    val result = runBlocking {
      service.calculateTotalCashoutsByProviderForUser(userId)
    }

    assertThat(result).isEqualTo(
      mapOf(
        AMAZON to UserCurrencyEarnings(BigDecimal("5.05"), Currency.getInstance("USD"), BigDecimal("5.05")),
      )
    )

  }

  @Test
  fun `SHOULD check if user has successful cashout transaction on userHasSuccessfulCashout WHEN there is such transaction`() {
    val userId = database.prepareUser()
    database.prepareUserCashoutTransaction(userId = userId, status = SUCCESSFUL)

    val result = runBlocking {
      service.userHasSuccessfulCashout(userId)
    }

    assertThat(result).isTrue()
  }

  @Test
  fun `SHOULD check if user has successful cashout transaction on userHasSuccessfulCashout WHEN there is no such transaction`() {
    val userWithoutCashout = database.prepareUser()

    val result = runBlocking {
      service.userHasSuccessfulCashout(userWithoutCashout)
    }

    assertThat(result).isFalse()
  }

  @Test
  fun `SHOULD check if user has successful cashout transaction on userHasSuccessfulCashout WHEN there is a transaction but not successful one`() {
    val userId = database.prepareUser()
    database.prepareUserCashoutTransaction(userId = userId, status = FAILED)

    val result = runBlocking {
      service.userHasSuccessfulCashout(userId)
    }

    assertThat(result).isFalse()
  }

  @Test
  fun `SHOULD return all cashouts ON loadTransaction`() {
    val userId = database.prepareUser()
    val anotherUserId = database.prepareUser()
    val transactionId1 = database.prepareUserCashoutTransaction(userId = userId)
    val transactionId2 = database.prepareUserCashoutTransaction(userId = anotherUserId)
    val transactionId3 = database.prepareUserCashoutTransaction(userId = userId)

    runBlocking {
      assertThat(service.loadTransactions(userId).map { it.cashoutTransactionId })
        .containsOnly(transactionId1, transactionId3)
      assertThat(service.loadTransactions(anotherUserId).map { it.cashoutTransactionId })
        .containsOnly(transactionId2)
    }
  }

  @Test
  fun `SHOULD return recently tracked cashouts ON loadRecentTransactions`() {
    val now = Instant.now()

    val userId = database.prepareUser()
    val transactionId1 = database.prepareUserCashoutTransaction(
      userId = userId,
      createdAt = now.minus(1, MINUTES),
      encryptedEmail = cashoutTransactionStub.encryptedEmail,
      emailHash = cashoutTransactionStub.emailHash,
      cashoutIp = cashoutTransactionStub.userIp,
      cashedOutAmountUsd = cashoutTransactionStub.amountUsd,
      status = SUCCESSFUL,
      encryptedName = cashoutTransactionStub.encryptedUserName
    )
    val transactionId2 = database.prepareUserCashoutTransaction(
      userId = userId,
      createdAt = now.minus(30, MINUTES),
      encryptedEmail = cashoutTransactionStub.encryptedEmail,
      emailHash = cashoutTransactionStub.emailHash,
      cashoutIp = cashoutTransactionStub.userIp,
      cashedOutAmountUsd = cashoutTransactionStub.amountUsd,
      status = SUCCESSFUL,
      encryptedName = cashoutTransactionStub.encryptedUserName,
    )
    database.prepareUserCashoutTransaction(
      userId = userId,
      createdAt = now.minus(65, MINUTES)
    )

    val actual = runBlocking {
      service.loadRecentTransactions(userId, now.minus(35, MINUTES))
    }

    assertThat(actual).containsOnly(
      cashoutTransactionStub.copy(cashoutTransactionId = transactionId1, userId = userId),
      cashoutTransactionStub.copy(cashoutTransactionId = transactionId2, userId = userId)
    )
  }

  @Test
  fun `SHOULD return count of successful cashouts ON successfulUserCashouts`() {
    val userId = database.prepareUser()
    database.prepareUserCashoutTransaction(userId = userId, status = FAILED)
    database.prepareUserCashoutTransaction(userId = userId, status = SUCCESSFUL)
    database.prepareUserCashoutTransaction(userId = userId, status = REQUESTED)
    database.prepareUserCashoutTransaction(userId = userId, status = SUCCESSFUL)
    val userId2 = database.prepareUser()

    runBlocking {
      assertThat(service.successfulUserCashouts(userId)).isEqualTo(2)
      assertThat(service.successfulUserCashouts(userId2)).isEqualTo(0)
    }

  }

  @Test
  fun `SHOULD return an empty config ON getProviderImagesConfig WHEN there is no config for country and no config for US`() {
    transaction(database) {
      CfgCashoutStatusProvidersImagesTable.deleteAll()
    }
    val actual = runBlocking {
      service.getProviderImagesConfig("RU", true)
    }

    assertThat(actual).isEqualTo(ProviderImagesCfg.empty())
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD return US config ON getProviderImagesConfig WHEN there is no config for country`(isEnabled: Boolean) {
    transaction(database) {
      CfgCashoutStatusProvidersImagesTable.deleteAll()
      CfgCashoutStatusProvidersImagesTable.insert { it[countryCode] = "US"; it[cashoutEnabled] = true; it[imageFilename1] = "1.png" }
      CfgCashoutStatusProvidersImagesTable.insert { it[countryCode] = "US"; it[cashoutEnabled] = false; it[imageFilename1] = "2.png" }
    }

    val actual = runBlocking {
      service.getProviderImagesConfig("RU", isEnabled)
    }

    if (isEnabled)
      assertThat(actual).isEqualTo(ProviderImagesCfg.empty().copy(imageFileNames = listOf("1.png")))
    else
      assertThat(actual).isEqualTo(ProviderImagesCfg.empty().copy(imageFileNames = listOf("2.png")))
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD return a country config ON getProviderImagesConfig WHEN there is a config for country`(isEnabled: Boolean) {
    transaction(database) {
      CfgCashoutStatusProvidersImagesTable.deleteAll()
      CfgCashoutStatusProvidersImagesTable.insert { it[countryCode] = "US"; it[cashoutEnabled] = true; it[imageFilename1] = "1.png" }
      CfgCashoutStatusProvidersImagesTable.insert {
        it[countryCode] = "RU"
        it[cashoutEnabled] = true
        it[imageFilename2] = "2.png"
        it[imageFilename3] = "3.png"
        it[imageFilename1] = null
        it[imageFilename4] = "   "
      }
      CfgCashoutStatusProvidersImagesTable.insert {
        it[countryCode] = "RU"
        it[cashoutEnabled] = false
        it[imageFilename2] = "4.png"
        it[imageFilename3] = "5.png"
        it[imageFilename1] = null
        it[imageFilename4] = "   "
      }
      CfgCashoutStatusProvidersImagesTable.insert { it[countryCode] = "BR"; it[cashoutEnabled] = true; it[imageFilename2] = "6.png" }
    }

    val expectedT = ProviderImagesCfg.empty().copy("RU", listOf("2.png", "3.png"))
    val expectedF = ProviderImagesCfg.empty().copy("RU", listOf("4.png", "5.png"))
    val actual = runBlocking {
      service.getProviderImagesConfig("RU", isEnabled)
    }
    if (isEnabled) {
      assertThat(actual).isEqualTo(expectedT)
    } else {
      assertThat(actual).isEqualTo(expectedF)
    }

  }

  @Test
  fun `SHOULD obfuscate cashout transactions personal data ON obfuscateUserCashoutTransactionPersonals`() {
    val userId = database.prepareUser()
    val otherUserId = database.prepareUser()
    database.prepareUserCashoutTransaction(userId = userId)
    database.prepareUserCashoutTransaction(userId = userId)
    database.prepareUserCashoutTransaction(userId = otherUserId)

    val actual = runBlocking { service.obfuscateUserCashoutTransactionPersonals(userId) }

    assertThat(actual).isEqualTo(2)

    transaction(database) {
      UserCashoutTransactionsTable
        .select { UserCashoutTransactionsTable.userId eq userId }
        .forEach { row ->
          assertThat(row[UserCashoutTransactionsTable.encryptedName]).isEqualTo("personals deleted")
          assertThat(row[UserCashoutTransactionsTable.encryptedAddress]).isEqualTo("personals deleted")
          assertThat(row[UserCashoutTransactionsTable.encryptedEmail]).isEqualTo("personals deleted")
        }
    }
  }

  @Test
  fun `SHOULD load last user personals ON getLastUserPersonals`() {
    val userId = database.prepareUser()
    val userId2 = database.prepareUser()

    val expected = UserPersonals(
      "<EMAIL>",
      "User Full Name"
    )

    // newest
    database.prepareUserCashoutTransaction(
      userId = userId,
      encryptedEmail = expected.email,
      encryptedName = expected.userFullName,
      createdAt = now
    )

    (1L..10).forEach { i ->
      database.prepareUserCashoutTransaction(
        userId = userId,
        createdAt = now.minus(i, ChronoUnit.DAYS)
      )
    }

    //some other user
    database.prepareUserCashoutTransaction(
      userId = userId2,
      createdAt = now.plus(1, ChronoUnit.DAYS)
    )

    runBlocking { service.getLastUserPersonals(userId) }
      .also {
        assertThat(it).isEqualTo(expected)
      }
  }

  @Test
  fun `SHOULD load last user personals ON getLastUserPersonals WHEN email is empty`() {
    val userId = database.prepareUser()
    val userId2 = database.prepareUser()

    val expected = UserPersonals(
      "",
      "User Full Name"
    )

    // newest
    database.prepareUserCashoutTransaction(
      userId = userId,
      encryptedEmail = "",
      encryptedName = expected.userFullName,
      createdAt = now
    )

    (1L..10).forEach { i ->
      database.prepareUserCashoutTransaction(
        userId = userId,
        createdAt = now.minus(i, ChronoUnit.DAYS)
      )
    }

    //some other user
    database.prepareUserCashoutTransaction(
      userId = userId2,
      createdAt = now.plus(1, ChronoUnit.DAYS)
    )

    runBlocking { service.getLastUserPersonals(userId) }
      .also {
        assertThat(it).isEqualTo(expected)
      }
  }

  @Test
  fun `SHOULD return null ON getLastUserPersonals WHEN no cashouts`() {
    val userId = database.prepareUser()

    runBlocking { service.getLastUserPersonals(userId) }
      .also {
        assertThat(it).isNull()
      }
  }

  @Test
  fun `SHOULD save user with long email ON loadUserIdsForEmail WHEN user email match the request`() {
    runBlocking {
      val userId1 = database.prepareUser()
      val longEmail = randomAlphanumeric(240) + "@" + randomAlphabetic(10) + "." + randomAlphabetic(3)

      database.prepareUserCashoutTransaction(userId = userId1, encryptedEmail = longEmail, emailHash = "hash_loadUserIdsForEmail")

      val actual = service.loadUserIdsForEmails(setOf("hash_loadUserIdsForEmail"))

      assertThat(actual.size).isEqualTo(1)
      assertThat(actual).containsOnly(userId1 to "hash_loadUserIdsForEmail")
    }
  }

  @Test
  fun `SHOULD return return last earnings cashout date ON loadLastEarningsCashoutForUser`() {
    val userId = database.prepareUser()
    val userId2 = database.prepareUser()
    //fresh but failed
    database.prepareUserCashoutTransaction(userId = userId, status = FAILED, createdAt = now)
    //successful, but old
    database.prepareUserCashoutTransaction(userId = userId, status = SUCCESSFUL, createdAt = now.minus(5, MINUTES))
    //expected
    database.prepareUserCashoutTransaction(userId = userId, status = SUCCESSFUL, providerType = AMAZON, createdAt = now.minus(1, MINUTES))
    //fresh, but for another user
    database.prepareUserCashoutTransaction(userId = userId2, status = SUCCESSFUL, createdAt = now)

    runBlocking {
      assertThat(service.loadLastEarningsCashoutForUser(userId)).isEqualTo(now.minus(1, MINUTES))
    }
  }

  @Test
  fun `SHOULD return last successful transaction ON loadLastSuccessfulTransaction`() {
    val userId = database.prepareUser()
    val userId2 = database.prepareUser()
    //fresh but failed
    database.prepareUserCashoutTransaction(userId = userId, status = FAILED, createdAt = now)
    //successful, but old
    database.prepareUserCashoutTransaction(userId = userId, status = SUCCESSFUL, createdAt = now.minus(5, MINUTES))
    //expected
    database.prepareUserCashoutTransaction(userId = userId, status = SUCCESSFUL, providerType = AMAZON, createdAt = now.minus(1, MINUTES))
    //fresh, but for another user
    database.prepareUserCashoutTransaction(userId = userId2, status = SUCCESSFUL, createdAt = now)

    runBlocking {
      service.loadLastSuccessfulTransaction(userId).let {
        assertEquals(AMAZON, it?.provider)
        assertEquals("Encrypted Bart Apart", it?.encryptedAddress)
      }
    }
  }

  @Test
  fun `SHOULD return null transaction ON loadLastSuccessfulTransaction WHEN no successful transactions yet`() {
    val userId = database.prepareUser()
    database.prepareUserCashoutTransaction(userId = userId, status = FAILED, createdAt = now)

    runBlocking {
      assertNull(service.loadLastSuccessfulTransaction(userId))
    }
  }

}

fun Database.preparePaymentProvider(
  provider: CashoutProvider,
  countryCode: String = "US",
  enabled: Boolean = true,
  disabledUntil: Instant? = Instant.now().minusSeconds(2)
) {
  transaction(this) {
    PaymentProviderTable.insert {
      it[displayName] = provider.displayName
      it[url] = provider.url
      it[videoUrl] = provider.videoUrl
      it[iconFilename] = provider.iconFilename
      it[largeIconFilename] = provider.largeIconFilename
      it[smallIconFilename] = provider.smallIconFilename
      it[text] = provider.text
      it[shortText] = provider.shortText
      it[PaymentProviderTable.provider] = provider.providerType.key
      it[PaymentProviderTable.countryCode] = countryCode
      it[disclaimer] = provider.disclaimer
      it[emailHint] = provider.emailHint
      it[minimumAmount] = provider.minimumAmount
      it[maximumAmount] = provider.maximumAmount
      it[PaymentProviderTable.enabled] = enabled
      it[PaymentProviderTable.disabledUntil] = disabledUntil
      it[orderKey] = 0
      it[identifierType] = provider.identifierType?.key
      it[identifierHint] = provider.identifierHint
    }
  }
}

fun Database.prepareUserCashoutTransaction(
  id: String = UUID.randomUUID().toString(),
  userId: String = this.prepareUser(),
  encryptedEmail: String? = null,
  emailHash: String? = null,
  cashedOutAmountUsd: BigDecimal? = null,
  providerType: PaymentProviderType? = null,
  countryCode: String? = null,
  encryptedName: String? = null,
  status: UserCashoutTransactionsTable.Status? = null,
  processedAt: Instant? = null,
  createdAt: Instant? = null,
  updatedAt: Instant? = null,
  type: UserCashoutTransactionsTable.Type = UserCashoutTransactionsTable.Type.EARNINGS,
  cashoutIp: String = "***********",
  userCurrencyCode: String? = "USD",
  operationalWithholdAmountUsd: BigDecimal? = null,
): String {
  transaction(this) {
    UserCashoutTransactionsTable.insert {
      it[UserCashoutTransactionsTable.id] = id
      it[UserCashoutTransactionsTable.userId] = userId
      it[UserCashoutTransactionsTable.type] = type.key
      it[UserCashoutTransactionsTable.status] = status?.key ?: INITIAL.key
      it[provider] = providerType?.key ?: PAYPAL.key
      it[UserCashoutTransactionsTable.countryCode] = countryCode ?: "US"
      it[UserCashoutTransactionsTable.encryptedName] = encryptedName
      it[encryptedAddress] = "Encrypted Bart Apart"
      it[UserCashoutTransactionsTable.encryptedEmail] = encryptedEmail
      it[UserCashoutTransactionsTable.emailHash] = emailHash
      it[userIp] = cashoutIp
      it[UserCashoutTransactionsTable.cashedOutAmountUsd] = cashedOutAmountUsd
      it[UserCashoutTransactionsTable.userCurrencyCode] = userCurrencyCode
      it[userCurrencyAmount] = cashedOutAmountUsd
      if (processedAt != null) it[UserCashoutTransactionsTable.processedAt] = processedAt
      if (createdAt != null) it[UserCashoutTransactionsTable.createdAt] = createdAt
      if (updatedAt != null) it[UserCashoutTransactionsTable.updatedAt] = updatedAt
      it[UserCashoutTransactionsTable.operationalWithholdAmountUsd] = operationalWithholdAmountUsd
    }
  }
  return id
}
