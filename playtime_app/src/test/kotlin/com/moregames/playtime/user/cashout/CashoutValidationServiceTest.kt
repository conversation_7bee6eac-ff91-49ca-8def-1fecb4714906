package com.moregames.playtime.user.cashout

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isFalse
import assertk.assertions.isTrue
import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.app.BuildVariant.PRODUCTION
import com.moregames.base.app.PaymentProviderType.*
import com.moregames.base.app.UserIdentifierType
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.exceptions.ParameterRequiredException
import com.moregames.base.featureflags.FeatureFlagsFacade
import com.moregames.base.messaging.dto.SignatureProvider.Companion.withSignature
import com.moregames.base.table.UserCashoutTransactionsTable.Status
import com.moregames.base.util.SignatureVerificationResult
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.app.isCashoutDisabledForCtsProfileMatchFalse
import com.moregames.playtime.checks.ExaminationService
import com.moregames.playtime.earnings.dto.UserCurrencyEarnings
import com.moregames.playtime.general.MarketService
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.security.UserCryptoService
import com.moregames.playtime.user.EmailValidationService
import com.moregames.playtime.user.LimitedTrackingInfo
import com.moregames.playtime.user.UserHandleValidationService
import com.moregames.playtime.user.UserPersistenceService
import com.moregames.playtime.user.cashout.dto.CashoutDemandApiDto
import com.moregames.playtime.user.cashout.dto.CashoutProvider
import com.moregames.playtime.user.cashout.dto.EncryptedCashoutDemandDto
import com.moregames.playtime.user.cashout.dto.SimplifiedCashoutDto
import com.moregames.playtime.user.cashout.exception.*
import com.moregames.playtime.user.exception.AttestationRequiredException
import com.moregames.playtime.user.exception.IncorrectEmailException
import com.moregames.playtime.user.exception.IncorrectUserHandleException
import com.moregames.playtime.user.fraudscore.FraudScoreService
import com.moregames.playtime.utils.cashoutProvidersStub
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit.MINUTES
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith

class CashoutValidationServiceTest {
  private val cashoutTimeCalculationsService: CashoutTimeCalculationsService = mock()
  private val paymentProvidersService: PaymentProvidersService = mock()
  private val cashoutStatusService: CashoutStatusService = mock()
  private val cashoutPersistenceService: CashoutPersistenceService = mock()
  private val marketService: MarketService = mock()
  private val rewardingFacade: RewardingFacade = mock()
  private val userPersistenceService: UserPersistenceService = mock()
  private val examinationService: ExaminationService = mock()
  private val emailValidationService: EmailValidationService = mock()
  private val timeService: TimeService = mock()
  private val userCryptoService: UserCryptoService = mock()
  private val fraudScoreService: FraudScoreService = mock()
  private val abTestingService: AbTestingService = mock()
  private val userHandleValidationService: UserHandleValidationService = mock()
  private val featureFlagsFacade: FeatureFlagsFacade = mock()

  private val service = CashoutValidationService(
    buildVariant = PRODUCTION,
    paymentProvidersService = paymentProvidersService,
    cashoutStatusService = cashoutStatusService,
    cashoutPersistenceService = cashoutPersistenceService,
    marketService = marketService,
    rewardingFacade = rewardingFacade,
    userPersistenceService = userPersistenceService,
    examinationService = examinationService,
    emailValidationService = emailValidationService,
    timeService = timeService,
    userCryptoService = userCryptoService,
    fraudScoreService = fraudScoreService,
    abTestingService = abTestingService,
    userHandleValidationService = userHandleValidationService,
    featureFlagsFacade = featureFlagsFacade
  )

  private companion object {
    const val USER_ID = "userId"
    const val EMAIL = "<EMAIL>"
    const val ENCRYPTED_EMAIL = "<EMAIL>"
    const val EMAIL_HASH = "emailHash"
    const val NORMALIZED_EMAIL_HASH = "normalizedEmailHash"
    const val MAX_SUCCESS_CASHOUTS_PER_DAY_TEST = 20
    const val EXAMINATION_COOLDOWN_MINUTES_TEST = 10L
    val now: Instant = Instant.now()

    val provider = CashoutProvider(
      displayName = "Amazon",
      url = "https://www.amazon.com",
      videoUrl = "https://www.youtube.com",
      iconFilename = "icon",
      largeIconFilename = "largeIcon",
      smallIconFilename = "smallIcon",
      text = "text",
      providerType = AMAZON,
      disclaimer = "disclaimer",
      emailHint = "Amazon account",
      minimumAmount = BigDecimal("0.01"),
      maximumAmount = BigDecimal("2000"),
      orderKey = 0,
      identifierType = UserIdentifierType.EMAIL,
      identifierHint = "Amazon account"
    )

    val earningsCashoutDemand = CashoutDemandApiDto(
      provider = PAYPAL,
      name = "Max Mustermann",
      encryptedName = "encrypted name",
      address = "Musterweg 5",
      encryptedAddress = "encrypted address",
      email = EMAIL,
      encryptedEmail = ENCRYPTED_EMAIL,
      isBonusRequest = false,
    )

    val signedEarningsCashoutDemand = earningsCashoutDemand.withSignature(null)
  }

  @BeforeEach
  fun before() {
    userPersistenceService.mock({ isUserWhitelisted(USER_ID) }, false)
    marketService.mock({ getUserAllowedCountryCodeOrUS(USER_ID) }, "US")
    cashoutPersistenceService.mock({ loadRecentCashoutsByUserId(USER_ID) }, emptyList())
    cashoutPersistenceService.mock({ loadRecentCashoutsByEmail(EMAIL_HASH) }, emptyList())
    cashoutTimeCalculationsService.mock({ alreadyCashedOutToday(USER_ID) }, false)
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)
    userPersistenceService.mock({ getUserConsentInfo(USER_ID) }, LimitedTrackingInfo(false, "US", false))
    fraudScoreService.mock({ isUserBlocked(USER_ID) }, false)
    marketService.mock({ isUserFromAllowedCountry(USER_ID) }, true)
    marketService.mock({ isGdprAppliesToCountry(any()) }, false)
    emailValidationService.mock({ isEmailValid(ENCRYPTED_EMAIL) }, true)
    userCryptoService.mock({ verifySignedMessage(any(), any()) }, SignatureVerificationResult.valid())
    paymentProvidersService.mock(
      { loadPaymentProvider(any(), any(), eq(USER_ID), any(), any()) },
      cashoutProvidersStub.first().copy(minimumAmount = null, maximumAmount = null)
    )
    timeService.mock({ now() }, now)
    abTestingService.mock({ shouldEnableVenmo(USER_ID) }, false)
  }

  @Test
  fun `SHOULD throw DisabledPaymentProviderException ON validateCashoutDemandRequest WHEN payment provider is disabled AND earnings are requested`() {
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      UserCurrencyEarnings(BigDecimal("5.00"), Currency.getInstance("USD"), BigDecimal("5.00"))
    )
    paymentProvidersService.mock(
      { loadPaymentProvider(earningsCashoutDemand.provider, "US", USER_ID, BigDecimal("5.00"), Locale.ENGLISH) },
      provider.copy(enabled = false)
    )

    assertFailsWith(DisabledPaymentProviderException::class) {
      runBlocking {
        service.validateCashoutDemandRequest(
          USER_ID,
          EncryptedCashoutDemandDto.fromApiDto(signedEarningsCashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH),
          ANDROID
        )
      }
    }.let {
      assertEquals("User userId can't cashout using provider paypal", it.internalMessage)
      assertEquals("Invalid payment provider", it.externalMessage)
    }
  }

  @Test
  fun `SHOULD throw CashoutThrottledException ON validateCashoutDemandRequest WHEN cashout is throttled`() {
    cashoutPersistenceService.mock(
      { loadRecentCashoutsByUserId(USER_ID) },
      List(MAX_SUCCESS_CASHOUTS_PER_DAY_TEST + 1) { idx -> SimplifiedCashoutDto(idx.toString(), Status.SUCCESSFUL, AMAZON) })

    assertThrows<CashoutThrottledException> {
      runBlocking {
        service.validateCashoutDemandRequest(
          USER_ID,
          EncryptedCashoutDemandDto.fromApiDto(signedEarningsCashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH),
          ANDROID
        )
      }
    }.let {
      assertThat(it.internalMessage).isEqualTo("User userId with email hash $EMAIL_HASH can't cashout throttled")
      assertEquals("Something went wrong, please try again or contact the support", it.externalMessage)
    }
  }

  @Test
  fun `SHOULD throw duplicate cashout exception ON validateCashoutDemandRequest WHEN user already cashed out in last period`() {
    cashoutTimeCalculationsService.mock({ alreadyCashedOutToday(USER_ID) }, true)
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, false)

    assertThrows<DuplicateCashoutException> {
      runBlocking {
        service.validateCashoutDemandRequest(
          USER_ID,
          EncryptedCashoutDemandDto.fromApiDto(signedEarningsCashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH),
          ANDROID
        )
      }
    }.let {
      assertEquals("Duplicate cashout request within a single cashout period for user userId", it.internalMessage)
      assertEquals("Something went wrong, please try again or contact the support", it.externalMessage)
    }
  }

  @Test
  fun `SHOULD throw duplicate cashout exception ON validateCashoutDemandRequest WHEN user is on ICP and cashout is disabled`() {
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, false)

    assertThrows<DuplicateCashoutException> {
      runBlocking {
        service.validateCashoutDemandRequest(
          USER_ID,
          EncryptedCashoutDemandDto.fromApiDto(signedEarningsCashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH),
          ANDROID
        )
      }
    }.let {
      assertEquals("Duplicate cashout request within a single cashout period for user userId", it.internalMessage)
      assertEquals("Something went wrong, please try again or contact the support", it.externalMessage)
    }
  }

  @Test
  fun `SHOULD NOT throw duplicate cashout exception ON validateCashoutDemandRequest WHEN user already cashed out in last period AND user is ICP`() {
    cashoutTimeCalculationsService.mock({ alreadyCashedOutToday(USER_ID) }, true)
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      UserCurrencyEarnings(BigDecimal("5.00"), Currency.getInstance("USD"), BigDecimal("5.00"))
    )
    paymentProvidersService.mock({ loadPaymentProvider(any(), any(), eq(USER_ID), any(), any()) }, provider)
    examinationService.mock({ wasSuccessfullyExamined(USER_ID) }, true)

    runBlocking {
      service.validateCashoutDemandRequest(
        USER_ID,
        EncryptedCashoutDemandDto.fromApiDto(signedEarningsCashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH),
        ANDROID
      )
    }
  }

  @Test
  fun `SHOULD return false ON isCashoutThrottled WHEN no metric hit the threshold`() {
    val nonThrottledList = listOf(
      SimplifiedCashoutDto("1", Status.SUCCESSFUL, AMAZON),
      SimplifiedCashoutDto("2", Status.SUCCESSFUL, AMAZON),
      SimplifiedCashoutDto("3", Status.SUCCESSFUL, AMAZON),
      SimplifiedCashoutDto("4", Status.SUCCESSFUL, AMAZON),
      SimplifiedCashoutDto("5", Status.FAILED, AMAZON),
      SimplifiedCashoutDto("6", Status.FAILED, AMAZON),
      SimplifiedCashoutDto("7", Status.FAILED, AMAZON),
      SimplifiedCashoutDto("8", Status.FAILED, AMAZON),
      SimplifiedCashoutDto("9", Status.FAILED, PAYPAL),
      SimplifiedCashoutDto("10", Status.FAILED, PAYPAL),
      SimplifiedCashoutDto("11", Status.FAILED, PAYPAL),
      SimplifiedCashoutDto("12", Status.FAILED, PAYPAL),
      SimplifiedCashoutDto("13", Status.FAILED, WALMART),
      SimplifiedCashoutDto("14", Status.FAILED, WALMART),
      SimplifiedCashoutDto("15", Status.FAILED, WALMART),
      SimplifiedCashoutDto("16", Status.FAILED, WALMART),
      SimplifiedCashoutDto("17", Status.FAILED, BEST_BUY),
      SimplifiedCashoutDto("18", Status.FAILED, BEST_BUY),
      SimplifiedCashoutDto("19", Status.FAILED, BEST_BUY),
      SimplifiedCashoutDto("20", Status.FAILED, BEST_BUY),
      SimplifiedCashoutDto("21", Status.FAILED, GOOGLE_PLAY),
      SimplifiedCashoutDto("22", Status.FAILED, GOOGLE_PLAY),
      SimplifiedCashoutDto("23", Status.FAILED, GOOGLE_PLAY)
    )
    cashoutPersistenceService.mock({ loadRecentCashoutsByUserId(USER_ID) }, nonThrottledList)

    val actual = runBlocking {
      service.isCashoutThrottled(USER_ID)
    }
    assertThat(actual).isFalse()

    val emailHash = EMAIL_HASH
    cashoutPersistenceService.mock({ loadRecentCashoutsByUserId(USER_ID) }, emptyList())
    cashoutPersistenceService.mock({ loadRecentCashoutsByEmail(emailHash) }, nonThrottledList)

    val actual2 = runBlocking {
      service.isCashoutThrottled(USER_ID, emailHash)
    }
    assertThat(actual2).isFalse()
  }

  @Test
  fun `SHOULD return true ON isCashoutThrottled WHEN recent successful transactions count hit the threshold`() {
    val highRateOfSuccessfulTransactions = listOf(
      SimplifiedCashoutDto("1", Status.SUCCESSFUL, AMAZON),
      SimplifiedCashoutDto("2", Status.SUCCESSFUL, WALMART),
      SimplifiedCashoutDto("3", Status.SUCCESSFUL, BEST_BUY),
      SimplifiedCashoutDto("4", Status.SUCCESSFUL, GOOGLE_PLAY),
      SimplifiedCashoutDto("5", Status.SUCCESSFUL, BURGER_KING)
    )
    cashoutPersistenceService.mock({ loadRecentCashoutsByUserId(USER_ID) }, highRateOfSuccessfulTransactions)

    val actual = runBlocking {
      service.isCashoutThrottled(USER_ID, null)
    }
    assertThat(actual).isTrue()

    val emailHash = EMAIL_HASH
    cashoutPersistenceService.mock({ loadRecentCashoutsByUserId(USER_ID) }, emptyList())
    cashoutPersistenceService.mock({ loadRecentCashoutsByEmail(emailHash) }, highRateOfSuccessfulTransactions)

    val actual2 = runBlocking {
      service.isCashoutThrottled(USER_ID, emailHash)
    }
    assertThat(actual2).isTrue()
  }

  @Test
  fun `SHOULD return false ON isCashoutThrottled WHEN recent successful transactions count hit the threshold AND user is whitelisted`() {
    userPersistenceService.mock({ isUserWhitelisted(any()) }, true)
    val highRateOfSuccessfulTransactions = listOf(
      SimplifiedCashoutDto("1", Status.SUCCESSFUL, AMAZON),
      SimplifiedCashoutDto("2", Status.SUCCESSFUL, WALMART),
      SimplifiedCashoutDto("3", Status.SUCCESSFUL, BEST_BUY),
      SimplifiedCashoutDto("4", Status.SUCCESSFUL, GOOGLE_PLAY),
      SimplifiedCashoutDto("5", Status.SUCCESSFUL, BURGER_KING)
    )
    cashoutPersistenceService.mock({ loadRecentCashoutsByUserId(USER_ID) }, highRateOfSuccessfulTransactions)

    val actual = runBlocking {
      service.isCashoutThrottled(USER_ID, null)
    }
    assertThat(actual).isFalse()
  }

  @Test
  fun `SHOULD return true ON isCashoutThrottled WHEN recent failed transactions count hit the threshold`() {
    val highRateOfSuccessfulTransactions = listOf(
      SimplifiedCashoutDto("1", Status.FAILED, BURGER_KING),
      SimplifiedCashoutDto("2", Status.FAILED, BURGER_KING),
      SimplifiedCashoutDto("3", Status.FAILED, BURGER_KING),
      SimplifiedCashoutDto("4", Status.FAILED, BURGER_KING),
      SimplifiedCashoutDto("5", Status.FAILED, AMAZON),
      SimplifiedCashoutDto("6", Status.FAILED, AMAZON),
      SimplifiedCashoutDto("7", Status.FAILED, AMAZON),
      SimplifiedCashoutDto("8", Status.FAILED, AMAZON),
      SimplifiedCashoutDto("9", Status.FAILED, PAYPAL),
      SimplifiedCashoutDto("10", Status.FAILED, PAYPAL),
      SimplifiedCashoutDto("11", Status.FAILED, PAYPAL),
      SimplifiedCashoutDto("12", Status.FAILED, PAYPAL),
      SimplifiedCashoutDto("13", Status.FAILED, WALMART),
      SimplifiedCashoutDto("14", Status.FAILED, WALMART),
      SimplifiedCashoutDto("15", Status.FAILED, WALMART),
      SimplifiedCashoutDto("16", Status.FAILED, WALMART),
      SimplifiedCashoutDto("17", Status.FAILED, BEST_BUY),
      SimplifiedCashoutDto("18", Status.FAILED, BEST_BUY),
      SimplifiedCashoutDto("19", Status.FAILED, BEST_BUY),
      SimplifiedCashoutDto("20", Status.FAILED, BEST_BUY)
    )
    cashoutPersistenceService.mock({ loadRecentCashoutsByUserId(USER_ID) }, highRateOfSuccessfulTransactions)

    val actual = runBlocking {
      service.isCashoutThrottled(USER_ID, null)
    }
    assertThat(actual).isTrue()

    val emailHash = EMAIL_HASH
    cashoutPersistenceService.mock({ loadRecentCashoutsByUserId(USER_ID) }, emptyList())
    cashoutPersistenceService.mock({ loadRecentCashoutsByEmail(emailHash) }, highRateOfSuccessfulTransactions)

    val actual2 = runBlocking {
      service.isCashoutThrottled(USER_ID, emailHash)
    }
    assertThat(actual2).isTrue()
  }

  @Test
  fun `SHOULD return true ON isCashoutThrottled WHEN recent failed transactions count hit the threshold for particular provider`() {
    val highRateOfSuccessfulTransactions = listOf(
      SimplifiedCashoutDto("1", Status.FAILED, AMAZON),
      SimplifiedCashoutDto("2", Status.FAILED, AMAZON),
      SimplifiedCashoutDto("3", Status.FAILED, AMAZON),
      SimplifiedCashoutDto("4", Status.FAILED, AMAZON),
      SimplifiedCashoutDto("5", Status.FAILED, AMAZON)
    )
    cashoutPersistenceService.mock({ loadRecentCashoutsByUserId(USER_ID) }, highRateOfSuccessfulTransactions)

    val actual = runBlocking {
      service.isCashoutThrottled(USER_ID, null)
    }
    assertThat(actual).isTrue()

    val emailHash = EMAIL_HASH
    cashoutPersistenceService.mock({ loadRecentCashoutsByUserId(USER_ID) }, emptyList())
    cashoutPersistenceService.mock({ loadRecentCashoutsByEmail(emailHash) }, highRateOfSuccessfulTransactions)

    val actual2 = runBlocking {
      service.isCashoutThrottled(USER_ID, emailHash)
    }
    assertThat(actual2).isTrue()
  }

  @Test
  fun `SHOULD call demand cashout ON validateCashoutDemandRequest WHEN attestation already passed`() {
    examinationService.mock({ wasSuccessfullyExamined(USER_ID) }, true)

    // no exceptions
    runBlocking {
      service.validateCashoutDemandRequest(
        USER_ID,
        EncryptedCashoutDemandDto.fromApiDto(signedEarningsCashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH),
        ANDROID
      )
    }

  }


  @Test
  fun `SHOULD throw AttestationRequiredException ON validateCashoutDemandRequest WHEN attestation was not done before`() {
    examinationService.mock({ wasSuccessfullyExamined(USER_ID) }, false)
    examinationService.mock({ getRecentExaminationNonceCreationTime(USER_ID) }, null)

    assertFailsWith(AttestationRequiredException::class) {
      runBlocking {
        service.validateCashoutDemandRequest(
          USER_ID,
          EncryptedCashoutDemandDto.fromApiDto(signedEarningsCashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH),
          ANDROID
        )
      }
    }.let {
      assertEquals("Attestation required for 'userId'", it.internalMessage)
      assertEquals("Something went wrong, please try again or contact the support", it.externalMessage)
    }
  }

  @Test
  fun `SHOULD NOT throw AttestationRequiredException ON validateCashoutDemandRequest WHEN attestation was not done before AND app is on IOS platform`() {
    examinationService.mock({ wasSuccessfullyExamined(USER_ID) }, false)
    examinationService.mock({ getRecentExaminationNonceCreationTime(USER_ID) }, null)

    // no exceptions
    runBlocking {
      service.validateCashoutDemandRequest(
        USER_ID,
        EncryptedCashoutDemandDto.fromApiDto(signedEarningsCashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH),
        IOS
      )
    }
  }

  @Test
  fun `SHOULD throw AttestationRequiredException ON validateCashoutDemandRequest WHEN attestation was not done before AND previous attempt was long ago`() {
    examinationService.mock({ wasSuccessfullyExamined(USER_ID) }, false)
    examinationService.mock({ getRecentExaminationNonceCreationTime(USER_ID) }, now.minus(EXAMINATION_COOLDOWN_MINUTES_TEST * 2, MINUTES))

    assertFailsWith(AttestationRequiredException::class) {
      runBlocking {
        service.validateCashoutDemandRequest(
          USER_ID,
          EncryptedCashoutDemandDto.fromApiDto(signedEarningsCashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH),
          ANDROID
        )
      }
    }.let {
      assertEquals("Attestation required for 'userId'", it.internalMessage)
      assertEquals("Something went wrong, please try again or contact the support", it.externalMessage)
    }
  }

  @Test
  fun `SHOULD throw ExaminationThrottlingException ON validateCashoutDemandRequest WHEN attestation was not done before AND previous attempt was done recently`() {
    examinationService.mock({ wasSuccessfullyExamined(USER_ID) }, false)
    examinationService.mock({ getRecentExaminationNonceCreationTime(USER_ID) }, now.minus(EXAMINATION_COOLDOWN_MINUTES_TEST / 2, MINUTES))

    assertFailsWith(ExaminationThrottlingException::class) {
      runBlocking {
        service.validateCashoutDemandRequest(
          USER_ID,
          EncryptedCashoutDemandDto.fromApiDto(signedEarningsCashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH),
          ANDROID
        )
      }
    }.let {
      assertEquals("Examination throttling for 'userId'", it.internalMessage)
      assertThat(it.externalMessage).isEqualTo("Something went wrong with your cash-out request. Please try again in 10 minutes.")
    }
  }

  @Test
  fun `SHOULD skip examination check ON validateCashoutDemandRequest WHEN app is on IOS platform`() {
    examinationService.mock({ wasSuccessfullyExamined(USER_ID) }, false)
    examinationService.mock({ getRecentExaminationNonceCreationTime(USER_ID) }, now.minus(EXAMINATION_COOLDOWN_MINUTES_TEST / 2, MINUTES))

    // no exceptions
    runBlocking {
      service.validateCashoutDemandRequest(
        USER_ID,
        EncryptedCashoutDemandDto.fromApiDto(signedEarningsCashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH),
        IOS
      )
    }
  }

  @Test
  fun `SHOULD validate email ON validateCashoutDemandRequest WHEN version is new enough`() {
    examinationService.mock({ wasSuccessfullyExamined(USER_ID) }, true)

    // no exceptions
    runBlocking {
      service.validateCashoutDemandRequest(
        userId = USER_ID,
        request = EncryptedCashoutDemandDto.fromApiDto(signedEarningsCashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH),
        ANDROID,
      )
    }
    verifyBlocking(emailValidationService) { isEmailValid(earningsCashoutDemand.encryptedEmail!!) }
  }

  @Test
  fun `SHOULD throw exception ON validateCashoutDemandRequest WHEN version is new enough AND email is invalid`() {
    emailValidationService.mock({ isEmailValid(ENCRYPTED_EMAIL) }, false)

    assertFailsWith(IncorrectEmailException::class) {
      runBlocking {
        service.validateCashoutDemandRequest(
          USER_ID,
          EncryptedCashoutDemandDto.fromApiDto(signedEarningsCashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH),
          ANDROID
        )
      }
    }.let {
      assertEquals("Encrypted email '<EMAIL>' is not correct, userId: userId", it.internalMessage)
      assertEquals("Something went wrong, please try again or contact the support", it.externalMessage)
    }
  }

  @Test
  fun `SHOULD throw exception ON validateCashoutDemandRequest WHEN email is invalid AND platform is iOS`() {
    emailValidationService.mock({ isEmailValid(ENCRYPTED_EMAIL) }, false)

    assertFailsWith(IncorrectEmailException::class) {
      runBlocking {
        service.validateCashoutDemandRequest(
          USER_ID,
          EncryptedCashoutDemandDto.fromApiDto(signedEarningsCashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH),
          IOS
        )
      }
    }.let {
      assertEquals("Encrypted email '<EMAIL>' is not correct, userId: userId", it.internalMessage)
      assertEquals("Something went wrong, please try again or contact the support", it.externalMessage)
    }
  }

  @ParameterizedTest
  @ValueSource(booleans = [true, false])
  fun `SHOULD throw ParameterRequiredException ON validateCashoutDemandRequest WHEN encrypted address is null or empty`(empty: Boolean) {
    assertFailsWith(ParameterRequiredException::class) {
      runBlocking {
        service.validateCashoutDemandRequest(
          USER_ID,
          EncryptedCashoutDemandDto.fromApiDto(
            earningsCashoutDemand.copy(encryptedAddress = if (empty) "" else null).withSignature(null).payload,
            EMAIL_HASH,
            NORMALIZED_EMAIL_HASH
          ),
          ANDROID
        )
      }
    }.let {
      assertEquals("Required parameter 'encryptedAddress' not found, userId: userId", it.internalMessage)
      assertEquals("Something went wrong, please try again or contact the support", it.externalMessage)
    }
  }

  @Test
  fun `SHOULD throw NoBonusToCashoutException ON validateCashoutDemandRequest WHEN bonus is requested`() {
    assertThrows<NoBonusToCashoutException> {
      runBlocking {
        service.validateCashoutDemandRequest(
          USER_ID,
          EncryptedCashoutDemandDto.fromApiDto(
            earningsCashoutDemand.copy(isBonusRequest = true).withSignature(null).payload,
            EMAIL_HASH,
            NORMALIZED_EMAIL_HASH
          ),
          ANDROID
        )
      }
    }.let {
      assertEquals("No bonus available to cashout for userId 'userId'", it.internalMessage)
      assertEquals("Something went wrong, please try again or contact the support", it.externalMessage)
    }
  }

  @Test
  fun `SHOULD call signature verification ON validateRequestSignature`() {
    runBlocking {
      service.validateRequestSignature(USER_ID, signedEarningsCashoutDemand)
    }

    verifyBlocking(userCryptoService) { verifySignedMessage(signedEarningsCashoutDemand, USER_ID) }
  }

  @Test
  fun `SHOULD allow cashout ON isCashoutAllowed WHEN user is whitelisted AND have all bad conditions`() {
    userPersistenceService.mock({ isUserWhitelisted(USER_ID) }, true)
    fraudScoreService.mock({ isUserBlocked(USER_ID) }, true)
    marketService.mock({ isUserFromAllowedCountry(USER_ID) }, false)

    val result = runBlocking {
      service.isCashoutAllowed(USER_ID)
    }

    assertThat(result).isTrue()
  }

  @Test
  fun `SHOULD disallow cashout ON isCashoutAllowed WHEN user has high fraud score`() {
    fraudScoreService.mock({ isUserBlocked(USER_ID) }, true)

    val result = runBlocking {
      service.isCashoutAllowed(USER_ID)
    }

    assertThat(result).isFalse()
  }

  @Test
  fun `SHOULD disallow cashout ON isCashoutAllowed WHEN user is not from allowed country`() {
    marketService.mock({ isUserFromAllowedCountry(USER_ID) }, false)

    val result = runBlocking {
      service.isCashoutAllowed(USER_ID)
    }

    assertThat(result).isFalse()
  }

  @Test
  fun `SHOULD throw CashoutDemandDisallowedException ON validateCashoutDemandRequest WHEN cashout is not allowed`() {
    fraudScoreService.mock({ isUserBlocked(USER_ID) }, true)

    assertFailsWith(CashoutDemandDisallowedException::class) {
      runBlocking {
        service.validateCashoutDemandRequest(
          USER_ID,
          EncryptedCashoutDemandDto.fromApiDto(signedEarningsCashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH),
          ANDROID
        )
      }
    }.let {
      assertEquals("Cashout demand is not allowed for userId 'userId'", it.internalMessage)
      assertEquals("Something went wrong, please try again or contact the support", it.externalMessage)
    }
  }

  @Test
  fun `SHOULD throw CashoutDemandRequireGdprConsentException ON checkIfCashoutAllowed WHEN user is part of gdpr country AND user is not consented`() {
    userPersistenceService.mock({ getUserConsentInfo(USER_ID) }, LimitedTrackingInfo(false, "GB", false))
    marketService.mock({ isGdprAppliesToCountry("GB") }, true)
    assertFailsWith(CashoutDemandRequireGdprConsentException::class) {
      runBlocking {
        service.validateCashoutDemandRequest(
          USER_ID,
          EncryptedCashoutDemandDto.fromApiDto(signedEarningsCashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH),
          ANDROID
        )
      }
    }.let {
      assertEquals("Cashout demand requires gdpr consent for userId 'userId'", it.internalMessage)
      assertEquals("Something went wrong, please try again or contact the support", it.externalMessage)
    }
  }

  @Test
  fun `SHOULD not throw CashoutDemandRequireGdprConsentException ON checkIfCashoutAllowed WHEN iOS user`() {
    userPersistenceService.mock({ getUserConsentInfo(USER_ID) }, LimitedTrackingInfo(false, "GB", false))
    marketService.mock({ isGdprAppliesToCountry("GB") }, true)

    runBlocking {
      service.validateCashoutDemandRequest(
        USER_ID,
        EncryptedCashoutDemandDto.fromApiDto(signedEarningsCashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH),
        IOS
      )
    }
  }

  @ParameterizedTest
  @ValueSource(strings = ["0", "1", "9.9", "10"])
  fun `SHOULD skip email usage validation ON validateCashoutDemandRequest WHEN cashout amount leq 10`(cashoutAmount: BigDecimal) {
    examinationService.mock({ wasSuccessfullyExamined(USER_ID) }, true)
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      UserCurrencyEarnings(cashoutAmount, Currency.getInstance("CAD"), cashoutAmount.multiply(BigDecimal("2")))
    )

    runBlocking {
      service.validateCashoutDemandRequest(
        USER_ID,
        EncryptedCashoutDemandDto.fromApiDto(signedEarningsCashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH),
        ANDROID
      )
    }

    verifyBlocking(fraudScoreService) { isUserBlocked(USER_ID) }
    verifyNoMoreInteractions(fraudScoreService)
  }

  @Test
  fun `SHOULD make email usage validation ON validateCashoutDemandRequest WHEN cashout gt 10`() {
    val cashoutAmount = BigDecimal("10.1")

    examinationService.mock({ wasSuccessfullyExamined(USER_ID) }, true)
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      UserCurrencyEarnings(cashoutAmount, Currency.getInstance("CAD"), cashoutAmount.multiply(BigDecimal("2")))
    )

    runBlocking {
      service.validateCashoutDemandRequest(
        USER_ID,
        EncryptedCashoutDemandDto.fromApiDto(signedEarningsCashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH),
        ANDROID
      )
    }

    verifyBlocking(fraudScoreService) { checkEmailIsNotOverusedOrRestrict(USER_ID, EMAIL_HASH, ANDROID) }
  }

  @Test
  fun `SHOULD skip email checks if user is ON enableVenmo experiment and email null`() {
    val cashoutAmount = BigDecimal("10.1")
    examinationService.mock({ wasSuccessfullyExamined(USER_ID) }, true)
    userHandleValidationService.mock({ isUserHandleValid("venmoUserHandle") }, true)
    abTestingService.mock({ shouldEnableVenmo(USER_ID) }, true)
    rewardingFacade.mock(
      { loadUnpaidUserCurrencyEarnings(USER_ID) },
      UserCurrencyEarnings(cashoutAmount, Currency.getInstance("CAD"), cashoutAmount.multiply(BigDecimal("2")))
    )

    val cashoutDemand = earningsCashoutDemand.copy(email = null, encryptedEmail = "", userHandle = "venmoUserHandle", provider = VENMO).withSignature(null)
    runBlocking {
      service.validateCashoutDemandRequest(USER_ID, EncryptedCashoutDemandDto.fromApiDto(cashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH), ANDROID)
    }
    verifyNoInteractions(emailValidationService)
    verifyBlocking(fraudScoreService, times(0)) { checkEmailIsNotOverusedOrRestrict(any(), any(), any()) }
  }

  @Test
  fun `SHOULD validate email ON validateCashoutDemandRequest WHEN venmo and email present`() {
    abTestingService.mock({ shouldEnableVenmo(USER_ID) }, true)
    emailValidationService.mock({ isEmailValid("userHandle") }, false)
    assertFailsWith(IncorrectEmailException::class) {
      runBlocking {
        service.validateCashoutDemandRequest(
          userId = USER_ID,
          request = EncryptedCashoutDemandDto.fromApiDto(
            earningsCashoutDemand.copy(encryptedEmail = "userHandle").withSignature(null).payload,
            EMAIL_HASH,
            NORMALIZED_EMAIL_HASH
          ),
          appPlatform = ANDROID,
        )
      }
    }.let {
      assertEquals("Encrypted email 'userHandle' is not correct, userId: userId", it.internalMessage)
      assertEquals("Something went wrong, please try again or contact the support", it.externalMessage)
    }
    verifyBlocking(emailValidationService, times(1)) { isEmailValid("userHandle") }
  }

  @Test
  fun `SHOULD throw exception ON validateCashoutDemandRequest WHEN email is empty and user is not on enableVenmo experiment`() {
    emailValidationService.mock({ isEmailValid("") }, false)

    val cashoutDemand = earningsCashoutDemand.copy(email = null, encryptedEmail = null, userHandle = "venmoUserHandle").withSignature(null)
    assertFailsWith(IncorrectEmailException::class) {
      runBlocking {
        service.validateCashoutDemandRequest(USER_ID, EncryptedCashoutDemandDto.fromApiDto(cashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH), ANDROID)
      }
    }.let {
      assertEquals("Encrypted email '' is not correct, userId: userId", it.internalMessage)
      assertEquals("Something went wrong, please try again or contact the support", it.externalMessage)
    }
  }

  @Test
  fun `SHOULD throw exception ON validateCashoutDemandRequest WHEN user is on enableVenmo experiment and handle is incorrect`() {
    abTestingService.mock({ shouldEnableVenmo(USER_ID) }, true)
    userHandleValidationService.mock({ isUserHandleValid("") }, false)
    val cashoutDemand = earningsCashoutDemand.copy(email = null, userHandle = "", provider = VENMO).withSignature(null)

    assertFailsWith(IncorrectUserHandleException::class) {
      runBlocking {
        service.validateCashoutDemandRequest(USER_ID, EncryptedCashoutDemandDto.fromApiDto(cashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH), ANDROID)
      }
    }.let {
      assertEquals("User handle '' is not correct, userId: userId", it.internalMessage)
      assertEquals("Something went wrong, please try again or contact the support", it.externalMessage)
    }
  }

  @Test
  fun `SHOULD throw exception ON validateCashoutDemandRequest WHEN venmo disabled`() {
    abTestingService.mock({ shouldEnableVenmo(USER_ID) }, false)
    val cashoutDemand = earningsCashoutDemand.copy(email = null, userHandle = "", provider = VENMO).withSignature(null)

    assertFailsWith(DisabledPaymentProviderException::class) {
      runBlocking {
        service.validateCashoutDemandRequest(USER_ID, EncryptedCashoutDemandDto.fromApiDto(cashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH), ANDROID)
      }
    }.let {
      assertEquals("User userId can't cashout using provider venmo", it.internalMessage)
      assertEquals("Invalid payment provider", it.externalMessage)
    }
  }

  @Test
  fun `SHOULD throw CashoutThrottledException ON validateCashoutDemandRequest WHEN user is on enableVenmo and cashout is throttled`() {
    abTestingService.mock({ shouldEnableVenmo(USER_ID) }, true)
    userHandleValidationService.mock({ isUserHandleValid("enableVenmo") }, true)
    cashoutPersistenceService.mock(
      { loadRecentCashoutsByUserId(USER_ID) }, List(6) { i ->
        SimplifiedCashoutDto((i + 1).toString(), Status.FAILED, VENMO)
      }
    )
    val cashoutDemand = earningsCashoutDemand.copy(email = null, userHandle = "enableVenmo", provider = VENMO).withSignature(null)
    assertFailsWith(CashoutThrottledException::class) {
      runBlocking {
        service.validateCashoutDemandRequest(USER_ID, EncryptedCashoutDemandDto.fromApiDto(cashoutDemand.payload, EMAIL_HASH, NORMALIZED_EMAIL_HASH), ANDROID)
      }
    }
  }

  @Test
  fun `SHOULD not allow cashout WHEN cts_profile_match is false AND ff is enabled`() {
    featureFlagsFacade.mock({ isCashoutDisabledForCtsProfileMatchFalse() }, true)
    examinationService.mock({ getCtsProfileMatchStatus(USER_ID) }, false)

    val result = runBlocking {
      service.isCashoutAllowed(USER_ID)
    }

    assertThat(result).isFalse()
  }

  @Test
  fun `SHOULD allow cashout WHEN cts_profile_match is true AND ff is enabled`() {
    featureFlagsFacade.mock({ isCashoutDisabledForCtsProfileMatchFalse() }, true)
    examinationService.mock({ getCtsProfileMatchStatus(USER_ID) }, true)

    val result = runBlocking {
      service.isCashoutAllowed(USER_ID)
    }

    assertThat(result).isTrue()
  }

  @Test
  fun `SHOULD allow cashout WHEN cts_profile_match is false AND ff is disabled`() {
    featureFlagsFacade.mock({ isCashoutDisabledForCtsProfileMatchFalse() }, false)
    examinationService.mock({ getCtsProfileMatchStatus(USER_ID) }, true)

    val result = runBlocking {
      service.isCashoutAllowed(USER_ID)
    }

    assertThat(result).isTrue()
  }
}
