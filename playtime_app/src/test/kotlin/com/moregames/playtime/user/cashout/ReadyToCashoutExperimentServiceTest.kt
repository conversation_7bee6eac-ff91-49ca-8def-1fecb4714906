package com.moregames.playtime.user.cashout

import com.moregames.base.abtesting.AbTestingService
import com.moregames.base.abtesting.BaseVariation
import com.moregames.base.abtesting.ClientExperiment.ANDROID_CASHOUT_READY_NOTIFICATION
import com.moregames.base.abtesting.DEFAULT
import com.moregames.base.abtesting.variations.AndroidCashoutReadyNotificationVariation
import com.moregames.base.util.mock
import com.moregames.playtime.earnings.dto.UserCurrencyEarnings
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.extension.ExtensionContext
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.ArgumentsProvider
import org.junit.jupiter.params.provider.ArgumentsSource
import org.mockito.kotlin.mock
import java.math.BigDecimal
import java.util.*
import java.util.stream.Stream
import kotlin.test.assertEquals

class ReadyToCashoutExperimentServiceTest {
  private val abTestingService: AbTestingService = mock()

  private val underTest = ReadyToCashoutExperimentService(
    abTestingService
  )

  companion object {
    const val USER_ID = "userId"
  }

  object ReadyToCashoutParams : ArgumentsProvider {
    override fun provideArguments(context: ExtensionContext?): Stream<out Arguments> =
      Stream.of(
        Arguments.of(
          DEFAULT, UserCurrencyEarnings(BigDecimal("0.70"), Currency.getInstance("USD"), BigDecimal("0.70")), null
        ),
        Arguments.of(
          DEFAULT, UserCurrencyEarnings(BigDecimal("1.70"), Currency.getInstance("USD"), BigDecimal("1.70")), null
        ),
        Arguments.of(
          DEFAULT, UserCurrencyEarnings(BigDecimal("1.0"), Currency.getInstance("USD"), BigDecimal("1.0")), null
        ),
        Arguments.of(
          AndroidCashoutReadyNotificationVariation.CashoutReadyClaim,
          UserCurrencyEarnings(BigDecimal("0.70"), Currency.getInstance("USD"), BigDecimal("0.70")),
          ReadyToCashoutExperimentConfig(
            title = "You've Got Money!",
            description = "Open JustPlay to claim your rewards"
          )
        ),
        Arguments.of(
          AndroidCashoutReadyNotificationVariation.CashoutReadyClaim,
          UserCurrencyEarnings(BigDecimal("1.70"), Currency.getInstance("USD"), BigDecimal("1.70")),
          ReadyToCashoutExperimentConfig(
            title = "You've Made \$1.70!",
            description = "Don't wait—claim your earnings now!"
          )
        ),
        Arguments.of(
          AndroidCashoutReadyNotificationVariation.CashoutReadyClaim,
          UserCurrencyEarnings(BigDecimal("1.0"), Currency.getInstance("USD"), BigDecimal("1.0")),
          ReadyToCashoutExperimentConfig(
            title = "You've Made \$1.00!",
            description = "Don't wait—claim your earnings now!"
          )
        ),
        Arguments.of(
          AndroidCashoutReadyNotificationVariation.CashoutReadyWaiting,
          UserCurrencyEarnings(BigDecimal("0.70"), Currency.getInstance("USD"), BigDecimal("0.70")),
          ReadyToCashoutExperimentConfig(
            title = "Your Cash is Ready!",
            description = "Your JustPlay payout is waiting for you"
          )
        ),
        Arguments.of(
          AndroidCashoutReadyNotificationVariation.CashoutReadyWaiting,
          UserCurrencyEarnings(BigDecimal("1.70"), Currency.getInstance("USD"), BigDecimal("1.70")),
          ReadyToCashoutExperimentConfig(
            title = "Cash Alert: \$1.70 Ready!",
            description = "Your rewards are waiting. Withdraw now!"
          )
        ),
        Arguments.of(
          AndroidCashoutReadyNotificationVariation.CashoutReadyWaiting,
          UserCurrencyEarnings(BigDecimal("1.0"), Currency.getInstance("USD"), BigDecimal("1.0")),
          ReadyToCashoutExperimentConfig(
            title = "Cash Alert: \$1.00 Ready!",
            description = "Your rewards are waiting. Withdraw now!"
          )
        )
      )
  }

  @ParameterizedTest
  @ArgumentsSource(ReadyToCashoutParams::class)
  fun `SHOULD return correct value ON getReadyToCashoutExperimentConfig`(
    variation: BaseVariation,
    unclaimedUsdEarnings: UserCurrencyEarnings,
    expected: ReadyToCashoutExperimentConfig?
  ) = runTest {
    abTestingService.mock({ assignedVariationValue(USER_ID, ANDROID_CASHOUT_READY_NOTIFICATION) }, variation)

    val actual = underTest.getReadyToCashoutExperimentConfig(USER_ID, unclaimedUsdEarnings)
    assertEquals(expected, actual)
  }

}