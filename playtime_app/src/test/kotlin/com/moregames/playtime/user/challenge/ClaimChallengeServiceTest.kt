package com.moregames.playtime.user.challenge

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.moregames.base.bigquery.BigQueryEventPublisher
import com.moregames.base.bus.MessageBus
import com.moregames.base.exceptions.BaseException.Companion.DEFAULT_EXTERNAL_MESSAGE
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.buseffects.PushNotificationEffect
import com.moregames.playtime.notifications.PushNotification.AndroidPushNotification.SpecialChallengeClaimedNotification
import com.moregames.playtime.translations.TranslationResource
import com.moregames.playtime.translations.TranslationService
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.challenge.common.ChallengeService
import com.moregames.playtime.user.challenge.common.SpecialChallengePotService
import com.moregames.playtime.user.challenge.dto.*
import com.moregames.playtime.user.challenge.dto.bq.UserChallengeUpdatedBqDto
import com.moregames.playtime.user.challenge.dto.claim.challenge.ClaimChallengeRequestApiDto
import com.moregames.playtime.user.challenge.dto.claim.challenge.ClaimChallengeResponseApiDto
import com.moregames.playtime.user.challenge.progress.ObjectiveProgressCalculatorType
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoInteractions
import java.time.Instant
import java.util.*

class ClaimChallengeServiceTest {
  companion object {
    const val USER_ID = "userId"
    const val CLAIMED_TEXT = "You've won a golden ticket"
    const val CLAIMED_TEXT_KEY = "You've won a golden key"
    val eventId = ChallengeEventId("event-id")
    val challengeId = ChallengeId("13")
    val event = ChallengeEvent(
      id = eventId,
      dateFrom = Instant.parse("2020-01-01T00:00:00Z"),
      dateTo = Instant.parse("2020-01-01T00:00:00Z"),
      challenges = listOf(),
      eventType = ChallengeEventType.GLOBAL,
      cfg = "{}",
      enabled = true,
      bonusId = "bonusId",
    )
    val challenge = Challenge(
      id = challengeId,
      eventId = eventId,
      progressMax = 100,
      gameId = 120,
      title = "title",
      icon = "icon",
      calculator = ObjectiveProgressCalculatorType.LEVEL_ID,
      applyEarningsCut = true,
      order = 0,
      goal = 0,
      challengeType = ChallengeType.REGULAR,
    )
  }

  private val challengeRewardingService: ChallengeRewardingService = mock()
  private val translationService: TranslationService = mock()
  private val challengeService: ChallengeService = mock()
  private val bigQueryEventPublisher: BigQueryEventPublisher = mock()
  private val timeService: TimeService = mock()
  private val messageBus: MessageBus = mock()
  private val userService: UserService = mock()
  private val specialChallengePotService: SpecialChallengePotService = mock()

  private val underTest = ClaimChallengeService(
    challengeRewardingService = challengeRewardingService,
    translationService = translationService,
    challengeService = challengeService,
    bigQueryEventPublisher = bigQueryEventPublisher,
    timeService = timeService,
    messageBus = messageBus,
    userService = userService,
    specialChallengePotService = specialChallengePotService,
  )

  private val now = Instant.now()
  private val completedAt = now.minusSeconds(10)
  private val locale = Locale.ENGLISH

  @BeforeEach
  fun init() {
    translationService.mock({ translateOrDefault(TranslationResource.CHALLENGE_CLAIM_TEXT, locale) }, CLAIMED_TEXT)
    translationService.mock({ translateOrDefault(TranslationResource.SPECIAL_CHALLENGE_KEY_COLLECTED_NOTIFICATION_TITLE, locale) }, CLAIMED_TEXT_KEY)

    timeService.mock({ now() }, now)
  }

  @Test
  fun `SHOULD return not claimed response WHEN challenge is invalid state after claiming`() {

    val coins = 1598
    challengeService.mock(
      { getUserChallenge(challengeId, USER_ID) },
      UserChallenge(
        challenge = challenge,
        userId = USER_ID,
        progress = 100,
        coins = 0,
        state = ChallengeState.COMPLETED,
        completedAt = completedAt,
        updatedAt = now,
      ),
      arrayOf(
        UserChallenge(
          challenge = challenge,
          userId = USER_ID,
          progress = 100,
          coins = 0,
          state = ChallengeState.COMPLETED,
          completedAt = completedAt,
          updatedAt = now,
        )
      )
    )
    challengeRewardingService.mock({ calculateChallengeReward(challengeId, USER_ID) }, coins)
    challengeService.mock({ claimChallenge(challengeId, USER_ID, coins) }, false)
    val result = runBlocking { underTest.claimChallenge(USER_ID, ClaimChallengeRequestApiDto(challengeId), locale) }
    assertThat(result).isEqualTo(
      ClaimChallengeResponseApiDto(coins = 0, errorMessage = DEFAULT_EXTERNAL_MESSAGE)
    )

    verifyNoInteractions(bigQueryEventPublisher)
  }

  @Test
  fun `SHOULD return claimed response WHEN challenge has been claimed by other request at the same time`() {
    val challengeId = ChallengeId("13")
    val coins = 1598
    challengeService.mock(
      { getUserChallenge(challengeId, USER_ID) },
      UserChallenge(
        challenge = challenge,
        userId = USER_ID,
        progress = 100,
        coins = 0,
        state = ChallengeState.COMPLETED,
        completedAt = completedAt,
        updatedAt = now,
      ),
      arrayOf(
        UserChallenge(
          challenge = challenge,
          userId = USER_ID,
          progress = 100,
          coins = coins,
          state = ChallengeState.CLAIMED,
          completedAt = completedAt,
          updatedAt = now,
        )
      )
    )
    challengeRewardingService.mock({ calculateChallengeReward(challengeId, USER_ID) }, coins)
    challengeService.mock({ claimChallenge(challengeId, USER_ID, coins) }, false)
    val result = runBlocking { underTest.claimChallenge(USER_ID, ClaimChallengeRequestApiDto(challengeId), locale) }
    assertThat(result).isEqualTo(
      ClaimChallengeResponseApiDto(coins = coins, text = CLAIMED_TEXT)
    )
    verifyNoInteractions(bigQueryEventPublisher)
  }

  @Test
  fun `SHOULD return claimed response WHEN challenge is completed`() {
    val eventId = ChallengeEventId("event-id")
    val challengeId = ChallengeId("13")
    val coins = 1598
    challengeService.mock(
      { getUserChallenge(challengeId, USER_ID) },
      UserChallenge(
        challenge = challenge,
        userId = USER_ID,
        progress = 100,
        coins = 0,
        state = ChallengeState.COMPLETED,
        completedAt = completedAt,
        updatedAt = now,
      )
    )
    challengeRewardingService.mock({ calculateChallengeReward(challengeId, USER_ID) }, coins)
    challengeService.mock({ claimChallenge(challengeId, USER_ID, coins) }, true)
    userService.mock({ userService.getUser(USER_ID, false) }, userDtoStub.copy(id = USER_ID, locale = locale))

    val result = runBlocking { underTest.claimChallenge(USER_ID, ClaimChallengeRequestApiDto(challengeId), locale) }
    assertThat(result).isEqualTo(
      ClaimChallengeResponseApiDto(coins = coins, text = "You've won a golden ticket")
    )
    verifyBlocking(messageBus, times(0)) {
      publishAsync(PushNotificationEffect(SpecialChallengeClaimedNotification(USER_ID, locale)))
    }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        UserChallengeUpdatedBqDto(
          userId = USER_ID,
          challengeId = challengeId,
          state = ChallengeState.CLAIMED,
          gameId = 120,
          completedAt = completedAt,
          createdAt = now,
          challengeEventId = eventId,
        )
      )
    }
  }

  @Test
  fun `SHOULD return claimed response, send notification and update progress WHEN challenge is completed and challengeType is special`() {
    val eventId = ChallengeEventId("event-id")
    val challengeId = ChallengeId("13")
    val coins = 1598
    challengeService.mock(
      { getUserChallenge(challengeId, USER_ID) },
      UserChallenge(
        challenge = challenge.copy(challengeType = ChallengeType.SPECIAL),
        userId = USER_ID,
        progress = 100,
        coins = 0,
        state = ChallengeState.COMPLETED,
        completedAt = completedAt,
        updatedAt = now,
      )
    )
    challengeRewardingService.mock({ calculateChallengeReward(challengeId, USER_ID) }, coins)
    challengeService.mock({ claimChallenge(challengeId, USER_ID, coins) }, true)
    userService.mock({ userService.getUser(USER_ID, false) }, userDtoStub.copy(id = USER_ID, locale = locale))

    val result = runBlocking { underTest.claimChallenge(USER_ID, ClaimChallengeRequestApiDto(challengeId), locale) }
    assertThat(result).isEqualTo(
      ClaimChallengeResponseApiDto(coins = coins, text = CLAIMED_TEXT_KEY)
    )
    verifyBlocking(messageBus) {
      publishAsync(PushNotificationEffect(SpecialChallengeClaimedNotification(USER_ID, locale)))
    }
    verifyBlocking(bigQueryEventPublisher) {
      publish(
        UserChallengeUpdatedBqDto(
          userId = USER_ID,
          challengeId = challengeId,
          state = ChallengeState.CLAIMED,
          gameId = 120,
          completedAt = completedAt,
          createdAt = now,
          challengeEventId = eventId,
        )
      )
    }
    verifyBlocking(specialChallengePotService) {
      updateChallengeSpecialPotProgress(UserChallenge(
        challenge = challenge.copy(challengeType = ChallengeType.SPECIAL),
        userId = USER_ID,
        progress = 100,
        coins = 0,
        state = ChallengeState.CLAIMED,
        completedAt = completedAt,
        updatedAt = now,
        )
      )
    }
  }

  @Test
  fun `SHOULD return claimed response WHEN challenge has been already claimed`() {
    val challengeId = ChallengeId("13")
    val coins = 1598
    challengeService.mock(
      { getUserChallenge(challengeId, USER_ID) },
      UserChallenge(
        challenge = challenge,
        userId = USER_ID,
        progress = 100,
        coins = coins,
        state = ChallengeState.CLAIMED,
        completedAt = completedAt,
        updatedAt = now,
      )
    )
    val result = runBlocking { underTest.claimChallenge(USER_ID, ClaimChallengeRequestApiDto(challengeId), locale) }
    assertThat(result).isEqualTo(
      ClaimChallengeResponseApiDto(coins = coins, text = CLAIMED_TEXT)
    )
    verifyNoInteractions(challengeRewardingService)
    verifyNoInteractions(bigQueryEventPublisher)

  }

  @Test
  fun `SHOULD not claim challenge WHEN challenge is in progress`() {
    val challengeId = ChallengeId("13")
    challengeService.mock(
      { getUserChallenge(challengeId, USER_ID) }, UserChallenge(
        userId = USER_ID,
        challenge = challenge,
        state = ChallengeState.IN_PROGRESS,
        progress = 96,
        coins = 0,
        completedAt = null,
        updatedAt = now,
      )
    )
    val result = runBlocking { underTest.claimChallenge(USER_ID, ClaimChallengeRequestApiDto(challengeId), locale) }
    assertThat(result).isEqualTo(
      ClaimChallengeResponseApiDto(coins = 0, errorMessage = DEFAULT_EXTERNAL_MESSAGE)
    )
    verifyNoInteractions(challengeRewardingService)
    verifyNoInteractions(bigQueryEventPublisher)
  }

  @Test
  fun `SHOULD not claim challenge WHEN there is no challenge`() {
    val challengeId = ChallengeId("13")
    challengeService.mock({ getUserChallenge(challengeId, USER_ID) }, null)
    val result = runBlocking { underTest.claimChallenge(USER_ID, ClaimChallengeRequestApiDto(challengeId), locale) }
    assertThat(result).isEqualTo(
      ClaimChallengeResponseApiDto(coins = 0, errorMessage = DEFAULT_EXTERNAL_MESSAGE)
    )
    verifyNoInteractions(challengeRewardingService)
    verifyNoInteractions(bigQueryEventPublisher)
  }

}
