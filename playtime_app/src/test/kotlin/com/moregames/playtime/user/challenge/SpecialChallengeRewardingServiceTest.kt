package com.moregames.playtime.user.challenge

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isFalse
import assertk.assertions.isTrue
import com.justplayapps.service.rewarding.earnings.UserEarningsPersistenceService
import com.moregames.base.bus.MessageBus
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.earnings.CashoutSettingsService
import com.moregames.playtime.earnings.currency.dto.CurrencyExchangeResultDto
import com.moregames.playtime.earnings.dto.UserCurrencyEarnings
import com.moregames.playtime.user.cashout.CashoutService
import com.moregames.playtime.user.cashout.CashoutStatusService
import com.moregames.playtime.user.challenge.dto.ChallengeType
import com.moregames.playtime.user.challenge.dto.RewardEarningsAddedEventDto
import com.moregames.playtime.user.challenge.dto.SpecialChallengePot
import com.moregames.playtime.user.challenge.dto.SpecialChallengePotState
import com.moregames.playtime.user.challenge.dto.UserSpecialChallengePot
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import org.mockito.kotlin.verifyNoInteractions
import org.mockito.kotlin.whenever
import java.math.BigDecimal
import java.time.Instant
import java.util.*

@Suppress("OPT_IN_USAGE")
class SpecialChallengeRewardingServiceTest {

  companion object {
    const val USER_ID = "userId"
    val startedAt: Instant = Instant.parse("2025-01-31T12:13:14Z")
    val now: Instant = Instant.now()
    const val USER_POT_ID = "2bccd8ae-3248-11f0-995a-2f11e55fb839"
    val specialPotConfigStub = SpecialChallengePot(
      id = 1,
      key = "POT_KEY",
      progressMax = 100,
      uiConfig = "{}"
    )
    val userPotStub = UserSpecialChallengePot(
      id = USER_POT_ID,
      userId = USER_ID,
      state = SpecialChallengePotState.COMPLETED,
      earnings = BigDecimal.ZERO,
      applovinNonBannerRevenue = BigDecimal.ZERO,
      counter = 0,
      potConfig = specialPotConfigStub,
      progress = 100,
    )

  }

  private val userEarningsPersistenceService: UserEarningsPersistenceService = mock()
  private val cashoutSettingsService: CashoutSettingsService = mock()
  private val cashoutService: CashoutService = mock()
  private val cashoutStatusService: CashoutStatusService = mock()
  private val timeService: TimeService = mock()
  private val challengeRevenueService: ChallengeRevenueService = mock()
  private val messageBus: MessageBus = mock()

  private val service = SpecialChallengeRewardingService(
    userEarningsPersistenceService = userEarningsPersistenceService,
    cashoutSettingsService = cashoutSettingsService,
    cashoutService = cashoutService,
    cashoutStatusService = cashoutStatusService,
    timeService = timeService,
    messageBus = messageBus,
    challengeRevenueService = challengeRevenueService,
  )

  init {
    Dispatchers.setMain(StandardTestDispatcher())
  }

  @BeforeEach
  fun before() {
    cashoutSettingsService.mock({ getUserMaxEarningsAmount(USER_ID) }, BigDecimal("30.0"))
    cashoutService.mock(
      { getNonCashedUserCurrencyEarnings(USER_ID) }, UserCurrencyEarnings(
        amountUsd = BigDecimal.ZERO,
        userCurrency = Currency.getInstance("USD"),
        userCurrencyAmount = BigDecimal.ZERO
      )
    )
    whenever(timeService.now()).thenReturn(now)
    whenever(challengeRevenueService.specialChallengeRevenueShare()).thenReturn(BigDecimal("0.15"))
  }

  @Test
  fun `SHOULD calculate reward ON calculateChallengeEventRewardUsd WHEN no revenue`() {
    runBlocking { service.calculateChallengeEventRewardUsd(userPotStub) }.let { result ->
      assertThat(result).isEqualTo(BigDecimal("0.01"))
    }
  }

  @ParameterizedTest
  @CsvSource(
    value = [
      "0.0,0.01",
      "0.01,0.01",
      "0.05,0.01",
      "0.06,0.01",
      "0.07,0.011445",
      "1.42,0.232170",
      "1.43,0.233805",
      "180.0,29.43000",
      "184.0,30.0",
    ]
  )
  fun `SHOULD calculate reward ON calculateChallengeEventRewardUsd`(applovinInterRevenue: BigDecimal, expected: BigDecimal) {
    val userPot = userPotStub.copy(applovinNonBannerRevenue = applovinInterRevenue)
    runBlocking { service.calculateChallengeEventRewardUsd(userPot) }.let { result ->
      assertThat(result).isEqualTo(expected)
    }
  }

  @Test
  fun `SHOULD calculate reward ON calculateChallengeEventRewardUsd WHEN user has unpaid earnings`() {
    val unpaidEarnings = BigDecimal("26.86")
    val expected = BigDecimal("3.14")

    val userPot = userPotStub.copy(applovinNonBannerRevenue = BigDecimal("180.0"))

    cashoutService.mock(
      { getNonCashedUserCurrencyEarnings(USER_ID) },
      UserCurrencyEarnings(
        amountUsd = unpaidEarnings,
        userCurrency = Currency.getInstance("USD"),
        userCurrencyAmount = unpaidEarnings
      )
    )

    runBlocking { service.calculateChallengeEventRewardUsd(userPot) }.let { result ->
      assertThat(result).isEqualTo(expected)
    }
  }

  @Test
  fun `SHOULD give earnings ON giveChallengeEventReward`() = runTest {
    val currencyCad = Currency.getInstance("CAD")
    val earningsAddedEvent = RewardEarningsAddedEventDto(
      metaId = -2,
      userId = USER_ID,
      amount = BigDecimal("1.23"),
      amountUserCurrency = BigDecimal("2.71"),
      userCurrencyCode = "CAD",
      challengeEventId = USER_POT_ID,
      createdAt = now,
      challengeType = ChallengeType.SPECIAL,
    )
    val reward = CurrencyExchangeResultDto(
      usdAmount = BigDecimal("1.23"),
      userCurrency = currencyCad,
      amount = BigDecimal("2.71"),
      amountNoRounding = BigDecimal("2.3456"),
    )

    userEarningsPersistenceService.mock(
      {
        addSpecialChallengeReward(
          USER_POT_ID,
          USER_ID,
          rewardUsd = BigDecimal("1.23"),
          currency = currencyCad,
          reward = BigDecimal("2.71"),
        )
      },
      true
    )

    val actual = runBlocking { service.giveChallengeEventReward(userPotStub, reward) }
    advanceUntilIdle()

    assertThat(actual).isTrue()

    verifyBlocking(cashoutStatusService) { enableCashout(USER_ID) }
    verifyBlocking(messageBus) { publish(earningsAddedEvent) }
  }

  @Test
  fun `SHOULD NOT enable cashout ON giveChallengeEventReward WHEN reward already given`() = runTest {
    val currencyCad = Currency.getInstance("CAD")
    val reward = CurrencyExchangeResultDto(
      usdAmount = BigDecimal("1.23"),
      userCurrency = currencyCad,
      amount = BigDecimal("2.71"),
      amountNoRounding = BigDecimal("2.3456"),
    )

    userEarningsPersistenceService.mock(
      {
        addSpecialChallengeReward(
          USER_POT_ID,
          USER_ID,
          rewardUsd = BigDecimal("1.23"),
          currency = currencyCad,
          reward = BigDecimal("2.71"),
        )
      },
      false
    )

    val actual = runBlocking { service.giveChallengeEventReward(userPotStub, reward) }
    advanceUntilIdle()

    assertThat(actual).isFalse()

    verifyNoInteractions(cashoutStatusService)
    verifyNoInteractions(messageBus)
  }
}