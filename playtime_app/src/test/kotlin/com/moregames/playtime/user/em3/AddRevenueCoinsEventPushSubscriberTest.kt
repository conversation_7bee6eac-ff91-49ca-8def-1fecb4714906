package com.moregames.playtime.user.em3

import com.justplayapps.service.rewarding.earnings.em3.AddRevenueCoinsEventPushSubscriber
import com.justplayapps.service.rewarding.earnings.em3.Em3CoinsService
import com.justplayapps.service.rewarding.earnings.em3.dto.AddRevenueCoinsEvent
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.playtime.user.LimitedTrackingInfo
import com.moregames.playtime.user.UserDto
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import java.time.Instant
import java.time.LocalDate

class AddRevenueCoinsEventPushSubscriberTest {
  private val em3CoinsService: Em3CoinsService = mock()

  private val underTest = AddRevenueCoinsEventPushSubscriber(
    em3CoinsService = em3CoinsService,
  )

  companion object {
    private const val USER_ID = "user-id"
    private val now = Instant.now()
    private val revenueFrom = now.minusSeconds(2000)
    private val revenueTo = now.minusSeconds(1000)

    private val user = UserDto(
      id = USER_ID,
      googleAdId = "google123",
      createdAt = now.minusSeconds(7000),
      countryCode = "GB",
      limitedTrackingInfo = LimitedTrackingInfo(false, "GB", true),
      trackingData = null,
      isDeleted = false,
      appPlatform = ANDROID,
      lastActiveAtDay = LocalDate.now()
    )

    private val message = AddRevenueCoinsEvent(userId = USER_ID, platform = ANDROID, takeRevenueFrom = revenueFrom, takeRevenueTo = revenueTo)
  }

  @Test
  fun `SHOULD add coins ON handle`() {
    runBlocking { underTest.handle(message) }

    verifyBlocking(em3CoinsService) { giveRevenueBasedCoins(USER_ID, message.platform, revenueFrom, revenueTo) }
  }
}