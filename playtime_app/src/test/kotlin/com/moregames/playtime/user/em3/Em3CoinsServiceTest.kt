package com.moregames.playtime.user.em3

import com.justplayapps.service.rewarding.earnings.EmExperimentBaseService
import com.justplayapps.service.rewarding.earnings.UserCurrentCoinsBalancePersistenceService
import com.justplayapps.service.rewarding.earnings.UserCurrentCoinsBalanceService
import com.justplayapps.service.rewarding.earnings.em3.Em3CoinsService
import com.justplayapps.service.rewarding.earnings.em3.RevenueCoinsPersistenceService
import com.justplayapps.service.rewarding.earnings.em3.dto.AddRevenueCoinsEvent
import com.justplayapps.service.rewarding.earnings.proto.UserCoinsAddedEventKt.em3UserGameCoins
import com.justplayapps.service.rewarding.earnings.proto.userCoinsAddedEvent
import com.justplayapps.service.rewarding.facade.PlaytimeFacade
import com.moregames.base.bus.MessageBus
import com.moregames.base.coins.UserCurrentCoinsGoalBalance
import com.moregames.base.dto.AppPlatform.ANDROID
import com.moregames.base.dto.AppPlatform.IOS
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.base.util.redis.SafeJedisClient
import com.moregames.playtime.utils.userDtoStub
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Instant
import java.util.*
import kotlin.time.Duration.Companion.hours

class Em3CoinsServiceTest {

  private val jedisClient: SafeJedisClient = mock()
  private val revenueCoinsPersistenceService: RevenueCoinsPersistenceService = mock()
  private val timeService: TimeService = mock()
  private val messageBus: MessageBus = mock()
  private val userCurrentCoinsBalancePersistenceService: UserCurrentCoinsBalancePersistenceService = mock()
  private val userCurrentCoinsBalanceService: UserCurrentCoinsBalanceService = mock()
  private val playtimeFacade: PlaytimeFacade = mock()
  private val emExperimentBaseService: EmExperimentBaseService = mock()

  private val underTest = Em3CoinsService(
    jedisClient = jedisClient,
    revenueCoinsPersistenceService = revenueCoinsPersistenceService,
    timeService = timeService,
    messageBus = messageBus,
    userCurrentCoinsBalancePersistenceService = userCurrentCoinsBalancePersistenceService,
    userCurrentCoinsBalanceService = userCurrentCoinsBalanceService,
    playtimeFacade = playtimeFacade,
    emExperimentBaseService = emExperimentBaseService,
  )

  companion object {
    private val now = Instant.parse("2025-03-17T13:14:15.57Z")

    private const val USER_ID = "user-id"
    private const val COINS_MULTIPLIER = 2000

    private val user = userDtoStub.copy(
      id = USER_ID,
      appPlatform = ANDROID,
    )
  }

  @BeforeEach
  fun before() {
    whenever(timeService.now()).thenReturn(now)
    playtimeFacade.mock({ getOfferwallCoinsToUsdConversionRatio() }, BigDecimal("220"))
    emExperimentBaseService.mock({ inflatingCoinsMultiplier(USER_ID) }, COINS_MULTIPLIER)
  }

  @Test
  fun `SHOULD NOT create new task ON launchRevenueCheckTask WHEN there is already planned check ahead in cache`() {
    val userId = UUID.randomUUID().toString()
    jedisClient.mock({ get("RevenueCoinsCheckAt:$userId") }, "2025-03-17T13:14:17Z")

    runBlocking { underTest.launchRevenueCheckTask(userId, ANDROID) }

    verifyNoInteractions(messageBus)
    verifyNoInteractions(revenueCoinsPersistenceService)
  }

  @Test
  fun `SHOULD NOT create new task ON launchRevenueCheckTask WHEN there is already planned check ahead in db`() {
    val userId = UUID.randomUUID().toString()
    revenueCoinsPersistenceService.mock({ getRevenueCoinsCheckAt(userId) }, Instant.parse("2025-03-17T13:14:17Z"))

    runBlocking { underTest.launchRevenueCheckTask(userId, ANDROID) }

    verifyNoInteractions(messageBus)
    verifyBlocking(revenueCoinsPersistenceService) { getRevenueCoinsCheckAt(userId) }
    verifyNoMoreInteractions(revenueCoinsPersistenceService)
  }

  @Test
  fun `SHOULD create new task ON launchRevenueCheckTask WHEN no previous launches`() {
    val userId = UUID.randomUUID().toString()
    val expectedIntervalStart = Instant.parse("2025-03-17T13:14:15Z") // verify rounding
    val expectedIntervalEnd = Instant.parse("2025-03-17T13:19:15Z")

    runBlocking { underTest.launchRevenueCheckTask(userId, ANDROID) }

    verifyBlocking(messageBus) {
      publish(
        AddRevenueCoinsEvent(userId, expectedIntervalStart, expectedIntervalEnd, ANDROID),
        expectedIntervalEnd
      )
    }
    verifyBlocking(jedisClient) {
      set(
        eq("RevenueCoinsCheckAt:$userId"),
        eq("2025-03-17T13:19:15Z"), // expectedIntervalEnd + string generation validation
        argThat { this.getParam<Long>("ex").equals(1.hours.inWholeSeconds) }
      )
    }
    verifyBlocking(revenueCoinsPersistenceService) { trackRevenueCoinsCheckAt(userId, expectedIntervalEnd) }
  }

  @Test
  fun `SHOULD create new task ON launchRevenueCheckTask WHEN there is previous launches`() {
    val userId = UUID.randomUUID().toString()
    val expectedIntervalStart = Instant.parse("2025-03-17T12:57:17Z")
    val expectedIntervalEnd = Instant.parse("2025-03-17T13:19:15Z") // depends on `now` only

    jedisClient.mock({ get("RevenueCoinsCheckAt:$userId") }, "2025-03-17T12:57:17Z")

    runBlocking { underTest.launchRevenueCheckTask(userId, IOS) }

    verifyBlocking(messageBus) {
      publish(
        AddRevenueCoinsEvent(userId, expectedIntervalStart, expectedIntervalEnd, IOS),
        expectedIntervalEnd
      )
    }
    verifyBlocking(jedisClient) {
      set(
        eq("RevenueCoinsCheckAt:$userId"),
        eq("2025-03-17T13:19:15Z"), // expectedIntervalEnd + string generation validation
        argThat { this.getParam<Long>("ex").equals(1.hours.inWholeSeconds) }
      )
    }
    verifyBlocking(revenueCoinsPersistenceService) { trackRevenueCoinsCheckAt(userId, expectedIntervalEnd) }
  }

  @Test
  fun `SHOULD do nothing ON giveRevenueBasedCoins WHEN zero inflated coins added`() {
    val from = now.minusSeconds(777)
    val to = now.minusSeconds(666)

    revenueCoinsPersistenceService.mock({ getApplovinRevenue(USER_ID, from, to) }, BigDecimal("0.0000011"))

    runBlocking { underTest.giveRevenueBasedCoins(USER_ID, user.appPlatform, from, to) }

    verifyBlocking(revenueCoinsPersistenceService) { getApplovinRevenue(USER_ID, from, to) }
    verifyNoMoreInteractions(revenueCoinsPersistenceService)
    verifyNoInteractions(messageBus)
  }

  @Test
  fun `SHOULD add coins ON giveRevenueBasedCoins`() {
    val from = now.minusSeconds(777)
    val to = now.minusSeconds(666)

    revenueCoinsPersistenceService.mock({ getApplovinRevenue(USER_ID, from, to) }, BigDecimal("1.23"))
    userCurrentCoinsBalanceService.mock({ getUserCurrentCoinsBalance(USER_ID, user.appPlatform) }, UserCurrentCoinsGoalBalance(146, 10, 20))

    runBlocking { underTest.giveRevenueBasedCoins(USER_ID, user.appPlatform, from, to) }

    verifyBlocking(userCurrentCoinsBalancePersistenceService) {
      addCoinsToCurrentBalance(USER_ID, BigDecimal("270.60"))
    }

    verifyBlocking(messageBus) {
      publish(
        userCoinsAddedEvent {
          this.userId = USER_ID
          this.coins = 146
          this.em3GameCoinsData = em3UserGameCoins {}
        }
      )
    }
  }
}