package com.moregames.playtime.user.em3

import assertk.assertThat
import assertk.assertions.isEqualTo
import com.justplayapps.base.Common
import com.justplayapps.playtime.rewarding.proto.copy
import com.justplayapps.playtime.rewarding.proto.getUserDataResponse
import com.justplayapps.service.rewarding.earnings.*
import com.justplayapps.service.rewarding.earnings.dto.Earnings
import com.justplayapps.service.rewarding.earnings.dto.EarningsCalculationResult
import com.justplayapps.service.rewarding.earnings.dto.UserCurrencyEarnings
import com.justplayapps.service.rewarding.earnings.em3.UserEarningsEm3Service
import com.moregames.base.coins.UserCurrentCoinsGoalBalance
import com.moregames.base.dto.AppPlatform
import com.moregames.base.messaging.dto.RevenueReceivedEventDto
import com.moregames.base.util.mock
import com.moregames.base.util.toProto
import com.moregames.playtime.earnings.dto.GenericRevenueDto
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyBlocking
import java.math.BigDecimal
import java.time.Duration
import java.time.Instant
import java.util.*

class UserEarningsEm3ServiceTest {
  private val userEarningsPersistenceService: UserEarningsPersistenceService = mock()
  private val userEarningsService: UserEarningsService = mock()
  private val userEarningsEm2Service: UserEarningsEm2Service = mock()
  private val userCurrentCoinsBalanceService: UserCurrentCoinsBalanceService = mock()

  private val underTest = UserEarningsEm3Service(
    userEarningsPersistenceService = userEarningsPersistenceService,
    userEarningsService = userEarningsService,
    userEarningsEm2Service = userEarningsEm2Service,
    userCurrentCoinsBalanceService = userCurrentCoinsBalanceService,
  )

  private val userId = "userId"
  private val metaId = 123
  private val cadCurrency = Currency.getInstance("CAD")
  private val cashOutPeriodStart = Instant.now().minus(Duration.ofHours(3))
  private val revenueGame = GenericRevenueDto(
    id = "eventId",
    userId = "userId",
    timestamp = Instant.now(),
    source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
    amount = BigDecimal("2.00"),
    amountExtra = null,
    gameId = null,
  )
  private val revenueOffer = GenericRevenueDto(
    id = "eventId",
    userId = "userId",
    timestamp = Instant.now(),
    source = RevenueReceivedEventDto.RevenueSource.IRON_SOURCE,
    amount = BigDecimal("2.00"),
    amountExtra = null,
    gameId = null,
  )
  private val revenueVideoAndroid = GenericRevenueDto(
    id = "eventId",
    userId = "userId",
    timestamp = Instant.now(),
    source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
    amount = BigDecimal("0.11"),
    amountExtra = null,
    gameId = 1000001,
  )
  private val revenueVideoIos = GenericRevenueDto(
    id = "eventId",
    userId = "userId",
    timestamp = Instant.now(),
    source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
    amount = BigDecimal("0.17"),
    amountExtra = null,
    gameId = 1000002,
  )
  private val earnings = Earnings(
    userId = userId,
    earningsSum = BigDecimal("1.1513635"),
    quotasDto = null,
  )
  private val userCurrentCoinsBalance = UserCurrentCoinsBalance(
    gameCoins = BigDecimal("101.1"),
    offerCoins = BigDecimal("102.2"),
    bonusCoins = BigDecimal("303.3")
  )
  private val earningsConversionResultDto = EarningsCalculationResult.Simple(
    metaId = metaId,
    amount = BigDecimal("1.25"),
    amountNoRounding = BigDecimal("1.2537128712871287128712871287129")
  )
  private val calculationExpected = EarningsCalculationResult.Em2(
    simpleCalculationResult = earningsConversionResultDto,
    noEarnings = false,
    realRevenue = BigDecimal("4.28"),
    realGameRevenue = BigDecimal("2.00"),
    em2CoinsBalance = userCurrentCoinsBalance,
    coinsForOneDollar = BigDecimal("220.0"),
    em2Revenue = BigDecimal("2.302727"),
    em2GameRevenue = BigDecimal("0.459545"),
    earnings = earnings,
  )
  private val unpaidEarnings = UserCurrencyEarnings(
    amountUsd = BigDecimal("2.71"),
    userCurrency = cadCurrency,
    userCurrencyAmount = BigDecimal("3.14")
  )

  private val userData = getUserDataResponse {
    offerWallCoinsToUsdConversionRatio = BigDecimal("220.0").toProto()
    isUserBlocked = false
    userMaxEarningsAmount = BigDecimal("30.0").toProto()
    userCountryCode = "US"
    userCurrency = cadCurrency.currencyCode
    platform = Common.AppPlatformProto.ANDROID
  }

  init {
    userEarningsService.mock({ getFirstCpTopUp(any(), any(), any()) }, BigDecimal.ZERO)
    userEarningsPersistenceService.mock({ loadUnpaidUserCurrencyEarnings(userId) }, unpaidEarnings)
    userCurrentCoinsBalanceService.mock({ loadCurrentCoinsBalanceEm2(userId) }, userCurrentCoinsBalance)
  }

  @Test
  fun `SHOULD convert revenue to earnings ON convertRevenueToEarnings`() {
    prepareBasicTest()

    runBlocking { underTest.convertRevenueToEarnings(userId, userData, cashOutPeriodStart) }.let { actual ->
      assertThat(actual).isEqualTo(calculationExpected)
    }

    verifyBlocking(userEarningsService) {
      giveEarningsToUser(
        userId = userId,
        revenueList = listOf(revenueGame, revenueOffer, revenueVideoAndroid, revenueVideoIos),
        earningsFromRevenue = earnings,
        earningsWithAdditionsAmountUsd = BigDecimal("1.1513635"),
        nonBoostedEarningsWithAdditionsAmountUsd = null,
        userCurrency = cadCurrency,
      )
    }
  }

  @Test
  fun `SHOULD convert revenue to earnings ON convertRevenueToEarnings WHEN earnings are boosted`() {
    val allRevenue = listOf(revenueGame, revenueOffer, revenueVideoAndroid, revenueVideoIos)

    userEarningsPersistenceService.mock({ getUnconvertedRevenue(userId) }, allRevenue)
    userEarningsService.mock(
      {
        giveEarningsToUser(
          userId = userId,
          revenueList = allRevenue,
          earningsFromRevenue = earnings,
          earningsWithAdditionsAmountUsd = BigDecimal("1.31415"),
          nonBoostedEarningsWithAdditionsAmountUsd = BigDecimal("0.19876"),
          cadCurrency,
        )
      },
      earningsConversionResultDto
    )
    userEarningsService.mock(
      { boostEarnings(userId, earnings.earningsSum, cashOutPeriodStart) },
      UserEarningsService.BoostedEarnings(
        earningsBefore = BigDecimal("0.19876"),
        earningsAfter = BigDecimal("1.31415"),
      )
    )

    runBlocking { underTest.convertRevenueToEarnings(userId, userData, cashOutPeriodStart) }.let { actual ->
      assertThat(actual).isEqualTo(calculationExpected)
    }

    verifyBlocking(userEarningsService) {
      giveEarningsToUser(
        userId = userId,
        revenueList = allRevenue,
        earningsFromRevenue = earnings,
        earningsWithAdditionsAmountUsd = BigDecimal("1.31415"),
        nonBoostedEarningsWithAdditionsAmountUsd = BigDecimal("0.19876"),
        userCurrency = cadCurrency,
      )
    }
  }

  @Test
  fun `SHOULD return zero earnings ON convertRevenueToEarnings WHEN user is blocked`() {
    val allRevenue = listOf(revenueGame, revenueOffer, revenueVideoAndroid, revenueVideoIos)
    val expectedEarnings = Earnings(userId, BigDecimal.ZERO)
    val expectedCalculations = EarningsCalculationResult.Em2(
      simpleCalculationResult = earningsConversionResultDto,
      noEarnings = false,
      realRevenue = BigDecimal("4.28"),
      realGameRevenue = BigDecimal("2.00"),
      em2CoinsBalance = userCurrentCoinsBalance,
      coinsForOneDollar = BigDecimal("220.0"),
      em2Revenue = BigDecimal("2.302727"),
      em2GameRevenue = BigDecimal("0.459545"),
      earnings = expectedEarnings,
    )

    userEarningsPersistenceService.mock({ getUnconvertedRevenue(userId) }, allRevenue)
    userEarningsService.mock(
      {
        giveEarningsToUser(
          userId = userId,
          revenueList = listOf(revenueGame, revenueOffer, revenueVideoAndroid, revenueVideoIos),
          earningsFromRevenue = expectedEarnings,
          earningsWithAdditionsAmountUsd = BigDecimal.ZERO,
          nonBoostedEarningsWithAdditionsAmountUsd = null,
          cadCurrency,
        )
      },
      earningsConversionResultDto
    )

    val userDataResponse = userData.copy { isUserBlocked = true }

    runBlocking { underTest.convertRevenueToEarnings(userId, userDataResponse, cashOutPeriodStart) }.let { actual ->
      assertThat(actual).isEqualTo(expectedCalculations)
    }

    verifyBlocking(userEarningsService) {
      giveEarningsToUser(
        userId = userId,
        revenueList = listOf(revenueGame, revenueOffer, revenueVideoAndroid, revenueVideoIos),
        earningsFromRevenue = expectedEarnings,
        earningsWithAdditionsAmountUsd = BigDecimal.ZERO,
        nonBoostedEarningsWithAdditionsAmountUsd = null,
        userCurrency = cadCurrency
      )
    }
  }

  @Test
  fun `SHOULD not give more than max earnings ON convertRevenueToEarnings`() {
    val allRevenue = listOf(revenueGame, revenueOffer, revenueVideoAndroid, revenueVideoIos)
    val unpaidEarnings = UserCurrencyEarnings(
      amountUsd = BigDecimal("29.99"),
      userCurrency = cadCurrency,
      userCurrencyAmount = BigDecimal("31.42")
    )
    val expectedEarnings = Earnings(userId, BigDecimal("0.01"))
    val calculationExpected = EarningsCalculationResult.Em2(
      simpleCalculationResult = earningsConversionResultDto,
      noEarnings = false,
      realRevenue = BigDecimal("4.28"),
      realGameRevenue = BigDecimal("2.00"),
      em2CoinsBalance = userCurrentCoinsBalance,
      coinsForOneDollar = BigDecimal("220.0"),
      em2Revenue = BigDecimal("2.302727"),
      em2GameRevenue = BigDecimal("0.459545"),
      earnings = expectedEarnings,
    )

    userEarningsPersistenceService.mock({ getUnconvertedRevenue(userId) }, allRevenue)
    userEarningsPersistenceService.mock({ loadUnpaidUserCurrencyEarnings(userId) }, unpaidEarnings)
    userEarningsService.mock(
      {
        giveEarningsToUser(
          userId = userId,
          revenueList = listOf(revenueGame, revenueOffer, revenueVideoAndroid, revenueVideoIos),
          earningsFromRevenue = expectedEarnings,
          earningsWithAdditionsAmountUsd = BigDecimal("0.01"),
          nonBoostedEarningsWithAdditionsAmountUsd = null,
          cadCurrency,
        )
      },
      earningsConversionResultDto
    )

    runBlocking { underTest.convertRevenueToEarnings(userId, userData, cashOutPeriodStart) }.let { actual ->
      assertThat(actual).isEqualTo(calculationExpected)
    }

    verifyBlocking(userEarningsService) {
      giveEarningsToUser(
        userId = userId,
        revenueList = listOf(revenueGame, revenueOffer, revenueVideoAndroid, revenueVideoIos),
        earningsFromRevenue = expectedEarnings,
        earningsWithAdditionsAmountUsd = BigDecimal("0.01"),
        nonBoostedEarningsWithAdditionsAmountUsd = null,
        userCurrency = cadCurrency
      )
    }
  }

  @Test
  fun `SHOULD return zero earnings ON convertRevenueToEarnings WHEN no coins`() {
    val coinsBalance = UserCurrentCoinsBalance(
      gameCoins = BigDecimal.ZERO,
      offerCoins = BigDecimal.ZERO,
      bonusCoins = BigDecimal.ZERO,
    )

    val expected = EarningsCalculationResult.Em2(
      simpleCalculationResult = EarningsCalculationResult.Simple(null, BigDecimal.ZERO, BigDecimal.ZERO),
      noEarnings = true,
      realRevenue = BigDecimal.ZERO,
      realGameRevenue = BigDecimal.ZERO,
      em2CoinsBalance = coinsBalance,
      coinsForOneDollar = BigDecimal("220.0"),
      em2Revenue = BigDecimal.ZERO,
      em2GameRevenue = BigDecimal.ZERO,
      earnings = Earnings(userId, BigDecimal.ZERO),
    )

    userCurrentCoinsBalanceService.mock({ loadCurrentCoinsBalanceEm2(userId) }, coinsBalance)
    userEarningsPersistenceService.mock({ getUnconvertedRevenue(userId) }, emptyList())
    userEarningsEm2Service.mock(
      {
        processAndReturnZeroEarnings(
          userId, emptyList(), BigDecimal.ZERO, BigDecimal.ZERO, coinsBalance, BigDecimal("220.0"), cadCurrency
        )
      },
      expected
    )

    userCurrentCoinsBalanceService.mock({ getUserCurrentCoinsBalance(userId, AppPlatform.ANDROID) }, UserCurrentCoinsGoalBalance(0, 0, 0))

    runBlocking { underTest.convertRevenueToEarnings(userId, userData, cashOutPeriodStart) }.let { actual ->
      assertThat(actual).isEqualTo(expected)
    }
  }

  @Test
  fun `SHOULD return zero earnings ON convertRevenueToEarnings WHEN no coins AND some real revenue`() {
    val revenue = GenericRevenueDto(
      id = "eventId",
      userId = "userId",
      timestamp = Instant.now(),
      source = RevenueReceivedEventDto.RevenueSource.APPLOVIN,
      amount = BigDecimal("2.00"),
      amountExtra = null,
      gameId = null,
    )
    val coinsBalance = UserCurrentCoinsBalance(
      gameCoins = BigDecimal("0.00"),
      offerCoins = BigDecimal("0.000"),
      bonusCoins = BigDecimal("0.0000"),
    )

    val expected = EarningsCalculationResult.Em2(
      simpleCalculationResult = EarningsCalculationResult.Simple(null, BigDecimal.ZERO, BigDecimal.ZERO),
      noEarnings = true,
      realRevenue = BigDecimal("2.00"),
      realGameRevenue = BigDecimal("2.00"),
      em2CoinsBalance = coinsBalance,
      coinsForOneDollar = BigDecimal("100.0"),
      em2Revenue = BigDecimal.ZERO,
      em2GameRevenue = BigDecimal.ZERO,
      earnings = Earnings(userId, BigDecimal.ZERO),
    )

    userCurrentCoinsBalanceService.mock({ loadCurrentCoinsBalanceEm2(userId) }, coinsBalance)
    userEarningsPersistenceService.mock({ getUnconvertedRevenue(userId) }, listOf(revenue))
    userEarningsEm2Service.mock(
      {
        processAndReturnZeroEarnings(
          userId, listOf(revenue), BigDecimal("2.00"), BigDecimal("2.00"), coinsBalance, BigDecimal("220.0"), cadCurrency
        )
      },
      expected
    )

    userCurrentCoinsBalanceService.mock({ getUserCurrentCoinsBalance(userId, AppPlatform.ANDROID) }, UserCurrentCoinsGoalBalance(0, 0, 0))

    runBlocking { underTest.convertRevenueToEarnings(userId, userData, cashOutPeriodStart) }.let { actual ->
      assertThat(actual).isEqualTo(expected)
    }
  }

  private fun prepareBasicTest() {
    val allRevenue = listOf(revenueGame, revenueOffer, revenueVideoAndroid, revenueVideoIos)

    userEarningsPersistenceService.mock({ getUnconvertedRevenue(userId) }, allRevenue)
    userEarningsService.mock(
      {
        giveEarningsToUser(
          userId = userId,
          revenueList = allRevenue,
          earningsFromRevenue = earnings,
          earningsWithAdditionsAmountUsd = BigDecimal("1.1513635"),
          nonBoostedEarningsWithAdditionsAmountUsd = null,
          cadCurrency,
        )
      },
      earningsConversionResultDto
    )
  }
}