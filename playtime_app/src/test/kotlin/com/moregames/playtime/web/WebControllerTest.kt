package com.moregames.playtime.web

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isNull
import com.moregames.base.coins.UserCurrentCoinsGoalBalance
import com.moregames.base.config.ApplicationConfig
import com.moregames.base.dto.AppPlatform
import com.moregames.base.dto.AppVersionDto
import com.moregames.base.dto.TrackingDataType
import com.moregames.base.util.Constants
import com.moregames.base.util.TimeService
import com.moregames.base.util.mock
import com.moregames.playtime.buseffects.AmplitudeEventEffectHandler
import com.moregames.playtime.earnings.dto.UserCurrencyEarnings
import com.moregames.playtime.ios.IosOnlineUsersService
import com.moregames.playtime.ios.dto.IdfaApiDto
import com.moregames.playtime.ios.dto.IdfvApiDto
import com.moregames.playtime.rewarding.RewardingFacade
import com.moregames.playtime.user.UserController
import com.moregames.playtime.user.UserService
import com.moregames.playtime.user.cashout.*
import com.moregames.playtime.user.cashout.dto.CashoutPeriodDto
import com.moregames.playtime.user.dto.AdjustIdDto
import com.moregames.playtime.user.dto.DeviceTokenApiDto
import com.moregames.playtime.user.dto.FirebaseAppInstanceIdDto
import com.moregames.playtime.user.tracking.TrackingData
import com.moregames.playtime.user.verification.VerificationController
import com.moregames.playtime.util.installDefaultContentNegotiation
import com.moregames.playtime.utils.Json
import com.moregames.playtime.utils.user
import io.ktor.application.*
import io.ktor.http.*
import io.ktor.routing.*
import io.ktor.server.testing.*
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.encodeToString
import net.javacrumbs.jsonunit.JsonAssert
import net.javacrumbs.jsonunit.JsonAssert.assertJsonEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*

@OptIn(ExperimentalSerializationApi::class)
class WebControllerTest {

  private val userService: UserService = mock()
  private val cashoutPeriodsService: CashoutPeriodsService = mock()
  private val cashoutStatusService: CashoutStatusService = mock()
  private val timeService: TimeService = mock()
  private val cashoutController: CashoutController = mock()
  private val paymentsController: PaymentsController = mock()
  private val verificationController: VerificationController = mock()
  private val iosOnlineUsersService: IosOnlineUsersService = mock()

  private val cashoutService: CashoutService = mock {
    onBlocking { userHasCashouts(any()) } doReturn false
  }
  private val amplitudeEventEffectHandler: AmplitudeEventEffectHandler = mock {
    onBlocking { shouldUseAmplitudeAnalytics(USER_ID) } doReturn false
  }
  private val applicationConfig: ApplicationConfig = mock()
  private val rewardingFacade: RewardingFacade = mock()

  private val webGamesController: WebGamesController = mock()
  private fun controller(): Application.() -> Unit = {
    install(IgnoreTrailingSlash)
    installDefaultContentNegotiation()
    routing {
      WebController(
        userService = userService,
        cashoutPeriodsService = cashoutPeriodsService,
        cashoutStatusService = cashoutStatusService,
        timeService = timeService,
        cashoutController = cashoutController,
        paymentsController = paymentsController,
        verificationController = verificationController,
        cashoutService = cashoutService,
        amplitudeEventEffectHandler = amplitudeEventEffectHandler,
        applicationConfig = applicationConfig,
        webGamesController = webGamesController,
        rewardingFacade = rewardingFacade,
        iosOnlineUsersService = iosOnlineUsersService,
      ).startRouting(this)
    }
  }

  private companion object {
    const val USER_ID = "userId"
    val now: Instant = Instant.now()
    val cashoutPeriod = CashoutPeriodDto(
      userId = USER_ID,
      periodStart = now.minus(12, ChronoUnit.HOURS),
      periodEnd = now.plus(12, ChronoUnit.HOURS),
      coinGoal = 2000,
      counter = 3,
      noEarningsCounter = 0,
      coinGoalMilestones = emptyList(),
    )
  }

  @BeforeEach
  fun before() {
    whenever(timeService.now()).thenReturn(now)
  }

  @Test
  fun `SHOULD enable nested controllers ON controller creation`() = withTestApplication(controller()) {
    // UserId context: /users/${userId}/...
    val cashoutControllerRouteCapture = argumentCaptor<Route>()
    val paymentsControllerRouteCapture = argumentCaptor<Route>()
    val verificationControllerCapture = argumentCaptor<Route>()

    verify(cashoutController).startRouting(cashoutControllerRouteCapture.capture())
    verify(paymentsController).startRouting(paymentsControllerRouteCapture.capture())
    verify(verificationController).startRouting(verificationControllerCapture.capture())

    var cashoutRoute: Route? = cashoutControllerRouteCapture.firstValue
    var paymentsRoute: Route? = paymentsControllerRouteCapture.firstValue
    var verificationRoute: Route? = verificationControllerCapture.firstValue

    listOf("{${UserController.USER_ID_PARAMETER}}", "users", "web", "").forEach { path ->

      assertThat(cashoutRoute?.selector?.toString()).isEqualTo(path)
      assertThat(paymentsRoute?.selector?.toString()).isEqualTo(path)
      assertThat(verificationRoute?.selector?.toString()).isEqualTo(path)

      cashoutRoute = cashoutRoute?.parent
      paymentsRoute = paymentsRoute?.parent
      verificationRoute = verificationRoute?.parent
    }
    assertThat(cashoutRoute).isNull()
    assertThat(paymentsRoute).isNull()
    assertThat(verificationRoute).isNull()
  }

  @Test
  fun `SHOULD save idfa ON idfa post call`() = withTestApplication(controller()) {
    val idfa = UUID.randomUUID().toString()

    val actual = handleRequest(
      method = HttpMethod.Post,
      uri = "/web/users/${USER_ID}/idfa"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(Constants.IOS_WEB_APP_VERSION_HEADER, "1")
      setBody(Json.defaultJsonConverter.encodeToString(IdfaApiDto(idfa = idfa)))
    }

    assertThat(actual.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userService, times(1)) { addUserTrackingData(USER_ID, TrackingData(idfa, TrackingDataType.IDFA, AppPlatform.IOS_WEB)) }
  }

  @Test
  fun `SHOULD save idfv ON idfa post call`() = withTestApplication(controller()) {
    val idfv = UUID.randomUUID().toString()

    val actual = handleRequest(
      method = HttpMethod.Post,
      uri = "/web/users/${USER_ID}/idfv"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(Constants.IOS_WEB_APP_VERSION_HEADER, "1")
      setBody(Json.defaultJsonConverter.encodeToString(IdfvApiDto(idfv = idfv)))
    }

    assertThat(actual.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userService, times(1)) {
      updateTrackingData(
        USER_ID, TrackingData(idfv, TrackingDataType.IDFV, AppPlatform.IOS_WEB), appVersion = AppVersionDto(
          AppPlatform.IOS_WEB, 1
        )
      )
    }
  }


  @Test
  fun `SHOULD save device token ON deviceToken post call`() = withTestApplication(controller()) {
    val deviceToken = "deviceToken"

    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/web/users/${USER_ID}/deviceToken"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(Constants.IOS_WEB_APP_VERSION_HEADER, "1")
      setBody(Json.defaultJsonConverter.encodeToString(DeviceTokenApiDto(deviceToken)))
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userService) {
      updateDeviceToken(USER_ID, deviceToken, AppPlatform.IOS_WEB)
    }
  }

  @Test
  fun `SHOULD NOT save device token ON deviceToken post call WHEN token is empty`() = withTestApplication(controller()) {
    handleRequest(
      method = HttpMethod.Post,
      uri = "/web/users/${USER_ID}/deviceToken"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(Constants.IOS_WEB_APP_VERSION_HEADER, "1")
      setBody(Json.defaultJsonConverter.encodeToString(DeviceTokenApiDto("")))
    }
      .also { assertThat(it.response.status()).isEqualTo(HttpStatusCode.OK) }

    verifyBlocking(userService, never()) { updateDeviceToken(any(), any(), any()) }
  }

  @Test
  fun `SHOULD call updateAdjustId ON POST adjustId`() = withTestApplication(controller()) {
    val adjustId = "adjustId"

    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/web/users/${USER_ID}/adjustId"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(Constants.IOS_WEB_APP_VERSION_HEADER, "1")
      setBody(Json.defaultJsonConverter.encodeToString(AdjustIdDto(adjustId = adjustId)))
    }

    verifyBlocking(userService) { updateAdjustId(USER_ID, adjustId) }
    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
  }

  @Test
  fun `SHOULD save firebaseAppInstanceId ON firebaseAppInstanceId post call`() = withTestApplication(controller()) {
    val firebaseAppInstanceId = "firebaseAppInstanceId"

    val response = handleRequest(
      method = HttpMethod.Post,
      uri = "/web/users/${USER_ID}/firebaseAppInstanceId"
    ) {
      addHeader("Content-Type", "application/json")
      addHeader(Constants.IOS_WEB_APP_VERSION_HEADER, "1")
      setBody(Json.defaultJsonConverter.encodeToString(FirebaseAppInstanceIdDto(firebaseAppInstanceId)))
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    verifyBlocking(userService) {
      addOrUpdateFirebaseAppInstanceId(USER_ID, firebaseAppInstanceId, AppPlatform.IOS_WEB)
    }
  }

  @Test
  fun `SHOULD return user data ON get user data call`() = withTestApplication(controller()) {
    val user = user.copy(
      userId = USER_ID,
    )
    rewardingFacade.mock({ inflatingCoinsMultiplier(USER_ID) }, 2000)
    rewardingFacade.mock({ getUserCurrentCoinsBalance(USER_ID, AppPlatform.IOS_WEB) }, UserCurrentCoinsGoalBalance(314, 271, 602))
    amplitudeEventEffectHandler.mock({ shouldUseAmplitudeAnalytics(USER_ID) }, true)
    userService.mock({ loadCoinGoalUser(USER_ID) }, user.copy(userId = USER_ID))
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)
    cashoutService.mock(
      { getNonCashedOutUserCurrencyEarningsNoMoreThanThreshold(USER_ID) },
      UserCurrencyEarnings(BigDecimal("10.019"), Currency.getInstance("USD"), BigDecimal("10.019"))
    )
    cashoutPeriodsService.mock({ getCurrentCashoutPeriod(USER_ID) }, cashoutPeriod)
    cashoutStatusService.mock({ isCashoutEnabled(USER_ID) }, true)

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "/web/users/${USER_ID}"
    ) {
      addHeader(Constants.IOS_WEB_APP_VERSION_HEADER, "1")
    }

    assertThat(response.response.status()).isEqualTo(HttpStatusCode.OK)
    //language=json
    val expectedResponse = """
      {
        "id": "userId",
        "coinsBalance": 314,
        "coinGoal": 4000000,
        "cashoutAvailable": true,
        "cashoutAmount": "${'$'}10.01",
        "nextCashoutTimestamp": "2023-12-06T22:18:49.521946Z",
        "configuration": {
          "useAmplitudeAnalytics": true
        },
        "timestamp": "2023-12-06T10:18:49.521946Z"
      }
    """.trimIndent()
    assertJsonEquals(expectedResponse, response.response.content, JsonAssert.whenIgnoringPaths("timestamp", "nextCashoutTimestamp"))
  }

  @Test
  fun `SHOULD return online users WHEN online called`(): Unit = withTestApplication(controller()) {
    whenever(iosOnlineUsersService.getActiveUsers()) doReturn 1001

    val response = handleRequest(
      method = HttpMethod.Get,
      uri = "web/users/online"
    )
      .also {
        assertThat(it.response.status()).isEqualTo(HttpStatusCode.OK)
      }.response

    assertJsonEquals("""{"online":1001}""", response.content)
  }

}