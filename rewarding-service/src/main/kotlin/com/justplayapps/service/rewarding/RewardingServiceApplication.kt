package com.justplayapps.service.rewarding

import com.google.inject.Guice
import com.justplayapps.service.rewarding.service.RewardingServiceApiImpl
import com.moregames.base.app.BuildVariant
import com.moregames.base.app.ModulesContainer
import com.moregames.base.app.ModulesInitializer
import com.moregames.base.bus.MessageBusModule
import com.moregames.base.grpc.server.GrpcModule
import com.moregames.base.grpc.server.GrpcServerConfig
import com.moregames.base.lifecycle.ServiceManagerModule
import com.moregames.base.lifecycle.getServiceManager
import com.moregames.base.util.logger
import io.ktor.application.*
import io.ktor.features.*
import io.ktor.routing.*
import kotlinx.serialization.InternalSerializationApi

@InternalSerializationApi
fun Application.main() {
  val buildVariant = BuildVariant.byKey(System.getenv("NODE_ENV"))

  install(DefaultHeaders)
  install(IgnoreTrailingSlash)

  val rewardingServiceApplication = RewardingServiceApplication(ModulesContainer(this, buildVariant, playtimeModules))
  rewardingServiceApplication.onStart()

  environment.monitor.subscribe(ApplicationStopped) {
    rewardingServiceApplication.onShutdown()
  }
}

val playtimeModules: ModulesInitializer = {
  +CoreModule(application, buildVariant)
  +MessageBusModule {
    rootPackages("com.justplayapps.service.rewarding.buseffects")
  }
  +GrpcModule {
    externalServer(GrpcServerConfig.DEFAULT) {
      service<RewardingServiceApiImpl>()
    }
  }
//  +PubsubPushMessagesModule(
//    "subscribers",
//    AddRevenueCoinsEventPushSubscriber::class
//  )
  +ServiceManagerModule()
}

class RewardingServiceApplication(modulesContainer: ModulesContainer) {
  private val injector = Guice.createInjector(modulesContainer)

  fun onStart() {
    val buildVariant = injector.getInstance(BuildVariant::class.java)
    logger().info("Starting application with build variant $buildVariant")
    injector.getInstance(ApiManager::class.java).initApi()

    injector.getServiceManager().startAsync().awaitHealthy()
  }

  fun onShutdown() {
    injector.getServiceManager().stopAsync().awaitStopped()
  }
}