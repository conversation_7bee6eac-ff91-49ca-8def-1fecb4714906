package com.justplayapps.service.rewarding.dto

import com.moregames.base.util.alert
import com.moregames.base.util.logger
import java.math.BigDecimal

data class CountryTierSettings(
  val maxCashoutAmountMultiplier: BigDecimal,
  val dailyEarningsQuotas: List<BigDecimal>
) {
  init {
    if (dailyEarningsQuotas.size != 8) {
      logger().alert("wrong settings of dailyEarningsQuotas detected")
      throw IllegalStateException("wrong settings of dailyEarningsQuotas detected")
    }
  }
}