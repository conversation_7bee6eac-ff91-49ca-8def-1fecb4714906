package com.justplayapps.service.rewarding.utils

import com.moregames.base.bigquery.BqEvent
import com.moregames.base.messaging.AppNotification
import com.moregames.base.messaging.dto.MessageDto
import com.moregames.base.messaging.dto.TaskDto
import com.moregames.base.util.*
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.json.Json
import kotlinx.serialization.modules.SerializersModule
import kotlinx.serialization.modules.contextual
import java.math.BigDecimal
import java.math.RoundingMode

@OptIn(ExperimentalSerializationApi::class)
val defaultJsonConverter by lazy {
  val provider = serializationReflectionProvider("com.justplayapps.service.rewarding", "com.moregames.base")

  Json {
    ignoreUnknownKeys = true
    encodeDefaults = true
    explicitNulls = false
    serializersModule = SerializersModule {
      contextual(InstantSerializer)
      contextual(BigDecimalSerializer)
      contextual(LocalDateSerializer)
      contextual(LocaleSerializer)
      with(provider) {
        registerSubclasses<MessageDto>()
        registerSubclasses<TaskDto>()
        registerSubclasses<AppNotification>()
        registerSubclasses<BqEvent>()
      }
    }
  }
}

fun BigDecimal.roundDownToSecondDigit(): BigDecimal = this.setScale(2, RoundingMode.DOWN)
fun BigDecimal.takePercentage(percent: Int): BigDecimal = this.multiply(BigDecimal(percent)).scaleByPowerOfTen(-2).roundDownToSecondDigit()
fun BigDecimal.increaseByPercentage(percent: Int): BigDecimal = takePercentage(100 + percent)
